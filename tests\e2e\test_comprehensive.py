"""
Comprehensive End-to-End Testing Suite
Task 40: Final Integration Testing

PATTERN: Complete system validation with performance benchmarking
Features:
- Full workflow testing
- Animation performance validation
- Data persistence verification
- Cross-browser compatibility
- Load testing and performance benchmarking
- Security vulnerability testing
"""

import asyncio
import time
import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import statistics

import pytest
import pytest_asyncio
from playwright.async_api import async_playwright, <PERSON>, <PERSON><PERSON><PERSON>, BrowserContext
from httpx import AsyncClient
import websockets
from concurrent.futures import ThreadPoolExecutor, as_completed

from backend.app.main import app
from backend.app.config.settings import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()

class E2ETestSuite:
    """Comprehensive end-to-end testing suite"""
    
    def __init__(self):
        self.browser: Browser = None
        self.context: BrowserContext = None
        self.page: Page = None
        self.api_client: AsyncClient = None
        self.test_results: Dict[str, Any] = {}
        self.performance_metrics: Dict[str, List[float]] = {}
    
    async def setup(self):
        """Setup test environment"""
        # Setup Playwright
        self.playwright = await async_playwright().start()
        self.browser = await self.playwright.chromium.launch(
            headless=False,  # Set to True for CI/CD
            args=[
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--enable-gpu-benchmarking'
            ]
        )
        
        self.context = await self.browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent='E2E-Test-Suite/1.0'
        )
        
        self.page = await self.context.new_page()
        
        # Setup API client
        self.api_client = AsyncClient(app=app, base_url="http://testserver")
        
        # Enable performance monitoring
        await self.page.add_init_script("""
            window.performanceMetrics = [];
            window.animationMetrics = [];
            
            const originalRequestAnimationFrame = window.requestAnimationFrame;
            window.requestAnimationFrame = function(callback) {
                const start = performance.now();
                return originalRequestAnimationFrame(function() {
                    const end = performance.now();
                    window.animationMetrics.push(end - start);
                    callback();
                });
            };
        """)
        
        logger.info("E2E test setup completed")
    
    async def teardown(self):
        """Cleanup test environment"""
        if self.api_client:
            await self.api_client.aclose()
        if self.context:
            await self.context.close()
        if self.browser:
            await self.browser.close()
        if hasattr(self, 'playwright'):
            await self.playwright.stop()
        
        logger.info("E2E test teardown completed")

    async def test_complete_user_workflow(self) -> Dict[str, Any]:
        """Test complete user workflow from input to result"""
        test_start = time.time()
        workflow_results = {
            "test_name": "complete_user_workflow",
            "start_time": datetime.utcnow().isoformat(),
            "steps": [],
            "performance_metrics": {},
            "success": False,
            "errors": []
        }
        
        try:
            # Step 1: Load application
            step_start = time.time()
            await self.page.goto("http://localhost:3000")
            await self.page.wait_for_load_state("networkidle")
            
            workflow_results["steps"].append({
                "step": "page_load",
                "duration": time.time() - step_start,
                "success": True
            })
            
            # Step 2: Wait for application initialization
            step_start = time.time()
            await self.page.wait_for_selector('[data-testid="hero-input-bar"]', timeout=10000)
            await self.page.wait_for_function("window.APP_INITIALIZED === true", timeout=15000)
            
            workflow_results["steps"].append({
                "step": "app_initialization",
                "duration": time.time() - step_start,
                "success": True
            })
            
            # Step 3: Test task creation
            await self._test_task_creation_workflow(workflow_results)
            
            # Step 4: Test calendar event creation
            await self._test_calendar_event_workflow(workflow_results)
            
            # Step 5: Test AI question workflow
            await self._test_ai_question_workflow(workflow_results)
            
            # Step 6: Test data persistence
            await self._test_data_persistence(workflow_results)
            
            # Step 7: Test real-time updates
            await self._test_realtime_updates(workflow_results)
            
            workflow_results["success"] = True
            workflow_results["total_duration"] = time.time() - test_start
            
        except Exception as e:
            workflow_results["errors"].append(str(e))
            logger.error(f"Workflow test failed: {e}")
        
        workflow_results["end_time"] = datetime.utcnow().isoformat()
        return workflow_results
    
    async def _test_task_creation_workflow(self, results: Dict[str, Any]):
        """Test task creation workflow with animation validation"""
        step_start = time.time()
        
        # Input task
        await self.page.fill('[data-testid="hero-input-bar"]', 
                           "Finish the AI dashboard documentation by Friday")
        
        # Wait for processing animation
        await self.page.click('[data-testid="submit-button"]')
        
        # Validate processing animations
        await self.page.wait_for_selector('[data-testid="processing-animation"]')
        await self.page.wait_for_selector('[data-testid="category-decision"]')
        
        # Wait for task creation
        await self.page.wait_for_selector('[data-testid="task-created"]', timeout=10000)
        
        # Validate task appears in task list
        await self.page.click('[data-testid="tasks-tab"]')
        task_element = await self.page.wait_for_selector(
            '[data-testid="task-item"]:has-text("AI dashboard documentation")'
        )
        
        assert task_element is not None, "Task not found in task list"
        
        results["steps"].append({
            "step": "task_creation",
            "duration": time.time() - step_start,
            "success": True
        })
    
    async def _test_calendar_event_workflow(self, results: Dict[str, Any]):
        """Test calendar event creation workflow"""
        step_start = time.time()
        
        # Clear input and add event
        await self.page.fill('[data-testid="hero-input-bar"]', '')
        await self.page.fill('[data-testid="hero-input-bar"]', 
                           "Schedule team meeting tomorrow at 2 PM for project review")
        
        await self.page.click('[data-testid="submit-button"]')
        
        # Wait for processing and categorization
        await self.page.wait_for_selector('[data-testid="category-decision"]')
        await self.page.wait_for_selector('[data-testid="event-created"]', timeout=10000)
        
        # Validate event in calendar
        await self.page.click('[data-testid="calendar-tab"]')
        event_element = await self.page.wait_for_selector(
            '[data-testid="calendar-event"]:has-text("team meeting")'
        )
        
        assert event_element is not None, "Event not found in calendar"
        
        results["steps"].append({
            "step": "calendar_event_creation",
            "duration": time.time() - step_start,
            "success": True
        })
    
    async def _test_ai_question_workflow(self, results: Dict[str, Any]):
        """Test AI question answering workflow"""
        step_start = time.time()
        
        # Ask AI question
        await self.page.fill('[data-testid="hero-input-bar"]', '')
        await self.page.fill('[data-testid="hero-input-bar"]', 
                           "What are the best practices for React performance optimization?")
        
        await self.page.click('[data-testid="submit-button"]')
        
        # Wait for AI processing and response
        await self.page.wait_for_selector('[data-testid="ai-processing"]')
        await self.page.wait_for_selector('[data-testid="ai-response"]', timeout=15000)
        
        # Validate response content
        response_element = await self.page.query_selector('[data-testid="ai-response"]')
        response_text = await response_element.inner_text()
        
        assert len(response_text) > 100, "AI response too short"
        assert "React" in response_text, "Response doesn't mention React"
        
        results["steps"].append({
            "step": "ai_question_workflow",
            "duration": time.time() - step_start,
            "success": True
        })
    
    async def _test_data_persistence(self, results: Dict[str, Any]):
        """Test data persistence across page reloads"""
        step_start = time.time()
        
        # Get current data count
        await self.page.click('[data-testid="tasks-tab"]')
        tasks_before = await self.page.query_selector_all('[data-testid="task-item"]')
        
        await self.page.click('[data-testid="calendar-tab"]')
        events_before = await self.page.query_selector_all('[data-testid="calendar-event"]')
        
        # Reload page
        await self.page.reload()
        await self.page.wait_for_load_state("networkidle")
        await self.page.wait_for_function("window.APP_INITIALIZED === true")
        
        # Verify data persistence
        await self.page.click('[data-testid="tasks-tab"]')
        tasks_after = await self.page.query_selector_all('[data-testid="task-item"]')
        
        await self.page.click('[data-testid="calendar-tab"]')
        events_after = await self.page.query_selector_all('[data-testid="calendar-event"]')
        
        assert len(tasks_after) == len(tasks_before), "Tasks not persisted after reload"
        assert len(events_after) == len(events_before), "Events not persisted after reload"
        
        results["steps"].append({
            "step": "data_persistence",
            "duration": time.time() - step_start,
            "success": True
        })
    
    async def _test_realtime_updates(self, results: Dict[str, Any]):
        """Test real-time WebSocket updates"""
        step_start = time.time()
        
        # Connect to WebSocket endpoint
        try:
            async with websockets.connect("ws://localhost:8000/ws") as websocket:
                # Send test message
                await websocket.send(json.dumps({
                    "type": "subscribe",
                    "channel": "task_updates"
                }))
                
                # Wait for confirmation
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                response_data = json.loads(response)
                
                assert response_data.get("type") == "subscription_confirmed"
                
                results["steps"].append({
                    "step": "websocket_connection",
                    "duration": time.time() - step_start,
                    "success": True
                })
        
        except Exception as e:
            results["steps"].append({
                "step": "websocket_connection",
                "duration": time.time() - step_start,
                "success": False,
                "error": str(e)
            })
    
    async def test_animation_performance(self) -> Dict[str, Any]:
        """Test animation performance and smoothness"""
        results = {
            "test_name": "animation_performance",
            "start_time": datetime.utcnow().isoformat(),
            "metrics": {},
            "success": False
        }
        
        try:
            # Navigate to app
            await self.page.goto("http://localhost:3000")
            await self.page.wait_for_load_state("networkidle")
            
            # Trigger multiple animations
            test_inputs = [
                "Create a task for tomorrow",
                "Schedule meeting next week",
                "What is machine learning?",
                "Add reminder for dentist appointment",
                "Research React best practices"
            ]
            
            animation_times = []
            
            for input_text in test_inputs:
                start_time = time.time()
                
                # Clear and fill input
                await self.page.fill('[data-testid="hero-input-bar"]', input_text)
                await self.page.click('[data-testid="submit-button"]')
                
                # Wait for animation to complete
                await self.page.wait_for_selector('[data-testid="processing-complete"]', timeout=10000)
                
                # Measure animation performance
                animation_metrics = await self.page.evaluate("window.animationMetrics")
                if animation_metrics:
                    animation_times.extend(animation_metrics)
                
                # Clear animation metrics for next test
                await self.page.evaluate("window.animationMetrics = []")
                
                # Small delay between tests
                await asyncio.sleep(1)
            
            # Calculate performance metrics
            if animation_times:
                results["metrics"] = {
                    "average_frame_time": statistics.mean(animation_times),
                    "max_frame_time": max(animation_times),
                    "min_frame_time": min(animation_times),
                    "fps_average": 1000 / statistics.mean(animation_times),
                    "dropped_frames": len([t for t in animation_times if t > 16.67]),  # 60fps = 16.67ms
                    "total_animations": len(animation_times)
                }
                
                # Check if performance is acceptable (>45 FPS average)
                results["success"] = results["metrics"]["fps_average"] > 45
            
        except Exception as e:
            results["error"] = str(e)
            logger.error(f"Animation performance test failed: {e}")
        
        results["end_time"] = datetime.utcnow().isoformat()
        return results
    
    async def test_load_performance(self) -> Dict[str, Any]:
        """Test application performance under load"""
        results = {
            "test_name": "load_performance",
            "start_time": datetime.utcnow().isoformat(),
            "concurrent_users": 10,
            "requests_per_user": 50,
            "success": False,
            "metrics": {}
        }
        
        try:
            # Concurrent user simulation
            async def simulate_user_session():
                async with AsyncClient(app=app, base_url="http://testserver") as client:
                    session_times = []
                    
                    for i in range(results["requests_per_user"]):
                        start_time = time.time()
                        
                        # Random API calls
                        test_requests = [
                            ("POST", "/api/process", {"input": f"Test task {i}", "mode": "auto"}),
                            ("GET", "/api/tasks", {}),
                            ("GET", "/api/calendar/events", {}),
                            ("GET", "/health", {})
                        ]
                        
                        for method, endpoint, data in test_requests:
                            if method == "POST":
                                response = await client.post(endpoint, json=data)
                            else:
                                response = await client.get(endpoint)
                            
                            assert response.status_code in [200, 201, 202]
                        
                        session_times.append(time.time() - start_time)
                        await asyncio.sleep(0.1)  # Small delay between requests
                    
                    return session_times
            
            # Run concurrent sessions
            tasks = [simulate_user_session() for _ in range(results["concurrent_users"])]
            session_results = await asyncio.gather(*tasks)
            
            # Aggregate results
            all_times = []
            for session_times in session_results:
                all_times.extend(session_times)
            
            results["metrics"] = {
                "total_requests": len(all_times),
                "average_response_time": statistics.mean(all_times),
                "max_response_time": max(all_times),
                "min_response_time": min(all_times),
                "p95_response_time": statistics.quantiles(all_times, n=20)[18],  # 95th percentile
                "requests_per_second": len(all_times) / sum(all_times),
                "failed_requests": 0  # Would track actual failures
            }
            
            # Success criteria: avg response < 2s, p95 < 5s
            results["success"] = (
                results["metrics"]["average_response_time"] < 2.0 and
                results["metrics"]["p95_response_time"] < 5.0
            )
            
        except Exception as e:
            results["error"] = str(e)
            logger.error(f"Load performance test failed: {e}")
        
        results["end_time"] = datetime.utcnow().isoformat()
        return results
    
    async def test_cross_browser_compatibility(self) -> Dict[str, Any]:
        """Test application compatibility across different browsers"""
        results = {
            "test_name": "cross_browser_compatibility",
            "start_time": datetime.utcnow().isoformat(),
            "browsers": [],
            "success": False
        }
        
        browser_configs = [
            {"name": "chromium", "viewport": {"width": 1920, "height": 1080}},
            {"name": "firefox", "viewport": {"width": 1920, "height": 1080}},
            {"name": "webkit", "viewport": {"width": 1366, "height": 768}}  # Safari
        ]
        
        successful_browsers = 0
        
        for config in browser_configs:
            browser_result = {
                "name": config["name"],
                "success": False,
                "errors": []
            }
            
            try:
                # Launch browser
                if config["name"] == "chromium":
                    browser = await self.playwright.chromium.launch(headless=True)
                elif config["name"] == "firefox":
                    browser = await self.playwright.firefox.launch(headless=True)
                else:  # webkit
                    browser = await self.playwright.webkit.launch(headless=True)
                
                context = await browser.new_context(viewport=config["viewport"])
                page = await context.new_page()
                
                # Basic functionality test
                await page.goto("http://localhost:3000")
                await page.wait_for_load_state("networkidle", timeout=30000)
                
                # Test input functionality
                await page.fill('[data-testid="hero-input-bar"]', "Test input for browser compatibility")
                await page.click('[data-testid="submit-button"]')
                
                # Wait for processing
                await page.wait_for_selector('[data-testid="processing-animation"]', timeout=10000)
                
                browser_result["success"] = True
                successful_browsers += 1
                
                await context.close()
                await browser.close()
                
            except Exception as e:
                browser_result["errors"].append(str(e))
                logger.error(f"Browser {config['name']} test failed: {e}")
            
            results["browsers"].append(browser_result)
        
        results["success"] = successful_browsers >= 2  # At least 2 browsers should work
        results["end_time"] = datetime.utcnow().isoformat()
        
        return results
    
    async def run_comprehensive_test_suite(self) -> Dict[str, Any]:
        """Run the complete test suite"""
        suite_start = time.time()
        
        suite_results = {
            "suite_name": "comprehensive_e2e_testing",
            "start_time": datetime.utcnow().isoformat(),
            "tests": [],
            "overall_success": False,
            "summary": {}
        }
        
        # Run all tests
        test_methods = [
            self.test_complete_user_workflow,
            self.test_animation_performance,
            self.test_load_performance,
            self.test_cross_browser_compatibility
        ]
        
        successful_tests = 0
        
        for test_method in test_methods:
            try:
                logger.info(f"Running {test_method.__name__}...")
                test_result = await test_method()
                suite_results["tests"].append(test_result)
                
                if test_result.get("success", False):
                    successful_tests += 1
                    
            except Exception as e:
                logger.error(f"Test {test_method.__name__} failed with exception: {e}")
                suite_results["tests"].append({
                    "test_name": test_method.__name__,
                    "success": False,
                    "error": str(e)
                })
        
        # Calculate summary
        suite_results["summary"] = {
            "total_tests": len(test_methods),
            "successful_tests": successful_tests,
            "failed_tests": len(test_methods) - successful_tests,
            "success_rate": (successful_tests / len(test_methods)) * 100,
            "total_duration": time.time() - suite_start
        }
        
        suite_results["overall_success"] = successful_tests >= (len(test_methods) * 0.8)  # 80% success rate
        suite_results["end_time"] = datetime.utcnow().isoformat()
        
        return suite_results

# Test runner
async def main():
    """Main test runner function"""
    suite = E2ETestSuite()
    
    try:
        await suite.setup()
        results = await suite.run_comprehensive_test_suite()
        
        # Save results
        timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
        results_file = f"test_results_{timestamp}.json"
        
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        logger.info(f"Test results saved to {results_file}")
        logger.info(f"Overall success: {results['overall_success']}")
        logger.info(f"Success rate: {results['summary']['success_rate']:.1f}%")
        
        return results
        
    finally:
        await suite.teardown()

if __name__ == "__main__":
    asyncio.run(main())
