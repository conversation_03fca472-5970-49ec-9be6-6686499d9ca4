Title: Output Parsers | Mirascope

URL Source: https://mirascope.com/docs/mirascope/learn/output_parsers

Markdown Content:
Output Parsers | Mirascope

===============

[![Image 1: Mirascope Frog Logo](https://mirascope.com/assets/branding/mirascope-logo.svg) Mirascope](https://mirascope.com/)

*   [Docs](https://mirascope.com/docs/mirascope)

[Blog](https://mirascope.com/blog)[Pricing](https://mirascope.com/pricing)

⌘K

Type to search

⌘K to search

Esc to close

[mirascope v1.25.4 1.2k](https://github.com/Mirascope/mirascope)

Mirascope[Lilypad](https://mirascope.com/docs/lilypad)

[Docs](https://mirascope.com/docs/mirascope)[Guides](https://mirascope.com/docs/mirascope/guides)[API Reference](https://mirascope.com/docs/mirascope/api)[LLMs Text](https://mirascope.com/docs/mirascope/llms-full)

[Welcome](https://mirascope.com/docs/mirascope)

Getting Started

[Why Mirascope?](https://mirascope.com/docs/mirascope/getting-started/why)[Help](https://mirascope.com/docs/mirascope/getting-started/help)[Contributing](https://mirascope.com/docs/mirascope/getting-started/contributing)[0.x Migration Guide](https://mirascope.com/docs/mirascope/getting-started/migration)

Learn

[Overview](https://mirascope.com/docs/mirascope/learn)[Prompts](https://mirascope.com/docs/mirascope/learn/prompts)[Calls](https://mirascope.com/docs/mirascope/learn/calls)[Streams](https://mirascope.com/docs/mirascope/learn/streams)[Chaining](https://mirascope.com/docs/mirascope/learn/chaining)[Response Models](https://mirascope.com/docs/mirascope/learn/response_models)[JSON Mode](https://mirascope.com/docs/mirascope/learn/json_mode)[Output Parsers](https://mirascope.com/docs/mirascope/learn/output_parsers)[Tools](https://mirascope.com/docs/mirascope/learn/tools)[Agents](https://mirascope.com/docs/mirascope/learn/agents)[Evals](https://mirascope.com/docs/mirascope/learn/evals)[Async](https://mirascope.com/docs/mirascope/learn/async)[Retries](https://mirascope.com/docs/mirascope/learn/retries)[Local Models](https://mirascope.com/docs/mirascope/learn/local_models)

Provider-Specific Features

[Thinking & Reasoning](https://mirascope.com/docs/mirascope/learn/provider-specific/thinking-and-reasoning)[OpenAI](https://mirascope.com/docs/mirascope/learn/provider-specific/openai)[Anthropic](https://mirascope.com/docs/mirascope/learn/provider-specific/anthropic)

Extensions

[Middleware](https://mirascope.com/docs/mirascope/learn/extensions/middleware)[Custom LLM Provider](https://mirascope.com/docs/mirascope/learn/extensions/custom_provider)

MCP - Model Context Protocol

[Client](https://mirascope.com/docs/mirascope/learn/mcp/client)

Output Parsers[](https://mirascope.com/docs/mirascope/learn/output_parsers#output-parsers)
==========================================================================================

If you haven't already, we recommend first reading the section on [Calls](https://mirascope.com/docs/mirascope/learn/calls)

Output Parsers in Mirascope provide a flexible way to process and structure the raw output from Large Language Models (LLMs). They allow you to transform the LLM's response into a more usable format, enabling easier integration with your application logic and improving the overall reliability of your LLM-powered features.

Basic Usage and Syntax[](https://mirascope.com/docs/mirascope/learn/output_parsers#basic-usage-and-syntax)
----------------------------------------------------------------------------------------------------------

API Documentation

Output Parsers are functions that take the call response object as input and return an output of a specified type. When you supply an output parser to a `call` decorator, it modifies the return type of the decorated function to match the output type of the parser.

Let's take a look at a basic example:

Shorthand Template

```
from mirascope import llm

def parse_recommendation(response: llm.CallResponse) -> tuple[str, str]:
    title, author = response.content.split(" by ")
    return (title, author)

@llm.call(provider="openai", model="gpt-4o-mini", output_parser=parse_recommendation) 
def recommend_book(genre: str) -> str:
    return f"Recommend a {genre} book. Output only Title by Author"

print(recommend_book("fantasy"))
# Output: ('"The Name of the Wind"', 'Patrick Rothfuss')
```

Additional Examples[](https://mirascope.com/docs/mirascope/learn/output_parsers#additional-examples)
----------------------------------------------------------------------------------------------------

There are many different ways to structure and parse LLM outputs, ranging from XML parsing to using regular expressions.

Here are a few examples:

Regular Expression XML JSON Mode

```
import re

from mirascope import llm, prompt_template

def parse_cot(response: llm.CallResponse) -> str:
    pattern = r"<thinking>.*?</thinking>.*?<o>(.*?)</o>"
    match = re.search(pattern, response.content, re.DOTALL)
    if not match:
        return response.content
    return match.group(1).strip()

@llm.call(provider="openai", model="gpt-4o-mini", output_parser=parse_cot) 
@prompt_template(
    """
    First, output your thought process in <thinking> tags. # [!code highlight]
    Then, provide your final output in <o> tags. # [!code highlight]

    Question: {question}
    """
)
def chain_of_thought(question: str): ...

question = "Roger has 5 tennis balls. He buys 2 cans of 3. How many does he have now?"
output = chain_of_thought(question)
print(output)
```

Next Steps[](https://mirascope.com/docs/mirascope/learn/output_parsers#next-steps)
----------------------------------------------------------------------------------

By leveraging Output Parsers effectively, you can create more robust and reliable LLM-powered applications, ensuring that the raw model outputs are transformed into structured data that's easy to work with in your application logic.

Next, we recommend taking a look at the section on [Tools](https://mirascope.com/docs/mirascope/learn/tools) to learn how to extend the capabilities of LLMs with custom functions.

Copy as Markdown

#### Provider

OpenAI

#### On this page

Output Parsers Basic Usage and Syntax Additional Examples Next Steps

Copy as Markdown

#### Provider

OpenAI

#### On this page

Output Parsers Basic Usage and Syntax Additional Examples Next Steps

© 2025 Mirascope. All rights reserved.

[Privacy Policy](https://mirascope.com/privacy)[Terms of Use](https://mirascope.com/terms/use)

Cookie Consent
--------------

We use cookies to track usage and improve the site.

Reject Accept
