"""
Test tool integration for AI-Powered Dashboard.

This test verifies that all tools can be imported and instantiated correctly
with the proper environment configuration.
"""

import sys
import os
from pathlib import Path
import pytest

# Add the backend directory to Python path
backend_path = Path(__file__).parent.parent / "backend"
sys.path.insert(0, str(backend_path))

def test_environment_variables():
    """Test that required environment variables are available."""
    # Check if .env file exists in project root
    env_file = Path(__file__).parent.parent / ".env"
    assert env_file.exists(), f".env file not found at {env_file}"
    
    # Load environment variables manually for testing
    import dotenv
    dotenv.load_dotenv(env_file)
    
    # Check required API keys
    assert os.getenv("OPENROUTER_API_KEY"), "OPENROUTER_API_KEY not found in environment"
    assert os.getenv("LANGSEARCH_API_KEY"), "LANGSEARCH_API_KEY not found in environment"
    
    print("✅ Environment variables loaded successfully!")
    print(f"OpenRouter API Key: {os.getenv('OPENROUTER_API_KEY')[:10]}...")
    print(f"LangSearch API Key: {os.getenv('LANGSEARCH_API_KEY')[:10]}...")


def test_settings_import():
    """Test that settings can be imported and initialized."""
    try:
        from app.config.settings import get_settings
        settings = get_settings()
        
        assert settings.openrouter_api_key, "OpenRouter API key not loaded"
        assert settings.langsearch_api_key, "LangSearch API key not loaded"
        
        print("✅ Settings imported and configured successfully!")
        print(f"Environment: {settings.environment}")
        print(f"Debug: {settings.debug}")
        print(f"API Port: {settings.api_port}")
        
        return settings
    except Exception as e:
        pytest.fail(f"Failed to import settings: {e}")


def test_tool_imports():
    """Test that all tools can be imported successfully."""
    try:
        from app.tools.task_tool import TaskTool, CreateTaskInput
        from app.tools.calendar_tool import CalendarTool, CreateEventInput
        from app.tools.web_search_tool import WebSearchTool, WebSearchInput
        from app.tools.database_search_tool import DatabaseSearchTool, HybridSearchInput
        
        print("✅ All tool classes imported successfully!")
        print(f"Task Tool: {TaskTool.__name__}")
        print(f"Calendar Tool: {CalendarTool.__name__}")
        print(f"Web Search Tool: {WebSearchTool.__name__}")
        print(f"Database Search Tool: {DatabaseSearchTool.__name__}")
        
        return {
            'TaskTool': TaskTool,
            'CalendarTool': CalendarTool,
            'WebSearchTool': WebSearchTool,
            'DatabaseSearchTool': DatabaseSearchTool,
            'CreateTaskInput': CreateTaskInput,
            'CreateEventInput': CreateEventInput,
            'WebSearchInput': WebSearchInput,
            'HybridSearchInput': HybridSearchInput
        }
    except Exception as e:
        pytest.fail(f"Failed to import tools: {e}")


def test_tool_instantiation():
    """Test that all tools can be instantiated."""
    try:
        # Import tools first
        tools = test_tool_imports()
        
        # Instantiate tools
        task_tool = tools['TaskTool']()
        calendar_tool = tools['CalendarTool']()
        web_search_tool = tools['WebSearchTool']()
        database_search_tool = tools['DatabaseSearchTool']()
        
        print("✅ All tools instantiated successfully!")
        print(f"Task Tool Instance: {type(task_tool).__name__}")
        print(f"Calendar Tool Instance: {type(calendar_tool).__name__}")
        print(f"Web Search Tool Instance: {type(web_search_tool).__name__}")
        print(f"Database Search Tool Instance: {type(database_search_tool).__name__}")
        
        return {
            'task_tool': task_tool,
            'calendar_tool': calendar_tool,
            'web_search_tool': web_search_tool,
            'database_search_tool': database_search_tool
        }
    except Exception as e:
        pytest.fail(f"Failed to instantiate tools: {e}")


def test_main_import():
    """Test that main.py can be imported with all tools."""
    try:
        # This should work now that we've fixed the .env path
        from app.main import task_tool, calendar_tool, web_search_tool, database_search_tool
        
        print("✅ Main module imported successfully with all tools!")
        print(f"Task Tool from main: {type(task_tool).__name__}")
        print(f"Calendar Tool from main: {type(calendar_tool).__name__}")
        print(f"Web Search Tool from main: {type(web_search_tool).__name__}")
        print(f"Database Search Tool from main: {type(database_search_tool).__name__}")
        
        return {
            'task_tool': task_tool,
            'calendar_tool': calendar_tool,
            'web_search_tool': web_search_tool,
            'database_search_tool': database_search_tool
        }
    except Exception as e:
        pytest.fail(f"Failed to import main module: {e}")


def test_input_models():
    """Test that input models can be created with valid data."""
    try:
        tools = test_tool_imports()
        
        # Test TaskInput
        task_input = tools['CreateTaskInput'](
            title="Test Task",
            description="This is a test task"
        )
        print(f"✅ CreateTaskInput created: {task_input.title}")
        
        # Test EventInput
        event_input = tools['CreateEventInput'](
            title="Test Event",
            description="This is a test event",
            suggested_date="today"
        )
        print(f"✅ CreateEventInput created: {event_input.title}")
        
        # Test WebSearchInput
        web_input = tools['WebSearchInput'](
            query="test search",
            freshness="oneWeek",
            max_results=5
        )
        print(f"✅ WebSearchInput created: {web_input.query}")
        
        # Test HybridSearchInput
        search_input = tools['HybridSearchInput'](
            query="test database search",
            search_type="hybrid",
            limit=10
        )
        print(f"✅ HybridSearchInput created: {search_input.query}")
        
        return True
    except Exception as e:
        pytest.fail(f"Failed to create input models: {e}")


if __name__ == "__main__":
    """Run tests directly for debugging."""
    print("🚀 Starting Tool Integration Tests...")
    print("=" * 50)
    
    try:
        print("\n1. Testing Environment Variables...")
        test_environment_variables()
        
        print("\n2. Testing Settings Import...")
        test_settings_import()
        
        print("\n3. Testing Tool Imports...")
        test_tool_imports()
        
        print("\n4. Testing Tool Instantiation...")
        test_tool_instantiation()
        
        print("\n5. Testing Input Models...")
        test_input_models()
        
        print("\n6. Testing Main Module Import...")
        test_main_import()
        
        print("\n" + "=" * 50)
        print("🎉 ALL TESTS PASSED! Tool integration is working correctly!")
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {e}")
        print("\n" + "=" * 50)
        print("💡 Check the error above and fix the configuration issues.")
        sys.exit(1)
