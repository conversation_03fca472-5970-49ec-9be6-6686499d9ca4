{"permissions": {"allow": ["Bash(grep:*)", "Bash(ls:*)", "<PERSON><PERSON>(source:*)", "Bash(find:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(tree:*)", "Bash(ruff:*)", "<PERSON><PERSON>(touch:*)", "<PERSON><PERSON>(cat:*)", "Bash(ruff check:*)", "Bash(pytest:*)", "<PERSON><PERSON>(python:*)", "Bash(python -m pytest:*)", "Bash(python3 -m pytest:*)", "WebFetch(domain:docs.anthropic.com)", "WebFetch(domain:jina.ai)", "WebFetch(domain:seogrove.ai)", "Bash(php:*)", "WebFetch(domain:ai.pydantic.dev)", "WebFetch(domain:platform.openai.com)", "<PERSON>tch", "Bash(cp:*)", "<PERSON><PERSON>(chmod:*)", "Bash(git add:*)", "Bash(git reset:*)", "Bash(git restore:*)", "Bash(./quick-setup.sh:*)", "<PERSON><PERSON>(docker-compose up:*)", "Bash(rm:*)"], "deny": []}}