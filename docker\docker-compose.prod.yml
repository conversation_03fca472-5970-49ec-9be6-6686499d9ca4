# Production Docker Compose Configuration
# PATTERN: Multi-service orchestration with production optimizations

version: '3.8'

services:
  # ===== NGINX REVERSE PROXY =====
  nginx:
    image: nginx:alpine
    container_name: ai-dashboard-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.prod.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
      - static_files:/usr/share/nginx/html/static:ro
    depends_on:
      - backend
    networks:
      - ai-dashboard-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.25'

  # ===== APPLICATION BACKEND =====
  backend:
    build:
      context: ..
      dockerfile: docker/Dockerfile.prod
    container_name: ai-dashboard-backend
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=postgresql+asyncpg://postgres:${POSTGRES_PASSWORD}@postgres:5432/ai_dashboard
      - REDIS_URL=redis://redis:6379/0
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - SECRET_KEY=${SECRET_KEY}
      - CORS_ORIGINS=["https://yourdomain.com","https://www.yourdomain.com"]
      - API_RATE_LIMIT_PER_MINUTE=120
      - EMBEDDING_RATE_LIMIT_PER_MINUTE=30
      - WEB_SEARCH_RATE_LIMIT_PER_MINUTE=60
      - LOG_LEVEL=INFO
      - SECURITY_HEADERS_ENABLED=true
    volumes:
      - app_data:/app/data
      - app_logs:/app/logs
      - static_files:/app/static
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - ai-dashboard-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '2.0'
        reservations:
          memory: 1G
          cpus: '1.0'
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "3"

  # ===== DATABASE =====
  postgres:
    image: postgres:15-alpine
    container_name: ai-dashboard-postgres
    environment:
      - POSTGRES_DB=ai_dashboard
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=en_US.UTF-8 --lc-ctype=en_US.UTF-8
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./postgres/init:/docker-entrypoint-initdb.d:ro
      - ./logs/postgres:/var/log/postgresql
    networks:
      - ai-dashboard-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d ai_dashboard"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "3"

  # ===== REDIS CACHE =====
  redis:
    image: redis:7-alpine
    container_name: ai-dashboard-redis
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
      - ./redis/redis.prod.conf:/usr/local/etc/redis/redis.conf:ro
    networks:
      - ai-dashboard-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
      start_period: 10s
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  # ===== MONITORING =====
  prometheus:
    image: prom/prometheus:latest
    container_name: ai-dashboard-prometheus
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/usr/share/prometheus/console_libraries'
      - '--web.console.templates=/usr/share/prometheus/consoles'
      - '--web.enable-lifecycle'
      - '--storage.tsdb.retention.time=30d'
    ports:
      - "9090:9090"
    networks:
      - ai-dashboard-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'

  grafana:
    image: grafana/grafana:latest
    container_name: ai-dashboard-grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD}
      - GF_SECURITY_ADMIN_USER=admin
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana:/etc/grafana/provisioning:ro
    ports:
      - "3000:3000"
    depends_on:
      - prometheus
    networks:
      - ai-dashboard-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'

# ===== NETWORKS =====
networks:
  ai-dashboard-network:
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: **********/16

# ===== VOLUMES =====
volumes:
  postgres_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/postgres
  
  redis_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/redis
  
  app_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/app
  
  app_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./logs/app
  
  static_files:
    driver: local
  
  prometheus_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/prometheus
  
  grafana_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/grafana
