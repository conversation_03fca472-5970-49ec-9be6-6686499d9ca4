#!/bin/bash

# AI-Powered Dashboard Development Setup Script
# Run this script to set up the complete development environment

set -e

echo "🚀 Setting up AI-Powered Dashboard Development Environment..."

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check prerequisites
check_prerequisites() {
    print_info "Checking prerequisites..."
    
    # Check Docker
    if command -v docker &> /dev/null; then
        print_success "Docker is installed"
    else
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check Docker Compose
    if command -v docker-compose &> /dev/null; then
        print_success "Docker Compose is installed"
    else
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check Node.js
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        print_success "Node.js is installed: $NODE_VERSION"
    else
        print_warning "Node.js is not installed. Installing via Docker only."
    fi
    
    # Check Python
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 --version)
        print_success "Python is installed: $PYTHON_VERSION"
    else
        print_warning "Python is not installed. Using Docker only."
    fi
}

# Setup environment file
setup_environment() {
    print_info "Setting up environment configuration..."
    
    if [ ! -f .env ]; then
        if [ -f .env.example ]; then
            cp .env.example .env
            print_success "Created .env from .env.example"
            print_warning "Please edit .env file with your API keys before continuing"
            
            # Check if API keys are set
            if grep -q "your-openrouter-key-here" .env; then
                print_error "Please set your OPENROUTER_API_KEY in .env file"
                print_info "Get your free API key from: https://openrouter.ai/"
                exit 1
            fi
            
            if grep -q "your-langsearch-key-here" .env; then
                print_error "Please set your LANGSEARCH_API_KEY in .env file"  
                print_info "Get your free API key from: https://langsearch.ai/"
                exit 1
            fi
        else
            print_error ".env.example file not found"
            exit 1
        fi
    else
        print_success ".env file already exists"
    fi
}

# Create data directory
setup_data_directory() {
    print_info "Setting up data directory..."
    
    if [ ! -d "data" ]; then
        mkdir -p data
        print_success "Created data directory"
    else
        print_success "Data directory already exists"
    fi
    
    # Set proper permissions
    chmod 755 data
}

# Install dependencies
install_dependencies() {
    print_info "Installing dependencies..."
    
    # Frontend dependencies
    if [ -d "frontend" ] && command -v npm &> /dev/null; then
        print_info "Installing frontend dependencies..."
        cd frontend
        npm install
        cd ..
        print_success "Frontend dependencies installed"
    fi
    
    # Backend dependencies
    if [ -d "backend" ] && command -v python3 &> /dev/null; then
        print_info "Installing backend dependencies..."
        cd backend
        python3 -m venv venv
        source venv/bin/activate
        pip install -r requirements.txt
        cd ..
        print_success "Backend dependencies installed"
    fi
}

# Setup Ollama
setup_ollama() {
    print_info "Setting up Ollama for embeddings..."
    
    # Check if Ollama is running
    if curl -s http://localhost:11434/api/tags > /dev/null 2>&1; then
        print_success "Ollama is already running"
    else
        print_info "Starting Ollama with Docker..."
        docker run -d --name ollama -p 11434:11434 -v ollama_data:/root/.ollama ollama/ollama:latest
        
        # Wait for Ollama to start
        print_info "Waiting for Ollama to start..."
        for i in {1..30}; do
            if curl -s http://localhost:11434/api/tags > /dev/null 2>&1; then
                print_success "Ollama is running"
                break
            fi
            sleep 1
        done
    fi
    
    # Pull required models
    print_info "Pulling required embedding model..."
    docker exec ollama ollama pull nomic-embed-text
    print_success "Embedding model ready"
}

# Build Docker containers
build_containers() {
    print_info "Building Docker containers..."
    
    docker-compose build
    print_success "Docker containers built successfully"
}

# Run health checks
run_health_checks() {
    print_info "Running health checks..."
    
    # Start services
    docker-compose up -d
    
    # Wait for services to be ready
    print_info "Waiting for services to start..."
    sleep 10
    
    # Check backend health
    if curl -f http://localhost:8000/health > /dev/null 2>&1; then
        print_success "Backend is healthy"
    else
        print_error "Backend health check failed"
        docker-compose logs backend
        exit 1
    fi
    
    # Check frontend
    if curl -f http://localhost:3000 > /dev/null 2>&1; then
        print_success "Frontend is accessible"
    else
        print_error "Frontend is not accessible"
        docker-compose logs frontend
        exit 1
    fi
    
    # Check Ollama
    if curl -f http://localhost:11434/api/tags > /dev/null 2>&1; then
        print_success "Ollama is healthy"
    else
        print_error "Ollama health check failed"
        exit 1
    fi
}

# Create development scripts
create_dev_scripts() {
    print_info "Creating development scripts..."
    
    # Create dev script directory
    mkdir -p scripts
    
    # Backend dev script
    cat > scripts/dev-backend.sh << 'EOF'
#!/bin/bash
cd backend
source venv/bin/activate
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
EOF
    
    # Frontend dev script
    cat > scripts/dev-frontend.sh << 'EOF'
#!/bin/bash
cd frontend
npm run dev
EOF
    
    # Full dev environment script
    cat > scripts/dev-full.sh << 'EOF'
#!/bin/bash
echo "🚀 Starting full development environment..."
docker-compose up -d
echo "✅ Development environment is running!"
echo "📱 Frontend: http://localhost:3000"
echo "🔧 Backend: http://localhost:8000"
echo "🔍 API Docs: http://localhost:8000/docs"
echo "🧠 Ollama: http://localhost:11434"
EOF
    
    # Make scripts executable
    chmod +x scripts/*.sh
    
    print_success "Development scripts created in scripts/ directory"
}

# Main setup function
main() {
    echo "======================================="
    echo "🚀 AI-Powered Dashboard Dev Setup"
    echo "======================================="
    
    check_prerequisites
    setup_environment
    setup_data_directory
    install_dependencies
    setup_ollama
    build_containers
    create_dev_scripts
    run_health_checks
    
    echo ""
    echo "======================================="
    echo "✅ Development Environment Setup Complete!"
    echo "======================================="
    echo ""
    echo "🎯 Quick Start Commands:"
    echo "  Full Environment:  ./scripts/dev-full.sh"
    echo "  Backend Only:      ./scripts/dev-backend.sh"  
    echo "  Frontend Only:     ./scripts/dev-frontend.sh"
    echo ""
    echo "🌐 Access URLs:"
    echo "  Dashboard:         http://localhost:3000"
    echo "  Backend API:       http://localhost:8000"
    echo "  API Documentation: http://localhost:8000/docs"
    echo "  Ollama API:        http://localhost:11434"
    echo ""
    echo "📚 Next Steps:"
    echo "  1. Open http://localhost:3000 in your browser"
    echo "  2. Type anything in the input bar to test AI categorization"
    echo "  3. Check logs with: docker-compose logs -f"
    echo "  4. Run tests with: npm test (frontend) or pytest (backend)"
    echo ""
    print_success "Happy developing! 🎉"
}

# Run main function
main
