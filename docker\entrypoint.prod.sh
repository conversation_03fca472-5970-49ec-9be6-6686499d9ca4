#!/bin/bash
# Production entrypoint script with health checks and monitoring

set -e

echo "🚀 Starting AI-Powered Dashboard in production mode..."

# Environment validation
if [ "$ENVIRONMENT" != "production" ]; then
    echo "⚠️  Warning: ENVIRONMENT is not set to 'production'"
fi

# Required environment variables check
required_vars=("OPENROUTER_API_KEY" "DATABASE_URL" "REDIS_URL" "SECRET_KEY")
for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        echo "❌ Error: Required environment variable $var is not set"
        exit 1
    fi
done

echo "✅ Environment validation passed"

# Create necessary directories
mkdir -p /app/logs /app/data
touch /app/logs/app.log /app/logs/nginx.log

# Database migration (if needed)
echo "🔄 Running database migrations..."
cd /app/backend
python -m alembic upgrade head || echo "⚠️  Database migration failed or not needed"

# Initialize data directory
echo "📁 Initializing data directory..."
if [ ! -f /app/data/initialized ]; then
    python -c "
import asyncio
from app.database.connection import init_database
asyncio.run(init_database())
" && touch /app/data/initialized
    echo "✅ Database initialized"
fi

# Health check function
health_check() {
    local max_attempts=30
    local attempt=1
    
    echo "🔍 Waiting for application to start..."
    while [ $attempt -le $max_attempts ]; do
        if curl -f -s http://localhost:8000/health > /dev/null 2>&1; then
            echo "✅ Application health check passed"
            return 0
        fi
        echo "⏳ Health check attempt $attempt/$max_attempts failed, retrying in 2s..."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    echo "❌ Application failed to start within expected time"
    return 1
}

# Start application in background for health check
echo "🔄 Starting application..."
cd /app/backend

# Export Python path
export PYTHONPATH=/app/backend

# Start the application with supervisord in background
if [ "$1" = "supervisord" ]; then
    # Start supervisord which will manage FastAPI and Nginx
    echo "🔄 Starting supervisord..."
    exec "$@"
else
    # Direct FastAPI start (for development)
    echo "🔄 Starting FastAPI directly..."
    exec uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4 --worker-class uvicorn.workers.UvicornWorker
fi
