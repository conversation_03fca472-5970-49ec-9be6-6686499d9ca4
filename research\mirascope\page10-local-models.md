Title: Local (Open-Source) Models | Mirascope

URL Source: https://mirascope.com/docs/mirascope/learn/local_models

Markdown Content:
Local (Open-Source) Models | Mirascope

===============

[![Image 1: Mirascope Frog Logo](https://mirascope.com/assets/branding/mirascope-logo.svg) Mirascope](https://mirascope.com/)

*   [Docs](https://mirascope.com/docs/mirascope)

[Blog](https://mirascope.com/blog)[Pricing](https://mirascope.com/pricing)

⌘K

Type to search

⌘K to search

Esc to close

[mirascope v1.25.4 1.2k](https://github.com/Mirascope/mirascope)

Mirascope[Lilypad](https://mirascope.com/docs/lilypad)

[Docs](https://mirascope.com/docs/mirascope)[Guides](https://mirascope.com/docs/mirascope/guides)[API Reference](https://mirascope.com/docs/mirascope/api)[LLMs Text](https://mirascope.com/docs/mirascope/llms-full)

[Welcome](https://mirascope.com/docs/mirascope)

Getting Started

[Why Mirascope?](https://mirascope.com/docs/mirascope/getting-started/why)[Help](https://mirascope.com/docs/mirascope/getting-started/help)[Contributing](https://mirascope.com/docs/mirascope/getting-started/contributing)[0.x Migration Guide](https://mirascope.com/docs/mirascope/getting-started/migration)

Learn

[Overview](https://mirascope.com/docs/mirascope/learn)[Prompts](https://mirascope.com/docs/mirascope/learn/prompts)[Calls](https://mirascope.com/docs/mirascope/learn/calls)[Streams](https://mirascope.com/docs/mirascope/learn/streams)[Chaining](https://mirascope.com/docs/mirascope/learn/chaining)[Response Models](https://mirascope.com/docs/mirascope/learn/response_models)[JSON Mode](https://mirascope.com/docs/mirascope/learn/json_mode)[Output Parsers](https://mirascope.com/docs/mirascope/learn/output_parsers)[Tools](https://mirascope.com/docs/mirascope/learn/tools)[Agents](https://mirascope.com/docs/mirascope/learn/agents)[Evals](https://mirascope.com/docs/mirascope/learn/evals)[Async](https://mirascope.com/docs/mirascope/learn/async)[Retries](https://mirascope.com/docs/mirascope/learn/retries)[Local Models](https://mirascope.com/docs/mirascope/learn/local_models)

Provider-Specific Features

[Thinking & Reasoning](https://mirascope.com/docs/mirascope/learn/provider-specific/thinking-and-reasoning)[OpenAI](https://mirascope.com/docs/mirascope/learn/provider-specific/openai)[Anthropic](https://mirascope.com/docs/mirascope/learn/provider-specific/anthropic)

Extensions

[Middleware](https://mirascope.com/docs/mirascope/learn/extensions/middleware)[Custom LLM Provider](https://mirascope.com/docs/mirascope/learn/extensions/custom_provider)

MCP - Model Context Protocol

[Client](https://mirascope.com/docs/mirascope/learn/mcp/client)

Local (Open-Source) Models[](https://mirascope.com/docs/mirascope/learn/local_models#local-open-source-models)
==============================================================================================================

You can use the [`llm.call`](https://mirascope.com/docs/mirascope/api) decorator to interact with models running with [Ollama](https://github.com/ollama/ollama) or [vLLM](https://github.com/vllm-project/vllm):

Ollama vLLM

```
from mirascope import llm
from pydantic import BaseModel

@llm.call("ollama", "llama3.2") 
def recommend_book(genre: str) -> str:
    return f"Recommend a {genre} book"

recommendation = recommend_book("fantasy")
print(recommendation)
# Output: Here are some popular and highly-recommended fantasy books...

class Book(BaseModel):
    title: str
    author: str

@llm.call("ollama", "llama3.2", response_model=Book) 
def extract_book(text: str) -> str:
    return f"Extract {text}"

book = extract_book("The Name of the Wind by Patrick Rothfuss")
assert isinstance(book, Book)
print(book)
# Output: title='The Name of the Wind' author='Patrick Rothfuss'
```

Double Check Support

OpenAI Compatibility[](https://mirascope.com/docs/mirascope/learn/local_models#openai-compatibility)
----------------------------------------------------------------------------------------------------

When hosting (fine-tuned) open-source LLMs yourself locally or in your own cloud with tools that have OpenAI compatibility, you can use the [`openai.call`](https://mirascope.com/docs/mirascope/api) decorator with a [custom client](https://mirascope.com/docs/mirascope/learn/calls#custom-client) to interact with your model using all of Mirascope's various features.

Ollama vLLM

```
from mirascope.core import openai
from openai import OpenAI
from pydantic import BaseModel

custom_client = OpenAI( 
    base_url="http://localhost:11434/v1",  # your ollama endpoint 
    api_key="ollama",  # required by openai, but unused 
) 

@openai.call("llama3.2", client=custom_client) 
def recommend_book(genre: str) -> str:
    return f"Recommend a {genre} book"

recommendation = recommend_book("fantasy")
print(recommendation)
# Output: Here are some popular and highly-recommended fantasy books...

class Book(BaseModel):
    title: str
    author: str

@openai.call("llama3.2", response_model=Book, client=custom_client) 
def extract_book(text: str) -> str:
    return f"Extract {text}"

book = extract_book("The Name of the Wind by Patrick Rothfuss")
assert isinstance(book, Book)
print(book)
# Output: title='The Name of the Wind' author='Patrick Rothfuss'
```

Copy as Markdown

#### Provider

OpenAI

#### On this page

Local (Open-Source) Models OpenAI Compatibility

Copy as Markdown

#### Provider

OpenAI

#### On this page

Local (Open-Source) Models OpenAI Compatibility

© 2025 Mirascope. All rights reserved.

[Privacy Policy](https://mirascope.com/privacy)[Terms of Use](https://mirascope.com/terms/use)

Cookie Consent
--------------

We use cookies to track usage and improve the site.

Reject Accept
