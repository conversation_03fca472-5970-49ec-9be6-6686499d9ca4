# Multi-stage Docker build for production deployment
# PATTERN: Optimized production container with security hardening

# ===== BUILD STAGE =====
FROM node:18-alpine AS frontend-builder

WORKDIR /app/frontend

# Copy package files
COPY frontend/package*.json ./
COPY frontend/yarn.lock* ./

# Install dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy source code
COPY frontend/ ./

# Build the application
ENV NODE_ENV=production
ENV VITE_API_BASE_URL=/api
RUN npm run build

# ===== PYTHON BUILD STAGE =====
FROM python:3.11-slim AS backend-builder

WORKDIR /app/backend

# Install system dependencies for building
RUN apt-get update && apt-get install -y \
    build-essential \
    gcc \
    g++ \
    libffi-dev \
    libssl-dev \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY backend/requirements.txt ./
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# ===== PRODUCTION STAGE =====
FROM python:3.11-slim AS production

# Create non-root user for security
RUN groupadd -r appgroup && useradd -r -g appgroup appuser

# Install runtime dependencies only
RUN apt-get update && apt-get install -y \
    nginx \
    supervisor \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Set working directory
WORKDIR /app

# Copy Python dependencies from builder
COPY --from=backend-builder /usr/local/lib/python3.11/site-packages/ /usr/local/lib/python3.11/site-packages/
COPY --from=backend-builder /usr/local/bin/ /usr/local/bin/

# Copy built frontend
COPY --from=frontend-builder /app/frontend/dist /app/static

# Copy backend application
COPY backend/ ./backend/

# Copy configuration files
COPY docker/nginx.prod.conf /etc/nginx/sites-available/default
COPY docker/supervisord.prod.conf /etc/supervisor/conf.d/supervisord.conf
COPY docker/entrypoint.prod.sh /entrypoint.sh

# Create necessary directories
RUN mkdir -p /app/logs /app/data /var/log/supervisor \
    && touch /app/logs/app.log /app/logs/nginx.log \
    && chown -R appuser:appgroup /app \
    && chown -R appuser:appgroup /var/log/supervisor \
    && chmod +x /entrypoint.sh

# Security hardening
RUN chmod 750 /app && \
    find /app -type f -exec chmod 640 {} \; && \
    find /app -type d -exec chmod 750 {} \; && \
    chmod +x /app/backend/*.py 2>/dev/null || true

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 8080

# Set environment variables
ENV PYTHONPATH=/app/backend
ENV PYTHONUNBUFFERED=1
ENV ENVIRONMENT=production
ENV PORT=8000

# Start application
ENTRYPOINT ["/entrypoint.sh"]
CMD ["supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]
