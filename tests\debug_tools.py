"""
Debugging tool instantiation issues.
"""

import sys
from pathlib import Path

def main():
    print("🔧 Debugging Tool Instantiation...")
    
    # Add backend to Python path
    project_root = Path(__file__).parent.parent
    backend_path = project_root / "backend"
    sys.path.insert(0, str(backend_path))
    
    # Load environment
    import dotenv
    dotenv.load_dotenv(project_root / ".env")
    
    print("1. Testing TaskTool instantiation separately...")
    try:
        from app.tools.task_tool import TaskTool
        print(f"TaskTool class: {TaskTool}")
        
        # Try to instantiate
        task_tool = TaskTool()
        print(f"✅ TaskTool instantiated: {task_tool}")
        print(f"TaskTool name: {getattr(task_tool, 'name', 'NO NAME FOUND')}")
        print(f"TaskTool type: {type(task_tool)}")
        
    except Exception as e:
        print(f"❌ TaskTool failed: {e}")
        import traceback
        traceback.print_exc()
        return
        
    print("\n2. Testing CalendarTool instantiation separately...")
    try:
        from app.tools.calendar_tool import CalendarTool
        calendar_tool = CalendarTool()
        print(f"✅ CalendarTool instantiated: {calendar_tool}")
        
    except Exception as e:
        print(f"❌ CalendarTool failed: {e}")
        import traceback
        traceback.print_exc()
        return
        
    print("\n3. Testing WebSearchTool instantiation separately...")
    try:
        from app.tools.web_search_tool import WebSearchTool
        web_tool = WebSearchTool()
        print(f"✅ WebSearchTool instantiated: {web_tool}")
        
    except Exception as e:
        print(f"❌ WebSearchTool failed: {e}")
        import traceback
        traceback.print_exc()
        return
        
    print("\n4. Testing HybridDatabaseSearchTool instantiation separately...")
    try:
        from app.tools.database_search_tool import HybridDatabaseSearchTool
        db_tool = HybridDatabaseSearchTool()
        print(f"✅ HybridDatabaseSearchTool instantiated: {db_tool}")
        
    except Exception as e:
        print(f"❌ HybridDatabaseSearchTool failed: {e}")
        import traceback
        traceback.print_exc()
        return
    
    print("\n🎉 All tools instantiated successfully!")

if __name__ == "__main__":
    main()
