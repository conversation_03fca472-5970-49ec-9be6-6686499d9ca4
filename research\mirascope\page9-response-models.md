Title: Response Models | Mirascope

URL Source: https://mirascope.com/docs/mirascope/learn/response_models

Markdown Content:
If you haven't already, we recommend first reading the section on [Calls](https://mirascope.com/docs/mirascope/learn/calls)

Response Models in Mirascope provide a powerful way to structure and validate the output from Large Language Models (LLMs). By leveraging Pydantic's [`BaseModel`](https://docs.pydantic.dev/latest/usage/models/), Response Models offer type safety, automatic validation, and easier data manipulation for your LLM responses. While we cover some details in this documentation, we highly recommend reading through Pydantic's documentation for a deeper, comprehensive dive into everything you can do with Pydantic's `BaseModel`.

Why Use Response Models?[](https://mirascope.com/docs/mirascope/learn/response_models#why-use-response-models)
--------------------------------------------------------------------------------------------------------------

1.   **Structured Output**: Define exactly what you expect from the LLM, ensuring consistency in responses.
2.   **Automatic Validation**: Pydantic handles type checking and validation, reducing errors in your application.
3.   **Improved Type Hinting**: Better IDE support and clearer code structure.
4.   **Easier Data Manipulation**: Work with Python objects instead of raw strings or dictionaries.

Basic Usage and Syntax[](https://mirascope.com/docs/mirascope/learn/response_models#basic-usage-and-syntax)
-----------------------------------------------------------------------------------------------------------

Let's take a look at a basic example using Mirascope vs. official provider SDKs:

Official SDK

Notice how Mirascope makes generating structured outputs significantly simpler than the official SDKs. It also greatly reduces boilerplate and standardizes the interaction across all supported LLM providers.

Tools By Default

### Accessing Original Call Response[](https://mirascope.com/docs/mirascope/learn/response_models#accessing-original-call-response)

Every `response_model` that uses a Pydantic `BaseModel` will automatically have the original `BaseCallResponse` instance accessible through the `_response` property:

### Built-In Types[](https://mirascope.com/docs/mirascope/learn/response_models#built-in-types)

For cases where you want to extract just a single built-in type, Mirascope provides a shorthand:

Here, we are using `list[str]` as the `response_model`, which Mirascope handles without needing to define a full `BaseModel`. You could of course set `response_model=list[Book]` as well.

Note that we have no way of attaching `BaseCallResponse` to built-in types, so using a Pydantic `BaseModel` is recommended if you anticipate needing access to the original call response.

Supported Field Types[](https://mirascope.com/docs/mirascope/learn/response_models#supported-field-types)
---------------------------------------------------------------------------------------------------------

While Mirascope provides a consistent interface, type support varies among providers:

| Type | OpenAI | Anthropic | Google | Groq | xAI | Mistral | Cohere |
| --- | --- | --- | --- | --- | --- | --- | --- |
| str | ✓✓ | ✓✓ | ✓✓ | ✓✓ | ✓✓ | ✓✓ | ✓✓ |
| int | ✓✓ | ✓✓ | ✓✓ | ✓✓ | ✓✓ | ✓✓ | ✓✓ |
| float | ✓✓ | ✓✓ | ✓✓ | ✓✓ | ✓✓ | ✓✓ | ✓✓ |
| bool | ✓✓ | ✓✓ | ✓✓ | ✓✓ | ✓✓ | ✓✓ | ✓✓ |
| bytes | ✓✓ | ✓✓ | -✓ | ✓✓ | ✓✓ | ✓✓ | ✓✓ |
| list | ✓✓ | ✓✓ | ✓✓ | ✓✓ | ✓✓ | ✓✓ | ✓✓ |
| set | ✓✓ | ✓✓ | -- | ✓✓ | ✓✓ | ✓✓ | ✓✓ |
| tuple | -✓ | ✓✓ | -✓ | ✓✓ | -✓ | ✓✓ | ✓✓ |
| dict | -✓ | ✓✓ | ✓✓ | ✓✓ | -✓ | ✓✓ | ✓✓ |
| Literal/Enum | ✓✓ | ✓✓ | ✓✓ | ✓✓ | ✓✓ | ✓✓ | ✓✓ |
| BaseModel | ✓✓ | ✓✓ | ✓✓ | ✓✓ | ✓✓ | ✓✓ | -✓ |
| Nested ($def) | ✓✓ | ✓✓ | ✓✓ | ✓✓ | ✓✓ | ✓✓ | -- |

✓✓ : Fully Supported, -✓: Only JSON Mode Support, -- : Not supported

Validation and Error Handling[](https://mirascope.com/docs/mirascope/learn/response_models#validation-and-error-handling)
-------------------------------------------------------------------------------------------------------------------------

While `response_model` significantly improves output structure and validation, it's important to handle potential errors.

Let's take a look at an example where we want to validate that all fields are uppercase:

Without additional prompt engineering, this call will fail every single time. It's important to engineer your prompts to reduce errors, but LLMs are far from perfect, so always remember to catch and handle validation errors gracefully.

We highly recommend taking a look at our section on [retries](https://mirascope.com/docs/mirascope/learn/retries) to learn more about automatically retrying and re-inserting validation errors, which enables retrying the call such that the LLM can learn from its previous mistakes.

### Accessing Original Call Response On Error[](https://mirascope.com/docs/mirascope/learn/response_models#accessing-original-call-response-on-error)

In case of a `ValidationError`, you can access the original response for debugging:

This allows you to gracefully handle errors as well as inspect the original LLM response when validation fails.

JSON Mode[](https://mirascope.com/docs/mirascope/learn/response_models#json-mode)
---------------------------------------------------------------------------------

By default, `response_model` uses [Tools](https://mirascope.com/docs/mirascope/learn/tools) under the hood. You can instead use [JSON Mode](https://mirascope.com/docs/mirascope/learn/json_mode) in conjunction with `response_model` by setting `json_mode=True`:

Few-Shot Examples[](https://mirascope.com/docs/mirascope/learn/response_models#few-shot-examples)
-------------------------------------------------------------------------------------------------

Adding few-shot examples to your response model can improve results by demonstrating exactly how to adhere to your desired output.

We take advantage of Pydantic's [`Field`](https://docs.pydantic.dev/latest/concepts/fields/) and [`ConfigDict`](https://docs.pydantic.dev/latest/concepts/config/) to add these examples to response models:

Streaming Response Models[](https://mirascope.com/docs/mirascope/learn/response_models#streaming-response-models)
-----------------------------------------------------------------------------------------------------------------

If you set `stream=True` when `response_model` is set, your LLM call will return an `Iterable` where each item will be a partial version of your response model representing the current state of the streamed information. The final model returned by the iterator will be the full response model.

Once exhausted, you can access the final, full response model through the `constructed_response_model` property of the structured stream. Note that this will also give you access to the [`._response` property](https://mirascope.com/docs/mirascope/learn/response_models#accessing-original-call-response) that every `BaseModel` receives.

You can also use the `stream` property to access the `BaseStream` instance and [all of it's properties](https://mirascope.com/docs/mirascope/learn/streams#common-stream-properties-and-methods).

FromCallArgs[](https://mirascope.com/docs/mirascope/learn/response_models#fromcallargs)
---------------------------------------------------------------------------------------

Fields annotated with `FromCallArgs` will be populated with the corresponding argument from the function call rather than expecting it from the LLM's response. This enables seamless validation of LLM outputs against function inputs:

Next Steps[](https://mirascope.com/docs/mirascope/learn/response_models#next-steps)
-----------------------------------------------------------------------------------

By following these best practices and leveraging Response Models effectively, you can create more robust, type-safe, and maintainable LLM-powered applications with Mirascope.

Next, we recommend taking a look at one of:

*   [JSON Mode](https://mirascope.com/docs/mirascope/learn/json_mode) to see an alternate way to generate structured outputs where using Pydantic to validate outputs is optional.
*   [Evals](https://mirascope.com/docs/mirascope/learn/evals) to see how to use `response_model` to evaluate your prompts.
