Title: library

URL Source: https://ollama.ai/library

Markdown Content:
*   [deepseek-r1 ----------- DeepSeek-R1 is a family of open reasoning models with performance approaching that of leading models, such as O3 and Gemini 2.5 Pro. tools thinking 1.5b 7b 8b 14b 32b 70b 671b 53.8M Pulls 35 Tags Updated 2 weeks ago](https://ollama.com/library/deepseek-r1)
*   [gemma3n ------- Gemma 3n models are designed for efficient execution on everyday devices such as laptops, tablets or phones. e2b e4b 193.4K Pulls 9 Tags Updated 3 weeks ago](https://ollama.com/library/gemma3n)
*   [gemma3 ------ The current, most capable model that runs on a single GPU. vision 1b 4b 12b 27b 9.1M Pulls 21 Tags Updated 3 months ago](https://ollama.com/library/gemma3)
*   [qwen3 ----- Qwen3 is the latest generation of large language models in Qwen series, offering a comprehensive suite of dense and mixture-of-experts (MoE) models. tools thinking 0.6b 1.7b 4b 8b 14b 30b 32b 235b 3.7M Pulls 35 Tags Updated 1 month ago](https://ollama.com/library/qwen3)
*   [qwen2.5vl --------- Flagship vision-language model of Qwen and also a significant leap from the previous Qwen2-VL. vision 3b 7b 32b 72b 375.4K Pulls 17 Tags Updated 1 month ago](https://ollama.com/library/qwen2.5vl)
*   [llama3.1 -------- Llama 3.1 is a new state-of-the-art model from Meta available in 8B, 70B and 405B parameter sizes. tools 8b 70b 405b 98.1M Pulls 93 Tags Updated 7 months ago](https://ollama.com/library/llama3.1)
*   [nomic-embed-text ---------------- A high-performing open embedding model with a large token context window. embedding 33.2M Pulls 3 Tags Updated 1 year ago](https://ollama.com/library/nomic-embed-text)
*   [llama3.2 -------- Meta's Llama 3.2 goes small with 1B and 3B models. tools 1b 3b 25.7M Pulls 63 Tags Updated 9 months ago](https://ollama.com/library/llama3.2)
*   [mistral ------- The 7B model released by Mistral AI, updated to version 0.3. tools 7b 16.7M Pulls 84 Tags Updated 6 days ago](https://ollama.com/library/mistral)
*   [qwen2.5 ------- Qwen2.5 models are pretrained on Alibaba's latest large-scale dataset, encompassing up to 18 trillion tokens. The model supports up to 128K tokens and has multilingual support. tools 0.5b 1.5b 3b 7b 14b 32b 72b 11.3M Pulls 133 Tags Updated 10 months ago](https://ollama.com/library/qwen2.5)
*   [llama3 ------ Meta Llama 3: The most capable openly available LLM to date 8b 70b 9.4M Pulls 68 Tags Updated 1 year ago](https://ollama.com/library/llama3)
*   [llava ----- 🌋 LLaVA is a novel end-to-end trained large multimodal model that combines a vision encoder and Vicuna for general-purpose visual and language understanding. Updated to version 1.6. vision 7b 13b 34b 7.8M Pulls 98 Tags Updated 1 year ago](https://ollama.com/library/llava)
*   [gemma2 ------ Google Gemma 2 is a high-performing and efficient model available in three sizes: 2B, 9B, and 27B. 2b 9b 27b 6.1M Pulls 94 Tags Updated 11 months ago](https://ollama.com/library/gemma2)
*   [phi3 ---- Phi-3 is a family of lightweight 3B (Mini) and 14B (Medium) state-of-the-art open models by Microsoft. 3.8b 14b 5.8M Pulls 72 Tags Updated 11 months ago](https://ollama.com/library/phi3)
*   [qwen2.5-coder ------------- The latest series of Code-Specific Qwen models, with significant improvements in code generation, code reasoning, and code fixing. tools 0.5b 1.5b 3b 7b 14b 32b 5.8M Pulls 199 Tags Updated 1 month ago](https://ollama.com/library/qwen2.5-coder)
*   [gemma ----- Gemma is a family of lightweight, state-of-the-art open models built by Google DeepMind. Updated to version 1.1 2b 7b 5.1M Pulls 102 Tags Updated 1 year ago](https://ollama.com/library/gemma)
*   [qwen ---- Qwen 1.5 is a series of large language models by Alibaba Cloud spanning from 0.5B to 110B parameters 0.5b 1.8b 4b 7b 14b 32b 72b 110b 4.8M Pulls 379 Tags Updated 1 year ago](https://ollama.com/library/qwen)
*   [qwen2 ----- Qwen2 is a new series of large language models from Alibaba group tools 0.5b 1.5b 7b 72b 4.3M Pulls 97 Tags Updated 10 months ago](https://ollama.com/library/qwen2)
*   [mxbai-embed-large ----------------- State-of-the-art large embedding model from mixedbread.ai embedding 335m 4.2M Pulls 4 Tags Updated 1 year ago](https://ollama.com/library/mxbai-embed-large)
*   [llama2 ------ Llama 2 is a collection of foundation language models ranging from 7B to 70B parameters. 7b 13b 70b 3.8M Pulls 102 Tags Updated 1 year ago](https://ollama.com/library/llama2)
*   [phi4 ---- Phi-4 is a 14B parameter, state-of-the-art open model from Microsoft. 14b 3.4M Pulls 5 Tags Updated 6 months ago](https://ollama.com/library/phi4)
*   [codellama --------- A large language model that can use text prompts to generate and discuss code. 7b 13b 34b 70b 2.5M Pulls 199 Tags Updated 1 year ago](https://ollama.com/library/codellama)
*   [tinyllama --------- The TinyLlama project is an open endeavor to train a compact 1.1B Llama model on 3 trillion tokens. 1.1b 2.4M Pulls 36 Tags Updated 1 year ago](https://ollama.com/library/tinyllama)
*   [minicpm-v --------- A series of multimodal LLMs (MLLMs) designed for vision-language understanding. vision 8b 2.4M Pulls 17 Tags Updated 8 months ago](https://ollama.com/library/minicpm-v)
*   [llama3.2-vision --------------- Llama 3.2 Vision is a collection of instruction-tuned image reasoning generative models in 11B and 90B sizes. vision 11b 90b 2.2M Pulls 9 Tags Updated 1 month ago](https://ollama.com/library/llama3.2-vision)
*   [llama3.3 -------- New state of the art 70B model. Llama 3.3 70B offers similar performance compared to the Llama 3.1 405B model. tools 70b 2.2M Pulls 14 Tags Updated 7 months ago](https://ollama.com/library/llama3.3)
*   [mistral-nemo ------------ A state-of-the-art 12B model with 128k context length, built by Mistral AI in collaboration with NVIDIA. tools 12b 2.1M Pulls 17 Tags Updated 11 months ago](https://ollama.com/library/mistral-nemo)
*   [dolphin3 -------- Dolphin 3.0 Llama 3.1 8B 🐬 is the next generation of the Dolphin series of instruct-tuned models designed to be the ultimate general purpose local model, enabling coding, math, agentic, function calling, and general use cases. 8b 2M Pulls 5 Tags Updated 6 months ago](https://ollama.com/library/dolphin3)
*   [olmo2 ----- OLMo 2 is a new family of 7B and 13B models trained on up to 5T tokens. These models are on par with or better than equivalently sized fully open models, and competitive with open-weight models such as Llama 3.1 on English academic benchmarks. 7b 13b 1.9M Pulls 9 Tags Updated 6 months ago](https://ollama.com/library/olmo2)
*   [deepseek-v3 ----------- A strong Mixture-of-Experts (MoE) language model with 671B total parameters with 37B activated for each token. 671b 1.8M Pulls 5 Tags Updated 6 months ago](https://ollama.com/library/deepseek-v3)
*   [bge-m3 ------ BGE-M3 is a new model from BAAI distinguished for its versatility in Multi-Functionality, Multi-Linguality, and Multi-Granularity. embedding 567m 1.6M Pulls 3 Tags Updated 11 months ago](https://ollama.com/library/bge-m3)
*   [qwq --- QwQ is the reasoning model of the Qwen series. tools 32b 1.5M Pulls 8 Tags Updated 4 months ago](https://ollama.com/library/qwq)
*   [mistral-small ------------- Mistral Small 3 sets a new benchmark in the “small” Large Language Models category below 70B. tools 22b 24b 1.3M Pulls 21 Tags Updated 5 months ago](https://ollama.com/library/mistral-small)
*   [llava-llama3 ------------ A LLaVA model fine-tuned from Llama 3 Instruct with better scores in several benchmarks. vision 8b 1.3M Pulls 4 Tags Updated 1 year ago](https://ollama.com/library/llava-llama3)
*   [smollm2 ------- SmolLM2 is a family of compact language models available in three size: 135M, 360M, and 1.7B parameters. tools 135m 360m 1.7b 1.2M Pulls 49 Tags Updated 8 months ago](https://ollama.com/library/smollm2)
*   [llama2-uncensored ----------------- Uncensored Llama 2 model by George Sung and Jarrad Hope. 7b 70b 1.2M Pulls 34 Tags Updated 1 year ago](https://ollama.com/library/llama2-uncensored)
*   [mixtral ------- A set of Mixture of Experts (MoE) model with open weights by Mistral AI in 8x7b and 8x22b parameter sizes. tools 8x7b 8x22b 1.1M Pulls 70 Tags Updated 7 months ago](https://ollama.com/library/mixtral)
*   [starcoder2 ---------- StarCoder2 is the next generation of transparently trained open code LLMs that comes in three sizes: 3B, 7B and 15B parameters. 3b 7b 15b 1M Pulls 67 Tags Updated 10 months ago](https://ollama.com/library/starcoder2)
*   [deepseek-coder-v2 ----------------- An open-source Mixture-of-Experts code language model that achieves performance comparable to GPT4-Turbo in code-specific tasks. 16b 236b 910K Pulls 64 Tags Updated 10 months ago](https://ollama.com/library/deepseek-coder-v2)
*   [deepseek-coder -------------- DeepSeek Coder is a capable coding model trained on two trillion code and natural language tokens. 1.3b 6.7b 33b 838.4K Pulls 102 Tags Updated 1 year ago](https://ollama.com/library/deepseek-coder)
*   [all-minilm ---------- Embedding models on very large sentence level datasets. embedding 22m 33m 817.2K Pulls 10 Tags Updated 1 year ago](https://ollama.com/library/all-minilm)
*   [snowflake-arctic-embed ---------------------- A suite of text embedding models by Snowflake, optimized for performance. embedding 22m 33m 110m 137m 335m 760.5K Pulls 16 Tags Updated 1 year ago](https://ollama.com/library/snowflake-arctic-embed)
*   [phi --- Phi-2: a 2.7B language model by Microsoft Research that demonstrates outstanding reasoning and language understanding capabilities. 2.7b 666.9K Pulls 18 Tags Updated 1 year ago](https://ollama.com/library/phi)
*   [codegemma --------- CodeGemma is a collection of powerful, lightweight models that can perform a variety of coding tasks like fill-in-the-middle code completion, code generation, natural language understanding, mathematical reasoning, and instruction following. 2b 7b 664K Pulls 85 Tags Updated 1 year ago](https://ollama.com/library/codegemma)
*   [dolphin-mixtral --------------- Uncensored, 8x7b and 8x22b fine-tuned models based on the Mixtral mixture of experts models that excels at coding tasks. Created by Eric Hartford. 8x7b 8x22b 607.8K Pulls 70 Tags Updated 7 months ago](https://ollama.com/library/dolphin-mixtral)
*   [openthinker ----------- A fully open-source family of reasoning models built using a dataset derived by distilling DeepSeek-R1. 7b 32b 535.6K Pulls 15 Tags Updated 3 months ago](https://ollama.com/library/openthinker)
*   [llama4 ------ Meta's latest collection of multimodal models. vision tools 16x17b 128x17b 465K Pulls 11 Tags Updated 1 month ago](https://ollama.com/library/llama4)
*   [orca-mini --------- A general-purpose model ranging from 3 billion parameters to 70 billion, suitable for entry-level hardware. 3b 7b 13b 70b 431.5K Pulls 119 Tags Updated 1 year ago](https://ollama.com/library/orca-mini)
*   [wizardlm2 --------- State of the art large language model from Microsoft AI with improved performance on complex chat, multilingual, reasoning and agent use cases. 7b 8x22b 381.8K Pulls 22 Tags Updated 1 year ago](https://ollama.com/library/wizardlm2)
*   [dolphin-mistral --------------- The uncensored Dolphin model based on Mistral that excels at coding tasks. Updated to version 2.8. 7b 365.5K Pulls 120 Tags Updated 1 year ago](https://ollama.com/library/dolphin-mistral)
*   [smollm ------ 🪐 A family of small models with 135M, 360M, and 1.7B parameters, trained on a new high-quality dataset. 135m 360m 1.7b 353.1K Pulls 94 Tags Updated 11 months ago](https://ollama.com/library/smollm)
*   [dolphin-llama3 -------------- Dolphin 2.9 is a new model with 8B and 70B sizes by Eric Hartford based on Llama 3 that has a variety of instruction, conversational, and coding skills. 8b 70b 344.9K Pulls 53 Tags Updated 1 year ago](https://ollama.com/library/dolphin-llama3)
*   [codestral --------- Codestral is Mistral AI’s first-ever code model designed for code generation tasks. 22b 334.9K Pulls 17 Tags Updated 10 months ago](https://ollama.com/library/codestral)
*   [command-r --------- Command R is a Large Language Model optimized for conversational interaction and long context tasks. tools 35b 303.5K Pulls 32 Tags Updated 10 months ago](https://ollama.com/library/command-r)
*   [hermes3 ------- Hermes 3 is the latest version of the flagship Hermes series of LLMs by Nous Research tools 3b 8b 70b 405b 302.5K Pulls 65 Tags Updated 7 months ago](https://ollama.com/library/hermes3)
*   [phi3.5 ------ A lightweight AI model with 3.8 billion parameters with performance overtaking similarly and larger sized models. 3.8b 300.5K Pulls 17 Tags Updated 10 months ago](https://ollama.com/library/phi3.5)
*   [yi -- Yi 1.5 is a high-performing, bilingual language model. 6b 9b 34b 285.4K Pulls 174 Tags Updated 1 year ago](https://ollama.com/library/yi)
*   [zephyr ------ Zephyr is a series of fine-tuned versions of the Mistral and Mixtral models that are trained to act as helpful assistants. 7b 141b 273.8K Pulls 40 Tags Updated 1 year ago](https://ollama.com/library/zephyr)
*   [phi4-mini --------- Phi-4-mini brings significant enhancements in multilingual support, reasoning, and mathematics, and now, the long-awaited function calling feature is finally supported. tools 3.8b 228.4K Pulls 5 Tags Updated 4 months ago](https://ollama.com/library/phi4-mini)
*   [moondream --------- moondream2 is a small vision language model designed to run efficiently on edge devices. vision 1.8b 221.9K Pulls 18 Tags Updated 1 year ago](https://ollama.com/library/moondream)
*   [granite-code ------------ A family of open foundation models by IBM for Code Intelligence 3b 8b 20b 34b 221.5K Pulls 162 Tags Updated 10 months ago](https://ollama.com/library/granite-code)
*   [wizard-vicuna-uncensored ------------------------ Wizard Vicuna Uncensored is a 7B, 13B, and 30B parameter model based on Llama 2 uncensored by Eric Hartford. 7b 13b 30b 219.5K Pulls 49 Tags Updated 1 year ago](https://ollama.com/library/wizard-vicuna-uncensored)
*   [granite3.3 ---------- IBM Granite 2B and 8B models are 128K context length language models that have been fine-tuned for improved reasoning and instruction-following capabilities. tools 2b 8b 213.6K Pulls 3 Tags Updated 3 months ago](https://ollama.com/library/granite3.3)
*   [starcoder --------- StarCoder is a code generation model trained on 80+ programming languages. 1b 3b 7b 15b 207.2K Pulls 100 Tags Updated 1 year ago](https://ollama.com/library/starcoder)
*   [devstral -------- Devstral: the best open source model for coding agents tools 24b 188.8K Pulls 5 Tags Updated 2 weeks ago](https://ollama.com/library/devstral)
*   [vicuna ------ General use chat model based on Llama and Llama 2 with 2K to 16K context sizes. 7b 13b 33b 186.6K Pulls 111 Tags Updated 1 year ago](https://ollama.com/library/vicuna)
*   [openchat -------- A family of open-source models trained on a wide variety of data, surpassing ChatGPT on various benchmarks. Updated to version 3.5-0106. 7b 178.9K Pulls 50 Tags Updated 1 year ago](https://ollama.com/library/openchat)
*   [magistral --------- Magistral is a small, efficient reasoning model with 24B parameters. tools thinking 24b 178.9K Pulls 5 Tags Updated 1 month ago](https://ollama.com/library/magistral)
*   [mistral-openorca ---------------- Mistral OpenOrca is a 7 billion parameter model, fine-tuned on top of the Mistral 7B model using the OpenOrca dataset. 7b 175.8K Pulls 17 Tags Updated 1 year ago](https://ollama.com/library/mistral-openorca)
*   [mistral-small3.1 ---------------- Building upon Mistral Small 3, Mistral Small 3.1 (2503) adds state-of-the-art vision understanding and enhances long context capabilities up to 128k tokens without compromising text performance. vision tools 24b 174.8K Pulls 5 Tags Updated 3 months ago](https://ollama.com/library/mistral-small3.1)
*   [codegeex4 --------- A versatile model for AI software development scenarios, including code completion. 9b 173.1K Pulls 17 Tags Updated 1 year ago](https://ollama.com/library/codegeex4)
*   [deepcoder --------- DeepCoder is a fully open-Source 14B coder model at O3-mini level, with a 1.5B version also available. 1.5b 14b 170.7K Pulls 9 Tags Updated 3 months ago](https://ollama.com/library/deepcoder)
*   [deepseek-v2 ----------- A strong, economical, and efficient Mixture-of-Experts language model. 16b 236b 168.6K Pulls 34 Tags Updated 1 year ago](https://ollama.com/library/deepseek-v2)
*   [deepseek-llm ------------ An advanced language model crafted with 2 trillion bilingual tokens. 7b 67b 168.4K Pulls 64 Tags Updated 1 year ago](https://ollama.com/library/deepseek-llm)
*   [openhermes ---------- OpenHermes 2.5 is a 7B model fine-tuned by Teknium on Mistral with fully open datasets. 165.6K Pulls 35 Tags Updated 1 year ago](https://ollama.com/library/openhermes)
*   [cogito ------ Cogito v1 Preview is a family of hybrid reasoning models by Deep Cogito that outperform the best available open models of the same size, including counterparts from LLaMA, DeepSeek, and Qwen across most standard benchmarks. tools 3b 8b 14b 32b 70b 163.7K Pulls 20 Tags Updated 3 months ago](https://ollama.com/library/cogito)
*   [codeqwen -------- CodeQwen1.5 is a large language model pretrained on a large amount of code data. 7b 158.3K Pulls 30 Tags Updated 1 year ago](https://ollama.com/library/codeqwen)
*   [phi4-reasoning -------------- Phi 4 reasoning and reasoning plus are 14-billion parameter open-weight reasoning models that rival much larger models on complex reasoning tasks. 14b 156.4K Pulls 9 Tags Updated 2 months ago](https://ollama.com/library/phi4-reasoning)
*   [llama2-chinese -------------- Llama 2 based model fine tuned to improve Chinese dialogue ability. 7b 13b 155.5K Pulls 35 Tags Updated 1 year ago](https://ollama.com/library/llama2-chinese)
*   [aya --- Aya 23, released by Cohere, is a new family of state-of-the-art, multilingual models that support 23 languages. 8b 35b 148.3K Pulls 33 Tags Updated 1 year ago](https://ollama.com/library/aya)
*   [mistral-large ------------- Mistral Large 2 is Mistral's new flagship model that is significantly more capable in code generation, mathematics, and reasoning with 128k context window and support for dozens of languages. tools 123b 144K Pulls 32 Tags Updated 7 months ago](https://ollama.com/library/mistral-large)
*   [glm4 ---- A strong multi-lingual general language model with competitive performance to Llama 3. 9b 140.2K Pulls 32 Tags Updated 1 year ago](https://ollama.com/library/glm4)
*   [tinydolphin ----------- An experimental 1.1B parameter model trained on the new Dolphin 2.8 dataset by Eric Hartford and based on TinyLlama. 1.1b 140.2K Pulls 18 Tags Updated 1 year ago](https://ollama.com/library/tinydolphin)
*   [granite3.2-vision ----------------- A compact and efficient vision-language model, specifically designed for visual document understanding, enabling automated content extraction from tables, charts, infographics, plots, diagrams, and more. vision tools 2b 139.8K Pulls 5 Tags Updated 4 months ago](https://ollama.com/library/granite3.2-vision)
*   [stable-code ----------- Stable Code 3B is a coding model with instruct and code completion variants on par with models such as Code Llama 7B that are 2.5x larger. 3b 139.4K Pulls 36 Tags Updated 1 year ago](https://ollama.com/library/stable-code)
*   [qwen2-math ---------- Qwen2 Math is a series of specialized math language models built upon the Qwen2 LLMs, which significantly outperforms the mathematical capabilities of open-source models and even closed-source models (e.g., GPT4o). 1.5b 7b 72b 138.7K Pulls 52 Tags Updated 10 months ago](https://ollama.com/library/qwen2-math)
*   [nous-hermes2 ------------ The powerful family of models by Nous Research that excels at scientific discussion and coding tasks. 10.7b 34b 134.8K Pulls 33 Tags Updated 1 year ago](https://ollama.com/library/nous-hermes2)
*   [wizardcoder ----------- State-of-the-art code generation model 33b 130.3K Pulls 67 Tags Updated 1 year ago](https://ollama.com/library/wizardcoder)
*   [command-r-plus -------------- Command R+ is a powerful, scalable large language model purpose-built to excel at real-world enterprise use cases. tools 104b 130K Pulls 21 Tags Updated 10 months ago](https://ollama.com/library/command-r-plus)
*   [bakllava -------- BakLLaVA is a multimodal model consisting of the Mistral 7B base model augmented with the LLaVA architecture. vision 7b 123.9K Pulls 17 Tags Updated 1 year ago](https://ollama.com/library/bakllava)
*   [neural-chat ----------- A fine-tuned model based on Mistral with good coverage of domain and language. 7b 119.7K Pulls 50 Tags Updated 1 year ago](https://ollama.com/library/neural-chat)
*   [stablelm2 --------- Stable LM 2 is a state-of-the-art 1.6B and 12B parameter language model trained on multilingual data in English, Spanish, German, Italian, French, Portuguese, and Dutch. 1.6b 12b 117.4K Pulls 84 Tags Updated 1 year ago](https://ollama.com/library/stablelm2)
*   [granite3.2 ---------- Granite-3.2 is a family of long-context AI models from IBM Granite fine-tuned for thinking capabilities. tools 2b 8b 115.7K Pulls 9 Tags Updated 4 months ago](https://ollama.com/library/granite3.2)
*   [bge-large --------- Embedding model from BAAI mapping texts to vectors. embedding 335m 113.8K Pulls 3 Tags Updated 11 months ago](https://ollama.com/library/bge-large)
*   [sqlcoder -------- SQLCoder is a code completion model fined-tuned on StarCoder for SQL generation tasks 7b 15b 112.7K Pulls 48 Tags Updated 1 year ago](https://ollama.com/library/sqlcoder)
*   [llama3-chatqa ------------- A model from NVIDIA based on Llama 3 that excels at conversational question answering (QA) and retrieval-augmented generation (RAG). 8b 70b 110.3K Pulls 35 Tags Updated 1 year ago](https://ollama.com/library/llama3-chatqa)
*   [reflection ---------- A high-performing model trained with a new technique called Reflection-tuning that teaches a LLM to detect mistakes in its reasoning and correct course. 70b 107.4K Pulls 17 Tags Updated 10 months ago](https://ollama.com/library/reflection)
*   [wizard-math ----------- Model focused on math and logic problems 7b 13b 70b 107.1K Pulls 64 Tags Updated 1 year ago](https://ollama.com/library/wizard-math)
*   [llava-phi3 ---------- A new small LLaVA model fine-tuned from Phi 3 Mini. vision 3.8b 105.7K Pulls 4 Tags Updated 1 year ago](https://ollama.com/library/llava-phi3)
*   [granite3.1-dense ---------------- The IBM Granite 2B and 8B models are text-only dense LLMs trained on over 12 trillion tokens of data, demonstrated significant improvements over their predecessors in performance and speed in IBM’s initial testing. tools 2b 8b 105K Pulls 33 Tags Updated 6 months ago](https://ollama.com/library/granite3.1-dense)
*   [llama3-gradient --------------- This model extends LLama-3 8B's context length from 8k to over 1m tokens. 8b 70b 104.5K Pulls 35 Tags Updated 1 year ago](https://ollama.com/library/llama3-gradient)
*   [granite3-dense -------------- The IBM Granite 2B and 8B models are designed to support tool-based use cases and support for retrieval augmented generation (RAG), streamlining code generation, translation and bug fixing. tools 2b 8b 104.2K Pulls 33 Tags Updated 8 months ago](https://ollama.com/library/granite3-dense)
*   [snowflake-arctic-embed2 ----------------------- Snowflake's frontier embedding model. Arctic Embed 2.0 adds multilingual support without sacrificing English performance or scalability. embedding 568m 101.1K Pulls 3 Tags Updated 7 months ago](https://ollama.com/library/snowflake-arctic-embed2)
*   [dbrx ---- DBRX is an open, general-purpose LLM created by Databricks. 132b 101.1K Pulls 7 Tags Updated 1 year ago](https://ollama.com/library/dbrx)
*   [exaone3.5 --------- EXAONE 3.5 is a collection of instruction-tuned bilingual (English and Korean) generative models ranging from 2.4B to 32B parameters, developed and released by LG AI Research. 2.4b 7.8b 32b 98.9K Pulls 13 Tags Updated 7 months ago](https://ollama.com/library/exaone3.5)
*   [samantha-mistral ---------------- A companion assistant trained in philosophy, psychology, and personal relationships. Based on Mistral. 7b 96.6K Pulls 49 Tags Updated 1 year ago](https://ollama.com/library/samantha-mistral)
*   [nous-hermes ----------- General use models based on Llama and Llama 2 from Nous Research. 7b 13b 94.9K Pulls 63 Tags Updated 1 year ago](https://ollama.com/library/nous-hermes)
*   [yi-coder -------- Yi-Coder is a series of open-source code language models that delivers state-of-the-art coding performance with fewer than 10 billion parameters. 1.5b 9b 93.5K Pulls 67 Tags Updated 10 months ago](https://ollama.com/library/yi-coder)
*   [dolphincoder ------------ A 7B and 15B uncensored variant of the Dolphin model family that excels at coding, based on StarCoder2. 7b 15b 93K Pulls 35 Tags Updated 1 year ago](https://ollama.com/library/dolphincoder)
*   [starling-lm ----------- Starling is a large language model trained by reinforcement learning from AI feedback focused on improving chatbot helpfulness. 7b 91.2K Pulls 36 Tags Updated 1 year ago](https://ollama.com/library/starling-lm)
*   [nemotron-mini ------------- A commercial-friendly small language model by NVIDIA optimized for roleplay, RAG QA, and function calling. tools 4b 91K Pulls 17 Tags Updated 10 months ago](https://ollama.com/library/nemotron-mini)
*   [xwinlm ------ Conversational model based on Llama 2 that performs competitively on various benchmarks. 7b 13b 88K Pulls 80 Tags Updated 1 year ago](https://ollama.com/library/xwinlm)
*   [solar ----- A compact, yet powerful 10.7B large language model designed for single-turn conversation. 10.7b 87.9K Pulls 32 Tags Updated 1 year ago](https://ollama.com/library/solar)
*   [phind-codellama --------------- Code generation model based on Code Llama. 34b 87.9K Pulls 49 Tags Updated 1 year ago](https://ollama.com/library/phind-codellama)
*   [internlm2 --------- InternLM2.5 is a 7B parameter model tailored for practical scenarios with outstanding reasoning capability. 1m 1.8b 7b 20b 87.1K Pulls 65 Tags Updated 11 months ago](https://ollama.com/library/internlm2)
*   [deepscaler ---------- A fine-tuned version of Deepseek-R1-Distilled-Qwen-1.5B that surpasses the performance of OpenAI’s o1-preview with just 1.5B parameters on popular math evaluations. 1.5b 86.2K Pulls 5 Tags Updated 5 months ago](https://ollama.com/library/deepscaler)
*   [athene-v2 --------- Athene-V2 is a 72B parameter model which excels at code completion, mathematics, and log extraction tasks. tools 72b 85K Pulls 17 Tags Updated 8 months ago](https://ollama.com/library/athene-v2)
*   [nemotron -------- Llama-3.1-Nemotron-70B-Instruct is a large language model customized by NVIDIA to improve the helpfulness of LLM generated responses to user queries. tools 70b 83K Pulls 17 Tags Updated 9 months ago](https://ollama.com/library/nemotron)
*   [yarn-llama2 ----------- An extension of Llama 2 that supports a context of up to 128k tokens. 7b 13b 82.4K Pulls 67 Tags Updated 1 year ago](https://ollama.com/library/yarn-llama2)
*   [falcon ------ A large language model built by the Technology Innovation Institute (TII) for use in summarization, text generation, and chat bots. 7b 40b 180b 82.4K Pulls 38 Tags Updated 1 year ago](https://ollama.com/library/falcon)
*   [dolphin-phi ----------- 2.7B uncensored Dolphin model by Eric Hartford, based on the Phi language model by Microsoft Research. 2.7b 79.7K Pulls 15 Tags Updated 1 year ago](https://ollama.com/library/dolphin-phi)
*   [wizardlm -------- General use model based on Llama 2. 77.7K Pulls 73 Tags Updated 1 year ago](https://ollama.com/library/wizardlm)
*   [llama3-groq-tool-use -------------------- A series of models from Groq that represent a significant advancement in open-source AI capabilities for tool use/function calling. tools 8b 70b 77K Pulls 33 Tags Updated 11 months ago](https://ollama.com/library/llama3-groq-tool-use)
*   [opencoder --------- OpenCoder is an open and reproducible code LLM family which includes 1.5B and 8B models, supporting chat in English and Chinese languages. 1.5b 8b 73.1K Pulls 9 Tags Updated 8 months ago](https://ollama.com/library/opencoder)
*   [paraphrase-multilingual ----------------------- Sentence-transformers model that can be used for tasks like clustering or semantic search. embedding 278m 71.1K Pulls 3 Tags Updated 11 months ago](https://ollama.com/library/paraphrase-multilingual)
*   [wizardlm-uncensored ------------------- Uncensored version of Wizard LM model 13b 70.3K Pulls 18 Tags Updated 1 year ago](https://ollama.com/library/wizardlm-uncensored)
*   [exaone-deep ----------- EXAONE Deep exhibits superior capabilities in various reasoning tasks including math and coding benchmarks, ranging from 2.4B to 32B parameters developed and released by LG AI Research. 2.4b 7.8b 32b 68.9K Pulls 13 Tags Updated 4 months ago](https://ollama.com/library/exaone-deep)
*   [orca2 ----- Orca 2 is built by Microsoft research, and are a fine-tuned version of Meta's Llama 2 models. The model is designed to excel particularly in reasoning. 7b 13b 68.3K Pulls 33 Tags Updated 1 year ago](https://ollama.com/library/orca2)
*   [aya-expanse ----------- Cohere For AI's language models trained to perform well across 23 different languages. tools 8b 32b 67.8K Pulls 33 Tags Updated 8 months ago](https://ollama.com/library/aya-expanse)
*   [smallthinker ------------ A new small reasoning model fine-tuned from the Qwen 2.5 3B Instruct model. 3b 66.7K Pulls 5 Tags Updated 6 months ago](https://ollama.com/library/smallthinker)
*   [llama-guard3 ------------ Llama Guard 3 is a series of models fine-tuned for content safety classification of LLM inputs and responses. 1b 8b 66K Pulls 33 Tags Updated 9 months ago](https://ollama.com/library/llama-guard3)
*   [falcon3 ------- A family of efficient AI models under 10B parameters performant in science, math, and coding through innovative training techniques. 1b 3b 7b 10b 65.9K Pulls 17 Tags Updated 7 months ago](https://ollama.com/library/falcon3)
*   [granite-embedding ----------------- The IBM Granite Embedding 30M and 278M models models are text-only dense biencoder embedding models, with 30M available in English only and 278M serving multilingual use cases. embedding 30m 278m 64K Pulls 6 Tags Updated 7 months ago](https://ollama.com/library/granite-embedding)
*   [medllama2 --------- Fine-tuned Llama 2 model to answer medical questions based on an open source medical dataset. 7b 63.7K Pulls 17 Tags Updated 1 year ago](https://ollama.com/library/medllama2)
*   [nous-hermes2-mixtral -------------------- The Nous Hermes 2 model from Nous Research, now trained over Mixtral. 8x7b 62.5K Pulls 18 Tags Updated 7 months ago](https://ollama.com/library/nous-hermes2-mixtral)
*   [stable-beluga ------------- Llama 2 based model fine tuned on an Orca-style dataset. Originally called Free Willy. 7b 13b 70b 62K Pulls 49 Tags Updated 1 year ago](https://ollama.com/library/stable-beluga)
*   [granite3-moe ------------ The IBM Granite 1B and 3B models are the first mixture of experts (MoE) Granite models from IBM designed for low latency usage. tools 1b 3b 59.9K Pulls 33 Tags Updated 8 months ago](https://ollama.com/library/granite3-moe)
*   [meditron -------- Open-source medical large language model adapted from Llama 2 to the medical domain. 7b 70b 58.5K Pulls 22 Tags Updated 1 year ago](https://ollama.com/library/meditron)
*   [deepseek-v2.5 ------------- An upgraded version of DeekSeek-V2 that integrates the general and coding abilities of both DeepSeek-V2-Chat and DeepSeek-Coder-V2-Instruct. 236b 56.9K Pulls 7 Tags Updated 10 months ago](https://ollama.com/library/deepseek-v2.5)
*   [r1-1776 ------- A version of the DeepSeek-R1 model that has been post trained to provide unbiased, accurate, and factual information by Perplexity. 70b 671b 50.9K Pulls 9 Tags Updated 4 months ago](https://ollama.com/library/r1-1776)
*   [reader-lm --------- A series of models that convert HTML content to Markdown content, which is useful for content conversion tasks. 0.5b 1.5b 50.7K Pulls 33 Tags Updated 10 months ago](https://ollama.com/library/reader-lm)
*   [granite3.1-moe -------------- The IBM Granite 1B and 3B models are long-context mixture of experts (MoE) Granite models from IBM designed for low latency usage. tools 1b 3b 50.3K Pulls 33 Tags Updated 6 months ago](https://ollama.com/library/granite3.1-moe)
*   [llama-pro --------- An expansion of Llama 2 that specializes in integrating both general language understanding and domain-specific knowledge, particularly in programming and mathematics. 49K Pulls 33 Tags Updated 1 year ago](https://ollama.com/library/llama-pro)
*   [yarn-mistral ------------ An extension of Mistral to support context windows of 64K or 128K. 7b 47.8K Pulls 33 Tags Updated 1 year ago](https://ollama.com/library/yarn-mistral)
*   [nexusraven ---------- Nexus Raven is a 13B instruction tuned model for function calling tasks. 13b 44.7K Pulls 32 Tags Updated 1 year ago](https://ollama.com/library/nexusraven)
*   [shieldgemma ----------- ShieldGemma is set of instruction tuned models for evaluating the safety of text prompt input and text output responses against a set of defined safety policies. 2b 9b 27b 44.5K Pulls 49 Tags Updated 9 months ago](https://ollama.com/library/shieldgemma)
*   [command-r7b ----------- The smallest model in Cohere's R series delivers top-tier speed, efficiency, and quality to build powerful AI applications on commodity GPUs and edge devices. tools 7b 43.4K Pulls 5 Tags Updated 6 months ago](https://ollama.com/library/command-r7b)
*   [mathstral --------- MathΣtral: a 7B model designed for math reasoning and scientific discovery by Mistral AI. 7b 43.2K Pulls 17 Tags Updated 1 year ago](https://ollama.com/library/mathstral)
*   [everythinglm ------------ Uncensored Llama2 based model with support for a 16K context window. 13b 43K Pulls 18 Tags Updated 1 year ago](https://ollama.com/library/everythinglm)
*   [codeup ------ Great code generation model based on Llama2. 13b 42.7K Pulls 19 Tags Updated 1 year ago](https://ollama.com/library/codeup)
*   [marco-o1 -------- An open large reasoning model for real-world solutions by the Alibaba International Digital Commerce Group (AIDC-AI). 7b 41.3K Pulls 5 Tags Updated 7 months ago](https://ollama.com/library/marco-o1)
*   [stablelm-zephyr --------------- A lightweight chat model allowing accurate, and responsive output without requiring high-end hardware. 3b 41K Pulls 17 Tags Updated 1 year ago](https://ollama.com/library/stablelm-zephyr)
*   [solar-pro --------- Solar Pro Preview: an advanced large language model (LLM) with 22 billion parameters designed to fit into a single GPU 22b 38.7K Pulls 18 Tags Updated 10 months ago](https://ollama.com/library/solar-pro)
*   [duckdb-nsql ----------- 7B parameter text-to-SQL model made by MotherDuck and Numbers Station. 7b 36.8K Pulls 17 Tags Updated 1 year ago](https://ollama.com/library/duckdb-nsql)
*   [falcon2 ------- Falcon2 is an 11B parameters causal decoder-only model built by TII and trained over 5T tokens. 11b 36.6K Pulls 17 Tags Updated 1 year ago](https://ollama.com/library/falcon2)
*   [magicoder --------- 🎩 Magicoder is a family of 7B parameter models trained on 75K synthetic instruction data using OSS-Instruct, a novel approach to enlightening LLMs with open-source code snippets. 7b 35.2K Pulls 18 Tags Updated 1 year ago](https://ollama.com/library/magicoder)
*   [mistrallite ----------- MistralLite is a fine-tuned model based on Mistral with enhanced capabilities of processing long contexts. 7b 34.8K Pulls 17 Tags Updated 1 year ago](https://ollama.com/library/mistrallite)
*   [codebooga --------- A high-performing code instruct model created by merging two existing code models. 34b 34K Pulls 16 Tags Updated 1 year ago](https://ollama.com/library/codebooga)
*   [phi4-mini-reasoning ------------------- Phi 4 mini reasoning is a lightweight open model that balances efficiency with advanced reasoning ability. 3.8b 33.5K Pulls 5 Tags Updated 2 months ago](https://ollama.com/library/phi4-mini-reasoning)
*   [bespoke-minicheck ----------------- A state-of-the-art fact-checking model developed by Bespoke Labs. 7b 32.3K Pulls 17 Tags Updated 10 months ago](https://ollama.com/library/bespoke-minicheck)
*   [tulu3 ----- Tülu 3 is a leading instruction following model family, offering fully open-source data, code, and recipes by the The Allen Institute for AI. 8b 70b 32.2K Pulls 9 Tags Updated 7 months ago](https://ollama.com/library/tulu3)
*   [mistral-small3.2 ---------------- An update to Mistral Small that improves on function calling, instruction following, and less repetition errors. vision tools 24b 32.2K Pulls 5 Tags Updated 4 weeks ago](https://ollama.com/library/mistral-small3.2)
*   [wizard-vicuna ------------- Wizard Vicuna is a 13B parameter model based on Llama 2 trained by MelodysDreamj. 13b 32K Pulls 17 Tags Updated 1 year ago](https://ollama.com/library/wizard-vicuna)
*   [nuextract --------- A 3.8B model fine-tuned on a private high-quality synthetic dataset for information extraction, based on Phi-3. 3.8b 31.1K Pulls 17 Tags Updated 12 months ago](https://ollama.com/library/nuextract)
*   [granite3-guardian ----------------- The IBM Granite Guardian 3.0 2B and 8B models are designed to detect risks in prompts and/or responses. 2b 8b 27.2K Pulls 10 Tags Updated 8 months ago](https://ollama.com/library/granite3-guardian)
*   [megadolphin ----------- MegaDolphin-2.2-120b is a transformation of Dolphin-2.2-70b created by interleaving the model with itself. 120b 27.1K Pulls 19 Tags Updated 1 year ago](https://ollama.com/library/megadolphin)
*   [notux ----- A top-performing mixture of experts model, fine-tuned with high-quality data. 8x7b 26.5K Pulls 18 Tags Updated 1 year ago](https://ollama.com/library/notux)
*   [open-orca-platypus2 ------------------- Merge of the Open Orca OpenChat model and the Garage-bAInd Platypus 2 model. Designed for chat and code generation. 13b 26K Pulls 17 Tags Updated 1 year ago](https://ollama.com/library/open-orca-platypus2)
*   [notus ----- A 7B chat model fine-tuned with high-quality data and based on Zephyr. 7b 25.6K Pulls 18 Tags Updated 1 year ago](https://ollama.com/library/notus)
*   [goliath ------- A language model created by combining two fine-tuned Llama 2 70B models into one. 24.5K Pulls 16 Tags Updated 1 year ago](https://ollama.com/library/goliath)
*   [sailor2 ------- Sailor2 are multilingual language models made for South-East Asia. Available in 1B, 8B, and 20B parameter sizes. 1b 8b 20b 23.7K Pulls 13 Tags Updated 7 months ago](https://ollama.com/library/sailor2)
*   [firefunction-v2 --------------- An open weights function calling model based on Llama 3, competitive with GPT-4o function calling capabilities. tools 70b 23.4K Pulls 17 Tags Updated 1 year ago](https://ollama.com/library/firefunction-v2)
*   [command-a --------- 111 billion parameter model optimized for demanding enterprises that require fast, secure, and high-quality AI tools 111b 21.8K Pulls 5 Tags Updated 4 months ago](https://ollama.com/library/command-a)
*   [alfred ------ A robust conversational model designed to be used for both chat and instruct use cases. 40b 18K Pulls 7 Tags Updated 1 year ago](https://ollama.com/library/alfred)
*   [command-r7b-arabic ------------------ A new state-of-the-art version of the lightweight Command R7B model that excels in advanced Arabic language capabilities for enterprises in the Middle East and Northern Africa. tools 7b 8,288 Pulls 5 Tags Updated 4 months ago](https://ollama.com/library/command-r7b-arabic)
