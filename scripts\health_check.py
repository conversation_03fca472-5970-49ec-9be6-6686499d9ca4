#!/usr/bin/env python3
"""
AI-Powered Dashboard Health Monitoring Script

Performs comprehensive health checks on all system components:
- Backend API health
- Frontend accessibility  
- Database connectivity
- Ollama embeddings service
- WebSocket functionality
- Performance metrics

Usage:
    python scripts/health_check.py
    python scripts/health_check.py --json
    python scripts/health_check.py --continuous --interval 30
"""

import asyncio
import json
import time
import argparse
import aiohttp
import websockets
import sqlite3
from datetime import datetime
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

@dataclass
class HealthStatus:
    """Health check result for a service"""
    service: str
    status: str  # 'healthy', 'unhealthy', 'unknown'
    response_time: Optional[float]
    details: Dict[str, Any]
    timestamp: str
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

class HealthChecker:
    """Comprehensive health monitoring for AI-powered dashboard"""
    
    def __init__(self):
        self.results: Dict[str, HealthStatus] = {}
        self.session: Optional[aiohttp.ClientSession] = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=10))
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def check_backend(self) -> HealthStatus:
        """Check Backend API health"""
        start_time = time.time()
        
        try:
            async with self.session.get('http://localhost:8000/health') as response:
                response_time = time.time() - start_time
                
                if response.status == 200:
                    data = await response.json()
                    return HealthStatus(
                        service='backend',
                        status='healthy',
                        response_time=response_time,
                        details={
                            'status_code': response.status,
                            'response_data': data,
                            'version': data.get('version', 'unknown')
                        },
                        timestamp=datetime.utcnow().isoformat()
                    )
                else:
                    return HealthStatus(
                        service='backend',
                        status='unhealthy',
                        response_time=response_time,
                        details={
                            'status_code': response.status,
                            'error': 'HTTP error'
                        },
                        timestamp=datetime.utcnow().isoformat()
                    )
                    
        except Exception as e:
            response_time = time.time() - start_time
            return HealthStatus(
                service='backend',
                status='unhealthy',
                response_time=response_time,
                details={
                    'error': str(e),
                    'error_type': type(e).__name__
                },
                timestamp=datetime.utcnow().isoformat()
            )
    
    async def check_frontend(self) -> HealthStatus:
        """Check Frontend accessibility"""
        start_time = time.time()
        
        try:
            async with self.session.get('http://localhost:3000') as response:
                response_time = time.time() - start_time
                
                if response.status == 200:
                    content = await response.text()
                    return HealthStatus(
                        service='frontend',
                        status='healthy',
                        response_time=response_time,
                        details={
                            'status_code': response.status,
                            'content_length': len(content),
                            'has_app_element': 'id="root"' in content or 'id="app"' in content
                        },
                        timestamp=datetime.utcnow().isoformat()
                    )
                else:
                    return HealthStatus(
                        service='frontend',
                        status='unhealthy',
                        response_time=response_time,
                        details={
                            'status_code': response.status,
                            'error': 'HTTP error'
                        },
                        timestamp=datetime.utcnow().isoformat()
                    )
                    
        except Exception as e:
            response_time = time.time() - start_time
            return HealthStatus(
                service='frontend',
                status='unhealthy', 
                response_time=response_time,
                details={
                    'error': str(e),
                    'error_type': type(e).__name__
                },
                timestamp=datetime.utcnow().isoformat()
            )
    
    async def check_database(self) -> HealthStatus:
        """Check Database connectivity"""
        start_time = time.time()
        
        try:
            # Check if database file exists
            db_path = 'data/dashboard.db'
            if not os.path.exists(db_path):
                return HealthStatus(
                    service='database',
                    status='unhealthy',
                    response_time=0,
                    details={'error': 'Database file not found'},
                    timestamp=datetime.utcnow().isoformat()
                )
            
            # Test connection and basic query
            conn = sqlite3.connect(db_path, timeout=5.0)
            cursor = conn.cursor()
            
            # Check journal mode (should be WAL for performance)
            cursor.execute("PRAGMA journal_mode;")
            journal_mode = cursor.fetchone()[0]
            
            # Check tables exist
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = [row[0] for row in cursor.fetchall()]
            
            # Get database size
            db_size = os.path.getsize(db_path)
            
            conn.close()
            response_time = time.time() - start_time
            
            return HealthStatus(
                service='database',
                status='healthy',
                response_time=response_time,
                details={
                    'journal_mode': journal_mode,
                    'tables_count': len(tables),
                    'tables': tables,
                    'db_size_bytes': db_size,
                    'db_path': db_path
                },
                timestamp=datetime.utcnow().isoformat()
            )
            
        except Exception as e:
            response_time = time.time() - start_time
            return HealthStatus(
                service='database',
                status='unhealthy',
                response_time=response_time,
                details={
                    'error': str(e),
                    'error_type': type(e).__name__
                },
                timestamp=datetime.utcnow().isoformat()
            )
    
    async def check_ollama(self) -> HealthStatus:
        """Check Ollama embeddings service"""
        start_time = time.time()
        
        try:
            # Check API availability
            async with self.session.get('http://localhost:11434/api/tags') as response:
                if response.status != 200:
                    response_time = time.time() - start_time
                    return HealthStatus(
                        service='ollama',
                        status='unhealthy',
                        response_time=response_time,
                        details={
                            'status_code': response.status,
                            'error': 'API not accessible'
                        },
                        timestamp=datetime.utcnow().isoformat()
                    )
                
                models_data = await response.json()
                models = [model['name'] for model in models_data.get('models', [])]
                
                # Check if required embedding model is available
                embedding_model_available = 'nomic-embed-text:latest' in models
                
                response_time = time.time() - start_time
                
                return HealthStatus(
                    service='ollama',
                    status='healthy' if embedding_model_available else 'unhealthy',
                    response_time=response_time,
                    details={
                        'status_code': response.status,
                        'models_count': len(models),
                        'available_models': models,
                        'embedding_model_ready': embedding_model_available,
                        'required_model': 'nomic-embed-text'
                    },
                    timestamp=datetime.utcnow().isoformat()
                )
                
        except Exception as e:
            response_time = time.time() - start_time
            return HealthStatus(
                service='ollama',
                status='unhealthy',
                response_time=response_time,
                details={
                    'error': str(e),
                    'error_type': type(e).__name__
                },
                timestamp=datetime.utcnow().isoformat()
            )
    
    async def check_websocket(self) -> HealthStatus:
        """Check WebSocket functionality"""
        start_time = time.time()
        
        try:
            # Test WebSocket connection
            async with websockets.connect('ws://localhost:8000/ws') as websocket:
                # Send test message
                test_message = {
                    "type": "test",
                    "payload": {"message": "health_check"}
                }
                await websocket.send(json.dumps(test_message))
                
                # Wait for response (with timeout)
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    response_data = json.loads(response)
                    response_time = time.time() - start_time
                    
                    return HealthStatus(
                        service='websocket',
                        status='healthy',
                        response_time=response_time,
                        details={
                            'connection_successful': True,
                            'message_sent': True,
                            'response_received': True,
                            'response_data': response_data
                        },
                        timestamp=datetime.utcnow().isoformat()
                    )
                    
                except asyncio.TimeoutError:
                    response_time = time.time() - start_time
                    return HealthStatus(
                        service='websocket',
                        status='unhealthy',
                        response_time=response_time,
                        details={
                            'connection_successful': True,
                            'message_sent': True,
                            'response_received': False,
                            'error': 'Response timeout'
                        },
                        timestamp=datetime.utcnow().isoformat()
                    )
                    
        except Exception as e:
            response_time = time.time() - start_time
            return HealthStatus(
                service='websocket',
                status='unhealthy',
                response_time=response_time,
                details={
                    'error': str(e),
                    'error_type': type(e).__name__
                },
                timestamp=datetime.utcnow().isoformat()
            )
    
    async def run_all_checks(self) -> Dict[str, HealthStatus]:
        """Run all health checks concurrently"""
        checks = [
            self.check_backend(),
            self.check_frontend(),  
            self.check_database(),
            self.check_ollama(),
            self.check_websocket()
        ]
        
        results = await asyncio.gather(*checks, return_exceptions=True)
        
        for result in results:
            if isinstance(result, Exception):
                # Handle unexpected errors
                error_status = HealthStatus(
                    service='unknown',
                    status='unhealthy',
                    response_time=0,
                    details={
                        'error': str(result),
                        'error_type': type(result).__name__
                    },
                    timestamp=datetime.utcnow().isoformat()
                )
                self.results['error'] = error_status
            else:
                self.results[result.service] = result
                
        return self.results
    
    def print_results(self, json_output: bool = False):
        """Print health check results"""
        if json_output:
            output = {
                'timestamp': datetime.utcnow().isoformat(),
                'overall_status': self.get_overall_status(),
                'services': {k: v.to_dict() for k, v in self.results.items()}
            }
            print(json.dumps(output, indent=2))
            return
            
        # Human readable output
        print("\n" + "="*60)
        print("🏥 AI-Powered Dashboard Health Check Report")
        print("="*60)
        print(f"⏰ Timestamp: {datetime.utcnow().isoformat()}")
        print(f"🎯 Overall Status: {self.get_overall_status()}")
        print("-"*60)
        
        for service, status in self.results.items():
            status_emoji = "✅" if status.status == "healthy" else "❌"
            response_time = f"{status.response_time:.3f}s" if status.response_time else "N/A"
            
            print(f"{status_emoji} {service.upper()}: {status.status.upper()} ({response_time})")
            
            # Print key details
            if status.details:
                for key, value in status.details.items():
                    if key in ['error', 'status_code', 'models_count', 'tables_count']:
                        print(f"   📋 {key}: {value}")
                        
        print("-"*60)
        print("📊 Summary:")
        healthy_count = sum(1 for s in self.results.values() if s.status == 'healthy')
        total_count = len(self.results)
        print(f"   Healthy Services: {healthy_count}/{total_count}")
        print(f"   Success Rate: {(healthy_count/total_count*100):.1f}%")
        print("="*60)
    
    def get_overall_status(self) -> str:
        """Get overall system health status"""
        if not self.results:
            return "unknown"
            
        unhealthy_services = [s for s in self.results.values() if s.status != 'healthy']
        
        if not unhealthy_services:
            return "healthy"
        elif len(unhealthy_services) == len(self.results):
            return "critical"
        else:
            return "degraded"

async def main():
    parser = argparse.ArgumentParser(description='AI-Powered Dashboard Health Checker')
    parser.add_argument('--json', action='store_true', help='Output results in JSON format')
    parser.add_argument('--continuous', action='store_true', help='Run health checks continuously')
    parser.add_argument('--interval', type=int, default=30, help='Interval between checks in continuous mode (seconds)')
    
    args = parser.parse_args()
    
    async with HealthChecker() as checker:
        if args.continuous:
            print(f"🔄 Running continuous health checks (interval: {args.interval}s)")
            print("Press Ctrl+C to stop")
            
            try:
                while True:
                    await checker.run_all_checks()
                    checker.print_results(args.json)
                    
                    if not args.json:
                        print(f"\n⏳ Waiting {args.interval} seconds for next check...")
                    
                    await asyncio.sleep(args.interval)
                    
            except KeyboardInterrupt:
                print("\n👋 Health monitoring stopped")
        else:
            # Single run
            await checker.run_all_checks()
            checker.print_results(args.json)
            
            # Exit with error code if system is not healthy
            overall_status = checker.get_overall_status()
            if overall_status in ['unhealthy', 'critical']:
                sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
