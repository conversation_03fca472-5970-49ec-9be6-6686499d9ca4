Title: Motion (prev Framer Motion) — Animation made simple for React, JS and Vue

URL Source: https://www.framer.com/motion/

Markdown Content:
Motion (prev Framer Motion) — Animation made simple for React, JS and Vue

===============

[](https://www.framer.com/motion/)

[Docs](https://www.framer.com/motion/docs)

[Examples](https://www.framer.com/motion/examples)

![Image 4: icon entry point for Site Search](https://framerusercontent.com/images/LcSrauRN6S5dbcfiUyHSBISkE.svg)

[Motion+](https://www.framer.com/motion/plus)

[](https://www.framer.com/motion/)

Motion
======

A powerful animation library and developer tools for making beautiful animations.

[![Image 5: JS logo](https://framerusercontent.com/images/CLTl0j0riTmlT5x4EZ9ZmJDDRA.png) JavaScript](https://www.framer.com/motion/docs/quick-start)

[![Image 6: React logo](https://framerusercontent.com/images/oyA2TFsGGza1axq55RcRBHlnLYA.png) React](https://www.framer.com/motion/docs/react)

[![Image 7: Vue logo](https://framerusercontent.com/images/2kGcEFcZWLohHflOwfzA0nsk3g.png) Vue](https://www.framer.com/motion/docs/vue)

Free and

open-source

Easy to use

Production-ready

Hybrid engine

Robot friendly

Tiny footprint

![Image 8](https://framerusercontent.com/images/lyeMp4g3kXbh4joTBITbB07M5o.jpg?scale-down-to=1024&lossless=1)

Features

Powerful animations
-------------------

C r e a t e s m o o t h,h i g h-p e r f o r m a n c e a n i m a t i o n s w i t h M o t i o n’s e a s y-t o-u s e A P I—f r o m s i m p l e t r a n s f o r m s t o a d v a n c e d i n t e r a c t i v e g e s t u r e s.

##### Simple

[![Image 9](https://framerusercontent.com/images/CLTl0j0riTmlT5x4EZ9ZmJDDRA.png)](https://examples.motion.dev/js/options?utm_source=homepage-header)[![Image 10](https://framerusercontent.com/images/oyA2TFsGGza1axq55RcRBHlnLYA.png)](https://examples.motion.dev/react/enter-animation?utm_source=homepage-header)[![Image 11](https://framerusercontent.com/images/GWrxs3CewblWm4PmKWd5dcLzVfA.png)](https://examples.motion.dev/vue/enter-animation?utm_source=homepage-header?utm_source=homepage-header)

##### Transforms

[![Image 12](https://framerusercontent.com/images/CLTl0j0riTmlT5x4EZ9ZmJDDRA.png)](https://examples.motion.dev/js/rotate?utm_source=homepage-header)[![Image 13](https://framerusercontent.com/images/oyA2TFsGGza1axq55RcRBHlnLYA.png)](https://examples.motion.dev/react/rotate?utm_source=homepage-header)[![Image 14](https://framerusercontent.com/images/GWrxs3CewblWm4PmKWd5dcLzVfA.png)](https://examples.motion.dev/vue/rotate?utm_source=homepage-header)

##### Scroll

[![Image 15](https://framerusercontent.com/images/CLTl0j0riTmlT5x4EZ9ZmJDDRA.png)](https://examples.motion.dev/js/scroll-pinning?utm_source=homepage-header)[![Image 16](https://framerusercontent.com/images/oyA2TFsGGza1axq55RcRBHlnLYA.png)](https://examples.motion.dev/react/scroll-linked?utm_source=homepage-header)[![Image 17](https://framerusercontent.com/images/GWrxs3CewblWm4PmKWd5dcLzVfA.png)](https://examples.motion.dev/vue/scroll-linked?utm_source=homepage-header)

##### Exit animations

[![Image 18](https://framerusercontent.com/images/oyA2TFsGGza1axq55RcRBHlnLYA.png)](https://examples.motion.dev/react/exit-animation?utm_source=homepage-header)[![Image 19](https://framerusercontent.com/images/GWrxs3CewblWm4PmKWd5dcLzVfA.png)](https://examples.motion.dev/vue/exit-animation?utm_source=homepage-header)

##### Springs

[![Image 20](https://framerusercontent.com/images/CLTl0j0riTmlT5x4EZ9ZmJDDRA.png)](https://examples.motion.dev/js/spring-follow-cursor?utm_source=homepage-header)[![Image 21](https://framerusercontent.com/images/oyA2TFsGGza1axq55RcRBHlnLYA.png)](https://examples.motion.dev/react/follow-pointer-with-spring?utm_source=homepage-header)[![Image 22](https://framerusercontent.com/images/GWrxs3CewblWm4PmKWd5dcLzVfA.png)](https://examples.motion.dev/vue/follow-pointer-with-spring?utm_source=homepage-header)

![Image 23](https://framerusercontent.com/images/gKHGJy8cUfCDFgFPOpBjYP4Ab8.png)

##### Gestures

[![Image 24](https://framerusercontent.com/images/CLTl0j0riTmlT5x4EZ9ZmJDDRA.png)](https://examples.motion.dev/js/gestures?utm_source=homepage-header)[![Image 25](https://framerusercontent.com/images/oyA2TFsGGza1axq55RcRBHlnLYA.png)](https://examples.motion.dev/react/gestures?utm_source=homepage-header)[![Image 26](https://framerusercontent.com/images/GWrxs3CewblWm4PmKWd5dcLzVfA.png)](https://examples.motion.dev/react/enter-animation?utm_source=homepage-header)

##### Layout animations

[![Image 27](https://framerusercontent.com/images/CLTl0j0riTmlT5x4EZ9ZmJDDRA.png)](https://examples.motion.dev/js/lightbox?utm_source=homepage-header)[![Image 28](https://framerusercontent.com/images/oyA2TFsGGza1axq55RcRBHlnLYA.png)](https://examples.motion.dev/react/layout-animation?utm_source=homepage-header)[![Image 29](https://framerusercontent.com/images/GWrxs3CewblWm4PmKWd5dcLzVfA.png)](https://examples.motion.dev/js/stagger?utm_source=homepage-header)

##### Sequencing

[![Image 30](https://framerusercontent.com/images/CLTl0j0riTmlT5x4EZ9ZmJDDRA.png)](https://examples.motion.dev/js/options?utm_source=homepage-header)[![Image 31](https://framerusercontent.com/images/oyA2TFsGGza1axq55RcRBHlnLYA.png)](https://examples.motion.dev/react/variants?utm_source=homepage-header)[![Image 32](https://framerusercontent.com/images/GWrxs3CewblWm4PmKWd5dcLzVfA.png)](https://examples.motion.dev/vue/variants?utm_source=homepage-header)

[Explore all examples](https://examples.motion.dev/)

Examples

Learn by doing
--------------

Explore Motion fundamentals with straightforward examples built with copy/pastable code.

Loading...

[See all examples](https://www.framer.com/motion/examples)

[![Image 33](https://framerusercontent.com/images/fB5ndRmaogqBGd95dRfoPSPME.png) Typewriter](https://examples.motion.dev/react/typewriter)[![Image 34](https://framerusercontent.com/images/1XtpjhXXQxr4URL1OepItZTbcGk.png) Tilt card](https://examples.motion.dev/react/tilt-card)[![Image 35](https://framerusercontent.com/images/bDACiRM0j0DUJiuXnWSHiMBPc.png) Tab select](https://examples.motion.dev/react/tab-select)[![Image 36](https://framerusercontent.com/images/a8WyQtfFVFm9X08khXasRotJfsg.png) Scroll highlight](https://examples.motion.dev/react/scroll-highlight)[![Image 37](https://framerusercontent.com/images/o7HO0bPDj5Y4yVCmEnx1ZrD3J0.png) Reveal text effect](https://examples.motion.dev/react/text-reveal)[![Image 38](https://framerusercontent.com/images/pYfgeJWYpWU61wno64h4cnv3uI.png) Price switcher](https://examples.motion.dev/react/number-price-switcher)

Sponsors

Motion is supported by the finest in the industry
-------------------------------------------------

###### Partner

[](https://framer.link/Kyr6Vpy)

###### Platinum sponsors

[](https://linear.app/)

[Emil Kowalski](https://emilkowal.ski/)

[](https://figma.com/)

2 spots left

[Become a platinum sponsor](https://www.framer.com/motion/sponsor)

###### gold sponsors

[](https://tailwindcss.com/)

[](https://liveblocks.io/)

[](https://vercel.com/)

[](https://lu.ma/)

4 spots left

[Become a gold sponsor](https://www.framer.com/motion/sponsor)

courses

Learn from experts
------------------

L e a r n M o t i o n f o r R e a c t w i t h i n t e r a c t i v e c o u r s e s a n d e x p e r t-l e d v i d e o s.

[##### Animations on the Web Emil Kowalski ![Image 39](https://framerusercontent.com/images/K5Y6wRbQ7U4XGfRYt9gthQKzA.png?scale-down-to=1024)](https://animations.dev/)

[##### Crafting Beautiful Experiences with Motion Jeroen Reumkens ![Image 40](https://framerusercontent.com/images/K5Y6wRbQ7U4XGfRYt9gthQKzA.png?scale-down-to=1024)](https://www.frontend.fyi/course/motion)

[Explore all courses](https://www.framer.com/motion/docs/react-courses)

Level up your animations with Motion+
-------------------------------------

Access to 100+ premium examples, exclusive APIs like [Cursor](https://www.framer.com/motion/docs/cursor), private Discord and GitHub, and powerful VS Code animation editing tools.

One-time payment, lifetime updates.

![Image 41](https://framerusercontent.com/images/dvcUQX74Mh8wmjKmhIoM2Yli4.png?scale-down-to=1024)

[Learn more](https://www.framer.com/motion/plus)

##### Stay in the loop

Subscribe for the latest news & updates.

Subscribe

[](https://www.framer.com/motion/)

###### ©2025 Motion Division Ltd.

###### Platforms

[JavaScript](https://www.framer.com/motion/docs/quick-start)

[React](https://www.framer.com/motion/)

[Vue](https://www.framer.com/motion/docs/vue)

[Studio](https://www.framer.com/motion/studio)

###### Site

[About](https://www.framer.com/motion/about)

[Blog](https://www.framer.com/motion/blog)

[Docs](https://www.framer.com/motion/docs)

[Examples](https://www.framer.com/motion/examples)

[Motion+](https://www.framer.com/motion/plus)

[Updates](https://www.framer.com/motion/updates)

###### Social

[Discord](https://motion.dev/plus)

[GitHub](https://github.com/motiondivision/motion)

[X/Twitter](https://twitter.com/mattgperry)

[Bluesky](https://bsky.app/profile/citizenofnowhe.re)
