Title: Agents | Mirascope

URL Source: https://mirascope.com/docs/mirascope/learn/agents

Markdown Content:
> **Definition**: a person who acts on behalf of another person or group

When working with Large Language Models (LLMs), an "agent" refers to an autonomous or semi-autonomous system that can act on your behalf. The core concept is the use of tools to enable the LLM to interact with its environment.

In this section we will implement a toy `Librarian` agent to demonstrate key concepts in Mirascope that will help you build agents.

If you haven't already, we recommend first reading the section on [Tools](https://mirascope.com/docs/mirascope/learn/tools)

Diagram illustrating the agent flow

State Management[](https://mirascope.com/docs/mirascope/learn/agents#state-management)
--------------------------------------------------------------------------------------

Since an agent needs to operate across multiple LLM API calls, the first concept to cover is state. The goal of providing state to the agent is to give it memory. For example, we can think of local variables as "working memory" and a database as "long-term memory".

Let's take a look at a basic chatbot (not an agent) that uses a class to maintain the chat's history:

In this example we:

*   Create a `Librarian` class with a `history` attribute.
*   Implement a private `_call` method that injects `history`.
*   Run the `_call` method in a loop, saving the history at each step.

A chatbot with memory, while more advanced, is still not an agent.

Provider-Agnostic Agent

Integrating Tools[](https://mirascope.com/docs/mirascope/learn/agents#integrating-tools)
----------------------------------------------------------------------------------------

The next concept to cover is introducing tools to our chatbot, turning it into an agent capable of acting on our behalf. The most basic agent flow is to call tools on behalf of the agent, providing them back through the chat history until the agent is ready to response to the initial query.

Let's take a look at a basic example where the `Librarian` can access the books available in the library:

In this example we:

1.   Added the `library` state to maintain the list of available books.
2.   Implemented the `_available_books` tool that returns the library as a string.
3.   Updated `_call` to give the LLM access to the tool. 
    *   We used the `tools` dynamic configuration field so the tool has access to the library through `self`.

4.   Added a `_step` method that implements a full step from user input to assistant output.
5.   For each step, we call the LLM and see if there are any tool calls. 
    *   If yes, we call the tools, collect the outputs, and insert the tool calls into the chat history. We then recursively call `_step` again with an empty user query until the LLM is done calling tools and is ready to response
    *   If no, the LLM is ready to respond and we return the response content.

Now that our chatbot is capable of using tools, we have a basic agent.

Human-In-The-Loop[](https://mirascope.com/docs/mirascope/learn/agents#human-in-the-loop)
----------------------------------------------------------------------------------------

While it would be nice to have fully autonomous agents, LLMs are far from perfect and often need assistance to ensure they continue down the right path in an agent flow.

One common and easy way to help guide LLM agents is to give the agent the ability to ask for help. This "human-in-the-loop" flow lets the agent ask for help if it determines it needs it:

Streaming[](https://mirascope.com/docs/mirascope/learn/agents#streaming)
------------------------------------------------------------------------

The previous examples print each tool call so you can see what the agent is doing before the final response; however, you still need to wait for the agent to generate its entire final response before you see the output.

Streaming can help to provide an even more real-time experience:

Next Steps[](https://mirascope.com/docs/mirascope/learn/agents#next-steps)
--------------------------------------------------------------------------

This section is just the tip of the iceberg when it comes to building agents, implementing just one type of simple agent flow. It's important to remember that "agent" is quite a general term and can mean different things for different use-cases. Mirascope's various features make building agents easier, but it will be up to you to determine the architecture that best suits your goals.

Next, we recommend taking a look at our [Agent Tutorials](https://mirascope.com/docs/mirascope/guides/agents/web-search-agent) to see examples of more complex, real-world agents.
