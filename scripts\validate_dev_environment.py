#!/usr/bin/env python3
"""
Development Environment Validation Script

Validates the complete development setup including:
- Environment variables and configuration
- Docker services and health
- API endpoints and functionality  
- Database integrity and performance
- Frontend build and functionality
- End-to-end workflow validation

Usage:
    python scripts/validate_dev_environment.py
    python scripts/validate_dev_environment.py --fix-issues
    python scripts/validate_dev_environment.py --comprehensive
"""

import asyncio
import os
import json
import subprocess
import sqlite3
import time
from pathlib import Path
from typing import Dict, List, Any, Optional
import argparse
import aiohttp
import sys

class DevEnvironmentValidator:
    """Comprehensive development environment validator"""
    
    def __init__(self, fix_issues: bool = False, comprehensive: bool = False):
        self.fix_issues = fix_issues
        self.comprehensive = comprehensive
        self.issues_found: List[Dict[str, Any]] = []
        self.validations_passed = 0
        self.validations_total = 0
        
    def log_success(self, message: str):
        """Log successful validation"""
        print(f"✅ {message}")
        self.validations_passed += 1
        
    def log_warning(self, message: str, fixable: bool = False):
        """Log warning"""
        fix_msg = " (auto-fixable)" if fixable and self.fix_issues else ""
        print(f"⚠️  {message}{fix_msg}")
        
    def log_error(self, message: str, fixable: bool = False):
        """Log error and track issue"""
        fix_msg = " (auto-fixable)" if fixable and self.fix_issues else ""
        print(f"❌ {message}{fix_msg}")
        self.issues_found.append({
            'type': 'error',
            'message': message,
            'fixable': fixable
        })
        
    def log_info(self, message: str):
        """Log informational message"""
        print(f"ℹ️  {message}")
        
    async def validate_environment_variables(self) -> bool:
        """Validate required environment variables"""
        self.log_info("Validating environment variables...")
        self.validations_total += 1
        
        required_vars = {
            'OPENROUTER_API_KEY': 'OpenRouter API key for LLM calls',
            'LANGSEARCH_API_KEY': 'LangSearch API key for web search'
        }
        
        optional_vars = {
            'DATABASE_URL': 'Database connection string',
            'OLLAMA_URL': 'Ollama API endpoint',
            'LOG_LEVEL': 'Application log level'
        }
        
        # Check .env file exists
        env_file = Path('.env')
        if not env_file.exists():
            self.log_error("Missing .env file", fixable=True)
            if self.fix_issues and Path('.env.example').exists():
                subprocess.run(['cp', '.env.example', '.env'], check=True)
                self.log_info("Created .env from .env.example template")
            return False
            
        # Load environment variables from .env
        env_vars = {}
        with open('.env', 'r') as f:
            for line in f:
                if '=' in line and not line.strip().startswith('#'):
                    key, value = line.strip().split('=', 1)
                    env_vars[key] = value
        
        all_valid = True
        
        # Check required variables
        for var, description in required_vars.items():
            if var not in env_vars or not env_vars[var] or 'your-' in env_vars[var].lower():
                self.log_error(f"Missing or invalid {var} ({description})")
                all_valid = False
            else:
                self.log_success(f"{var} is configured")
                
        # Check optional variables
        for var, description in optional_vars.items():
            if var in env_vars and env_vars[var]:
                self.log_success(f"{var} is configured: {env_vars[var]}")
            else:
                self.log_warning(f"Optional {var} not set ({description})")
        
        if all_valid:
            self.log_success("Environment variables validation passed")
            
        return all_valid
    
    async def validate_docker_setup(self) -> bool:
        """Validate Docker setup and services"""
        self.log_info("Validating Docker setup...")
        self.validations_total += 2
        
        # Check Docker is installed
        try:
            result = subprocess.run(['docker', '--version'], 
                                  capture_output=True, text=True, check=True)
            self.log_success(f"Docker is installed: {result.stdout.strip()}")
        except (subprocess.CalledProcessError, FileNotFoundError):
            self.log_error("Docker is not installed or not accessible")
            return False
            
        # Check Docker Compose is available
        try:
            result = subprocess.run(['docker-compose', '--version'], 
                                  capture_output=True, text=True, check=True)
            self.log_success(f"Docker Compose is available: {result.stdout.strip()}")
            self.validations_passed += 1
        except (subprocess.CalledProcessError, FileNotFoundError):
            self.log_error("Docker Compose is not installed or not accessible")
            return False
            
        # Check docker-compose.yml exists
        compose_file = Path('docker-compose.yml')
        if not compose_file.exists():
            self.log_error("Missing docker-compose.yml file")
            return False
        else:
            self.log_success("docker-compose.yml exists")
            
        return True
    
    async def validate_project_structure(self) -> bool:
        """Validate project directory structure"""
        self.log_info("Validating project structure...")
        self.validations_total += 1
        
        required_dirs = [
            'frontend',
            'backend',
            'backend/app',
            'backend/app/agents',
            'backend/app/tools',
            'backend/app/models',
            'docker',
            'tests',
            'data'
        ]
        
        required_files = [
            'frontend/package.json',
            'backend/requirements.txt',
            'backend/app/main.py',
            '.env.example',
            'README.md',
            'docker-compose.yml'
        ]
        
        missing_dirs = []
        missing_files = []
        
        for dir_path in required_dirs:
            if not Path(dir_path).exists():
                missing_dirs.append(dir_path)
                
        for file_path in required_files:
            if not Path(file_path).exists():
                missing_files.append(file_path)
        
        if missing_dirs:
            self.log_error(f"Missing directories: {', '.join(missing_dirs)}", fixable=True)
            if self.fix_issues:
                for dir_path in missing_dirs:
                    Path(dir_path).mkdir(parents=True, exist_ok=True)
                    self.log_info(f"Created directory: {dir_path}")
        
        if missing_files:
            self.log_error(f"Missing files: {', '.join(missing_files)}")
            
        if not missing_dirs and not missing_files:
            self.log_success("Project structure is complete")
            return True
            
        return False
    
    async def validate_database_setup(self) -> bool:
        """Validate database setup and integrity"""
        self.log_info("Validating database setup...")
        self.validations_total += 1
        
        db_path = Path('data/dashboard.db')
        
        # Check if data directory exists
        if not Path('data').exists():
            self.log_error("Data directory missing", fixable=True)
            if self.fix_issues:
                Path('data').mkdir(parents=True, exist_ok=True)
                self.log_info("Created data directory")
        
        # Check database file
        if not db_path.exists():
            self.log_warning("Database file doesn't exist (will be created on first run)")
            return True
            
        try:
            # Test database connection
            conn = sqlite3.connect(str(db_path), timeout=5.0)
            cursor = conn.cursor()
            
            # Check journal mode (should be WAL for performance)
            cursor.execute("PRAGMA journal_mode;")
            journal_mode = cursor.fetchone()[0]
            
            if journal_mode.upper() != 'WAL':
                self.log_warning(f"Database journal mode is {journal_mode}, should be WAL")
                if self.fix_issues:
                    cursor.execute("PRAGMA journal_mode=WAL;")
                    self.log_info("Enabled WAL mode for database")
            else:
                self.log_success("Database is using WAL mode")
            
            # Check if basic tables exist
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = [row[0] for row in cursor.fetchall()]
            
            expected_tables = ['tasks', 'events', 'user_inputs', 'embeddings']
            missing_tables = [t for t in expected_tables if t not in tables]
            
            if missing_tables:
                self.log_warning(f"Some database tables don't exist yet: {', '.join(missing_tables)}")
            else:
                self.log_success("All expected database tables exist")
            
            conn.close()
            self.log_success("Database connection test passed")
            return True
            
        except Exception as e:
            self.log_error(f"Database validation failed: {str(e)}")
            return False
    
    async def validate_frontend_setup(self) -> bool:
        """Validate frontend setup"""
        self.log_info("Validating frontend setup...")
        self.validations_total += 1
        
        # Check package.json
        package_json = Path('frontend/package.json')
        if not package_json.exists():
            self.log_error("Frontend package.json missing")
            return False
            
        try:
            with open(package_json, 'r') as f:
                package_data = json.load(f)
                
            # Check required dependencies
            required_deps = {
                'react': 'React framework',
                'framer-motion': 'Animation library',
                'typescript': 'TypeScript support'
            }
            
            dependencies = {**package_data.get('dependencies', {}), 
                           **package_data.get('devDependencies', {})}
            
            missing_deps = []
            for dep, description in required_deps.items():
                if dep not in dependencies:
                    missing_deps.append(f"{dep} ({description})")
                else:
                    self.log_success(f"✓ {dep} is installed: {dependencies[dep]}")
            
            if missing_deps:
                self.log_error(f"Missing frontend dependencies: {', '.join(missing_deps)}")
                return False
                
            # Check if node_modules exists
            node_modules = Path('frontend/node_modules')
            if not node_modules.exists():
                self.log_warning("Frontend dependencies not installed", fixable=True)
                if self.fix_issues:
                    self.log_info("Installing frontend dependencies...")
                    subprocess.run(['npm', 'install'], cwd='frontend', check=True)
                    self.log_success("Frontend dependencies installed")
            else:
                self.log_success("Frontend dependencies are installed")
                
            self.log_success("Frontend setup validation passed")
            return True
            
        except Exception as e:
            self.log_error(f"Frontend validation failed: {str(e)}")
            return False
    
    async def validate_backend_setup(self) -> bool:
        """Validate backend setup"""
        self.log_info("Validating backend setup...")
        self.validations_total += 1
        
        # Check requirements.txt
        requirements_file = Path('backend/requirements.txt')
        if not requirements_file.exists():
            self.log_error("Backend requirements.txt missing")
            return False
            
        # Check main.py
        main_file = Path('backend/app/main.py')
        if not main_file.exists():
            self.log_error("Backend main.py missing")
            return False
            
        # Check if virtual environment exists (optional)
        venv_path = Path('backend/venv')
        if not venv_path.exists():
            self.log_warning("Backend virtual environment not found")
        else:
            self.log_success("Backend virtual environment exists")
            
        # Validate Python requirements
        try:
            with open(requirements_file, 'r') as f:
                requirements = f.read()
                
            required_packages = ['fastapi', 'uvicorn', 'mirascope', 'pydantic']
            missing_packages = []
            
            for package in required_packages:
                if package not in requirements.lower():
                    missing_packages.append(package)
                else:
                    self.log_success(f"✓ {package} is in requirements")
                    
            if missing_packages:
                self.log_error(f"Missing backend packages: {', '.join(missing_packages)}")
                return False
                
            self.log_success("Backend setup validation passed")
            return True
            
        except Exception as e:
            self.log_error(f"Backend validation failed: {str(e)}")
            return False
    
    async def validate_services_health(self) -> bool:
        """Validate running services health"""
        if not self.comprehensive:
            return True
            
        self.log_info("Validating services health (comprehensive mode)...")
        self.validations_total += 3
        
        # Import and use the health checker
        from .health_check import HealthChecker
        
        async with HealthChecker() as checker:
            results = await checker.run_all_checks()
            
            healthy_services = 0
            total_services = len(results)
            
            for service, status in results.items():
                if status.status == 'healthy':
                    self.log_success(f"{service} service is healthy")
                    healthy_services += 1
                else:
                    self.log_error(f"{service} service is unhealthy: {status.details.get('error', 'Unknown error')}")
            
            # Adjust validation counts based on results
            self.validations_passed += healthy_services
            self.validations_total = self.validations_total - 3 + total_services
            
            if healthy_services == total_services:
                self.log_success("All services are healthy")
                return True
            else:
                self.log_error(f"Only {healthy_services}/{total_services} services are healthy")
                return False
    
    async def run_validation(self) -> bool:
        """Run complete validation suite"""
        start_time = time.time()
        
        print("="*70)
        print("🔍 AI-Powered Dashboard Development Environment Validation")
        print("="*70)
        
        validation_functions = [
            self.validate_environment_variables,
            self.validate_docker_setup,
            self.validate_project_structure,
            self.validate_database_setup,
            self.validate_frontend_setup,
            self.validate_backend_setup,
            self.validate_services_health
        ]
        
        results = []
        for validation_func in validation_functions:
            try:
                result = await validation_func()
                results.append(result)
            except Exception as e:
                self.log_error(f"Validation failed: {validation_func.__name__}: {str(e)}")
                results.append(False)
        
        # Summary
        elapsed_time = time.time() - start_time
        passed_validations = self.validations_passed
        total_validations = self.validations_total
        success_rate = (passed_validations / total_validations * 100) if total_validations > 0 else 0
        
        print("\n" + "="*70)
        print("📊 Validation Summary")
        print("="*70)
        print(f"⏱️  Total time: {elapsed_time:.2f} seconds")
        print(f"✅ Passed: {passed_validations}/{total_validations} ({success_rate:.1f}%)")
        print(f"❌ Issues found: {len(self.issues_found)}")
        
        if self.issues_found:
            print(f"\n🔧 Issues Summary:")
            for i, issue in enumerate(self.issues_found, 1):
                status = "auto-fixed" if issue['fixable'] and self.fix_issues else "needs attention"
                print(f"   {i}. {issue['message']} [{status}]")
                
        overall_success = all(results) and len(self.issues_found) == 0
        
        if overall_success:
            print("\n🎉 Development environment validation PASSED!")
            print("✅ Your development environment is ready to go!")
        else:
            print("\n⚠️  Development environment validation found issues")
            print("🔧 Run with --fix-issues to automatically fix fixable problems")
            print("📖 Check the README.md for manual setup instructions")
        
        print("="*70)
        
        return overall_success

async def main():
    parser = argparse.ArgumentParser(description='Validate AI-Powered Dashboard development environment')
    parser.add_argument('--fix-issues', action='store_true', help='Automatically fix fixable issues')
    parser.add_argument('--comprehensive', action='store_true', help='Run comprehensive validation including service health checks')
    
    args = parser.parse_args()
    
    validator = DevEnvironmentValidator(fix_issues=args.fix_issues, comprehensive=args.comprehensive)
    success = await validator.run_validation()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    asyncio.run(main())
