# AI-Powered Dashboard Development Monitoring Configuration
# This file defines monitoring and observability settings for local development

# Health Check Configuration
HEALTH_CHECK_INTERVAL=30  # seconds
HEALTH_CHECK_TIMEOUT=10   # seconds
HEALTH_CHECK_RETRIES=3    # number of retries before marking unhealthy

# Logging Configuration  
LOG_LEVEL=DEBUG           # DEBUG, INFO, WARNING, ERROR
LOG_FORMAT=structured     # structured, simple, json
LOG_FILE_ENABLED=true     # Enable file logging
LOG_FILE_PATH=logs/app.log
LOG_FILE_MAX_SIZE=10MB    # Maximum log file size
LOG_FILE_BACKUP_COUNT=5   # Number of backup log files

# Performance Monitoring
METRICS_ENABLED=true      # Enable Prometheus metrics
METRICS_PORT=9090         # Prometheus metrics port
RESPONSE_TIME_BUCKETS=0.01,0.05,0.1,0.25,0.5,1.0,2.5,5.0,10.0  # Response time buckets
REQUEST_RATE_WINDOW=60    # Request rate calculation window (seconds)

# Database Monitoring
DB_QUERY_LOG_ENABLED=true      # Log slow database queries
DB_SLOW_QUERY_THRESHOLD=1.0    # Log queries taking longer than this (seconds)
DB_CONNECTION_POOL_METRICS=true # Track connection pool statistics

# WebSocket Monitoring
WS_CONNECTION_METRICS=true     # Track WebSocket connections
WS_MESSAGE_METRICS=true        # Track WebSocket message counts
WS_PING_INTERVAL=30           # WebSocket ping interval (seconds)

# AI/LLM Monitoring
LLM_REQUEST_METRICS=true       # Track LLM API requests
LLM_RESPONSE_TIME_METRICS=true # Track LLM response times
LLM_TOKEN_USAGE_METRICS=true   # Track token usage
LLM_ERROR_RATE_METRICS=true    # Track LLM error rates

# Animation Performance Monitoring
ANIMATION_PERFORMANCE_METRICS=true # Track animation frame rates
FPS_MONITORING_ENABLED=true        # Monitor frontend FPS
FRAME_DROP_THRESHOLD=55             # Alert if FPS drops below this

# Alert Thresholds (Development)
CPU_ALERT_THRESHOLD=80        # CPU usage percentage
MEMORY_ALERT_THRESHOLD=80     # Memory usage percentage
DISK_ALERT_THRESHOLD=90       # Disk usage percentage
RESPONSE_TIME_ALERT=5.0       # Response time alert threshold (seconds)
ERROR_RATE_ALERT=0.05         # Error rate alert threshold (5%)

# Development-Specific Settings
HOT_RELOAD_MONITORING=true    # Monitor hot reload events
BUILD_TIME_TRACKING=true      # Track build times
DEV_TOOLS_INTEGRATION=true    # Enable browser dev tools integration
SOURCE_MAP_ANALYSIS=true      # Analyze source maps for optimization

# Docker Monitoring
DOCKER_STATS_ENABLED=true     # Enable Docker container stats
DOCKER_HEALTH_CHECKS=true     # Enable Docker health checks
CONTAINER_RESOURCE_LIMITS=true # Monitor container resource usage

# Network Monitoring
NETWORK_LATENCY_MONITORING=true    # Monitor network latency
API_ENDPOINT_MONITORING=true       # Monitor individual API endpoints
EXTERNAL_SERVICE_MONITORING=true   # Monitor external service dependencies

# Security Monitoring (Development)
SECURITY_SCAN_ENABLED=false       # Disable intensive scans in dev
VULNERABILITY_ALERTS=true         # Enable vulnerability alerts
DEPENDENCY_SCAN_ENABLED=true      # Scan dependencies for vulnerabilities

# Ollama Monitoring
OLLAMA_MODEL_METRICS=true         # Track model loading/unloading
OLLAMA_EMBEDDING_PERFORMANCE=true # Track embedding generation performance
OLLAMA_MEMORY_USAGE=true          # Monitor Ollama memory usage

# Frontend Performance Monitoring
BUNDLE_SIZE_TRACKING=true         # Track bundle size changes
LIGHTHOUSE_SCORES=false           # Disable Lighthouse in dev (too resource intensive)
WEB_VITALS_MONITORING=true        # Monitor Core Web Vitals
REACT_PROFILER_ENABLED=true       # Enable React Profiler

# Data Monitoring
DATABASE_SIZE_MONITORING=true     # Monitor database growth
EMBEDDING_INDEX_SIZE=true         # Monitor embedding index size
CACHE_HIT_RATE_MONITORING=true    # Monitor cache performance

# Development Alerts (Less aggressive than production)
SLACK_WEBHOOK_URL=                # Optional: Slack webhook for alerts
EMAIL_ALERTS_ENABLED=false        # Disable email alerts in dev
CONSOLE_ALERTS_ENABLED=true       # Show alerts in console
DESKTOP_NOTIFICATIONS=true        # Show desktop notifications for critical alerts

# Profiling
PROFILING_ENABLED=false           # Disable by default (enable for performance debugging)
PROFILING_SAMPLE_RATE=0.01        # Sample rate for profiling (1%)
PROFILING_OUTPUT_DIR=profiles/    # Directory for profiling output

# Tracing
DISTRIBUTED_TRACING=false         # Disable in local dev
REQUEST_TRACING=true              # Enable request tracing
TRACE_SAMPLING_RATE=1.0           # Sample all requests in dev

# Backup and Recovery Monitoring
BACKUP_MONITORING=false           # Disable automated backups in dev
DATA_INTEGRITY_CHECKS=true        # Enable data integrity checks
RECOVERY_TESTING=false            # Disable automated recovery testing

# Environment-Specific Overrides
ENVIRONMENT=development
DEBUG_MODE=true
VERBOSE_LOGGING=true
DEVELOPMENT_MIDDLEWARE=true

# Custom Dashboard Monitoring
AI_CATEGORIZATION_ACCURACY=true   # Track AI categorization accuracy
USER_INTERACTION_METRICS=true     # Track user interactions
VISUAL_FEEDBACK_METRICS=true      # Track animation performance
WORKFLOW_COMPLETION_RATES=true    # Track workflow success rates

# Integration Testing Monitoring
E2E_TEST_METRICS=true             # Track end-to-end test performance
UNIT_TEST_COVERAGE=true           # Monitor unit test coverage
INTEGRATION_TEST_TIMING=true      # Monitor integration test timing

# Resource Usage Monitoring
MEMORY_LEAK_DETECTION=true        # Detect memory leaks
CPU_PROFILING_TRIGGERS=true       # Enable CPU profiling triggers
DISK_IO_MONITORING=true           # Monitor disk I/O
NETWORK_IO_MONITORING=true        # Monitor network I/O

# Development Quality Metrics
CODE_COVERAGE_MONITORING=true     # Monitor code coverage changes
TECHNICAL_DEBT_TRACKING=true      # Track technical debt metrics
REFACTORING_IMPACT=true           # Measure refactoring impact
FEATURE_USAGE_ANALYTICS=true      # Track feature usage patterns
