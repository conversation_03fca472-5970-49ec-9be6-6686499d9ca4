# 🚀 AI-Powered Dashboard

> **Modern personal productivity dashboard with visual transparency and smooth animations**

A React-based dashboard where users input anything and AI intelligently categorizes it into Tasks, Events, or Questions - with complete visual feedback and smooth 60fps animations powered by Mirascope agents.

[![TypeScript](https://img.shields.io/badge/TypeScript-007ACC?style=flat&logo=typescript&logoColor=white)](https://www.typescriptlang.org/)
[![React](https://img.shields.io/badge/React-20232A?style=flat&logo=react&logoColor=61DAFB)](https://reactjs.org/)
[![FastAPI](https://img.shields.io/badge/FastAPI-005571?style=flat&logo=fastapi)](https://fastapi.tiangolo.com/)
[![Python](https://img.shields.io/badge/Python-3776AB?style=flat&logo=python&logoColor=white)](https://python.org/)
[![Docker](https://img.shields.io/badge/Docker-0db7ed?style=flat&logo=docker&logoColor=white)](https://www.docker.com/)

## ✨ Key Features

- **🧠 Pure AI Decision Making**: No keyword hardcoding - AI intelligently categorizes any input
- **� Visual Transparency**: See every AI processing step with smooth animations
- **⚡ Real-time Updates**: WebSocket-powered instant feedback and state synchronization
- **📱 Smart Components**: Calendar integration, task management, semantic search
- **🎨 60fps Animations**: Smooth spring physics using Framer Motion throughout
- **💾 State Persistence**: Data survives refresh with SQLite + embeddings
- **🔧 Production Ready**: Docker containerization with health checks

## 🎯 How It Works

1. **User Input**: Type anything into the hero input bar
2. **AI Analysis**: Mirascope agents analyze and categorize (Task/Event/Question)  
3. **Visual Feedback**: See real-time processing animations
4. **Smart Processing**: AI routes to appropriate tools (calendar/tasks/search/web)
5. **Results Display**: Smooth animations reveal categorized results
6. **Persistence**: Everything auto-saves with SQLite + semantic embeddings

## 🏗️ Architecture Overview
![Architecture Diagram](docs/architecture.md)

```
ai-powered-dashboard/
├── frontend/                 # React + TypeScript + Vite
│   ├── src/
│   │   ├── components/      # UI components with Framer Motion
│   │   ├── hooks/           # Custom React hooks
│   │   ├── services/        # API communication layer
│   │   ├── types/           # TypeScript definitions
│   │   └── utils/           # Utility functions
│   └── package.json
├── backend/                 # FastAPI + Mirascope + Python
│   ├── app/
│   │   ├── agents/          # Mirascope AI agents
│   │   ├── tools/           # AI tool implementations
│   │   ├── api/             # FastAPI routes & WebSockets
│   │   ├── models/          # Pydantic data models
│   │   ├── database/        # SQLite connection & migrations
│   │   └── config/          # Environment configuration
│   └── requirements.txt
├── docker/                  # Container orchestration
├── tests/                   # Frontend, backend, and e2e tests
├── data/                    # SQLite database storage
└── .env                     # Environment configuration
```

## 🚀 Quick Start

### Prerequisites

- **Docker** (recommended) or **Node 18+ & Python 3.11+**
- **API Keys** (free):
  - [OpenRouter](https://openrouter.ai/) for AI models
  - [LangSearch](https://langsearch.ai/) for web search

### 1. Clone & Setup

```bash
# Clone the repository
git clone <repository-url>
cd ai-powered-dashboard

# Copy environment template
cp .env.example .env
```

### 2. Configure Environment

Edit `.env` with your API keys:

```bash
# OpenRouter API Configuration (for LLM calls)
OPENROUTER_API_KEY=your-openrouter-key-here
OPENROUTER_MODEL=tngtech/deepseek-r1t2-chimera:free

# LangSearch API Configuration (for web search)  
LANGSEARCH_API_KEY=your-langsearch-key-here

# Database Configuration
DATABASE_URL=sqlite:///./data/dashboard.db

# Ollama Configuration (for embeddings)
OLLAMA_URL=http://localhost:11434
OLLAMA_MODEL=nomic-embed-text
```

### 3. Run with Docker (Recommended)

```bash
# Start all services
docker-compose up -d --build

# View logs
docker-compose logs -f

# Access the dashboard
open http://localhost:3000
```

### 4. Manual Setup (Alternative)

**Backend:**
```bash
cd backend
python -m venv venv
venv\Scripts\activate  # Windows
pip install -r requirements.txt
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

**Frontend:**
```bash
cd frontend
npm install
npm run dev
```

**Ollama (for embeddings):**
```bash
# Install Ollama from https://ollama.ai/
ollama pull nomic-embed-text
ollama serve
```

## 🎮 Usage Guide

### Basic Workflow

1. **Open Dashboard**: Navigate to `http://localhost:3000`
2. **Type Anything**: Use the hero input bar - no keywords needed!
3. **Watch AI Process**: See real-time animations as AI categorizes
4. **View Results**: AI automatically organizes into tasks, events, or answers

### Example Inputs

| Input | AI Category | Result |
|-------|-------------|--------|
| "Do math homework due tomorrow" | **Task** | Creates task with due date |
| "Dentist appointment next Friday 2pm" | **Event** | Adds calendar event |
| "What is photosynthesis?" | **Question** | Shows AI answer with sources |
| "Buy groceries and call mom" | **Tasks** | Creates multiple tasks |
| "Meeting with John about project" | **Event** | Prompts for date/time |

### Advanced Features

- **Smart Categorization**: AI learns from your patterns
- **Visual Feedback**: Every processing step is animated
- **State Persistence**: Data survives browser refresh
- **Real-time Updates**: WebSocket synchronization
- **Semantic Search**: Find tasks/events by meaning, not keywords
- **Web Integration**: Questions pull from real web search

## 🔧 Development

### Local Development

**Prerequisites:**
- Node.js 18+
- Python 3.11+
- Docker (optional but recommended)

**Hot Reload Development:**
```bash
# Terminal 1: Backend with hot reload
cd backend
python -m uvicorn app.main:app --reload --port 8000

# Terminal 2: Frontend with hot reload  
cd frontend
npm run dev

# Terminal 3: Ollama for embeddings
ollama serve
```

**Environment Variables:**
```bash
# Copy example environment
cp .env.example .env

# Required variables:
OPENROUTER_API_KEY=your-key    # Get from openrouter.ai
LANGSEARCH_API_KEY=your-key    # Get from langsearch.ai
OLLAMA_URL=http://localhost:11434
DATABASE_URL=sqlite:///./data/dashboard.db
```

### Testing

**Backend Tests:**
```bash
cd backend
pytest tests/ -v --cov=app --cov-report=term-missing
```

**Frontend Tests:**
```bash
cd frontend
npm test -- --coverage --watchAll=false
```

**Integration Tests:**
```bash
# With Docker running
cd tests/e2e
python -m pytest test_workflow.py -v
```

**Load Testing:**
```bash
# Test WebSocket performance
cd tests/performance
python test_websocket_performance.py
```

## 📡 API Reference

### Core Endpoints

**POST /api/process-input**
```json
{
  "text": "Do math homework due tomorrow",
  "timestamp": "2025-07-21T10:00:00Z"
}

Response:
{
  "category": "task",
  "confidence": 0.95,
  "result": {
    "id": "task-123",
    "title": "Do math homework", 
    "due_date": "2025-07-22T23:59:59Z",
    "priority": "medium"
  },
  "processing_steps": [
    "Analyzing input...",
    "Categorizing...", 
    "Task identified!"
  ]
}
```

**GET/POST /api/tasks**
```bash
# Get all tasks
GET /api/tasks?category=homework&status=pending

# Create task
POST /api/tasks
{
  "title": "Complete project",
  "due_date": "2025-07-25T17:00:00Z",
  "priority": "high"
}
```

**GET/POST /api/events**
```bash
# Get calendar events
GET /api/events?start_date=2025-07-21&end_date=2025-07-28

# Create event
POST /api/events
{
  "title": "Team meeting",
  "start_time": "2025-07-22T14:00:00Z",
  "duration": 60
}
```

### WebSocket API

**Connection:** `ws://localhost:8000/ws`

**Message Format:**
```json
{
  "type": "processing_step",
  "payload": {
    "step": "analyzing",
    "message": "Analyzing input...",
    "animation": "typing_indicator",
    "progress": 33
  }
}
```

**WebSocket Events:**
- `input_received` - Input submitted
- `processing_step` - Visual feedback update
- `categorization_complete` - AI categorization done
- `tool_execution` - Tool processing update
- `result_ready` - Final result available
- `error` - Processing error occurred

## 🎨 Animation System

### Visual Feedback States

Following the **mermaid diagram exactly**, animations progress through:

1. **Analyzing** (🎭): Typing indicator, analysis pulse
2. **Categorizing** (🤖): Brain animation, category highlighting  
3. **Processing** (⚡): Tool-specific animations
4. **Complete** (✨): Success celebration, result reveal

### Framer Motion Configuration

**Spring Physics:**
```typescript
const springConfig = {
  type: "spring",
  damping: 25,
  stiffness: 400,
  mass: 1
};

const fadeInUp = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  transition: springConfig
};
```

**GPU Acceleration:**
```css
.animated-element {
  transform: translateZ(0);
  will-change: transform, opacity;
  backface-visibility: hidden;
}
```

## 🔧 Configuration

### Environment Variables

**Core Configuration:**
```bash
# AI Model Configuration
OPENROUTER_API_KEY=sk-or-v1-...           # OpenRouter API key
OPENROUTER_MODEL=tngtech/deepseek-r1t2-chimera:free  # Free model
AI_FALLBACK_MODEL=google/gemini-2.0-flash-exp:free   # Backup model

# Search Configuration  
LANGSEARCH_API_KEY=sk-...                 # LangSearch API key

# Database Configuration
DATABASE_URL=sqlite:///./data/dashboard.db  # SQLite database path

# Ollama Configuration (Embeddings)
OLLAMA_URL=http://localhost:11434         # Ollama server URL
OLLAMA_MODEL=nomic-embed-text             # Embedding model

# Performance Configuration
MAX_WORKERS=4                             # Background task workers
CONNECTION_POOL_SIZE=20                   # Database connections
REQUEST_TIMEOUT=30                        # API request timeout

# Animation Configuration  
ANIMATION_DURATION_MS=300                 # Base animation duration
SPRING_TENSION=400                        # Spring physics tension
SPRING_FRICTION=25                        # Spring physics friction
```

**Development vs Production:**
```bash
# Development
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=info
CORS_ORIGINS=["http://localhost:3000"]

# Production  
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=warning
CORS_ORIGINS=["https://yourdomain.com"]
```

### Model Configuration

**Supported LLM Providers:**
- **OpenRouter** (recommended): Free tier with multiple models
- **OpenAI**: Direct API integration
- **Anthropic**: Claude models via OpenRouter

**Free Models (OpenRouter):**
```bash
# Recommended free models:
OPENROUTER_MODEL=tngtech/deepseek-r1t2-chimera:free      # Fast, good reasoning
AI_FALLBACK_MODEL=google/gemini-2.0-flash-exp:free       # Backup option

# Alternative free models:
# microsoft/phi-3.5-mini-128k-instruct:free
# qwen/qwen-2.5-72b-instruct:free  
# meta-llama/llama-3.2-11b-vision-instruct:free
```

## 🔍 Troubleshooting

### Common Issues

**❌ "OpenRouter API key not working"**
```bash
# Check API key format
echo $OPENROUTER_API_KEY  # Should start with sk-or-v1-

# Test API connection
curl -H "Authorization: Bearer $OPENROUTER_API_KEY" \
     -H "Content-Type: application/json" \
     https://openrouter.ai/api/v1/models
```

**❌ "Ollama embeddings not working"**
```bash
# Check Ollama status
ollama list

# Install embedding model if missing
ollama pull nomic-embed-text

# Test Ollama API
curl http://localhost:11434/api/embeddings \
  -d '{"model": "nomic-embed-text", "prompt": "test"}'
```

**❌ "WebSocket connections failing"**
```bash
# Check backend is running
curl http://localhost:8000/health

# Check WebSocket endpoint
websocat ws://localhost:8000/ws

# Common fix: CORS configuration
# Add frontend URL to CORS_ORIGINS in .env
```

**❌ "Animations are choppy"**
```bash
# Enable GPU acceleration in browser dev tools
# Check for console errors in frontend
# Reduce SPRING_TENSION/SPRING_FRICTION values
```

**❌ "Database errors"**
```bash
# Check database permissions
ls -la data/dashboard.db

# Reset database
rm data/dashboard.db
docker-compose restart backend

# Check SQLite WAL mode
sqlite3 data/dashboard.db "PRAGMA journal_mode;"
```

### Performance Optimization

**Frontend:**
```typescript
// Enable React strict mode in development
// Use React.memo for expensive components
// Implement proper cleanup in useEffect hooks

// Example optimization:
const TaskItem = React.memo(({ task }) => {
  const memoizedValue = useMemo(() => 
    computeExpensiveValue(task), [task.id]
  );
  
  return <div>{memoizedValue}</div>;
});
```

**Backend:**
```python
# Enable connection pooling
# Use async/await throughout
# Implement proper error handling

# Example optimization:
@lru_cache(maxsize=128)
def get_cached_embeddings(text: str):
    return generate_embeddings(text)
```

### Health Checks

**All Services:**
```bash
# Check all services
curl -f http://localhost:8000/health    # Backend
curl -f http://localhost:3000           # Frontend  
curl -f http://localhost:11434/api/tags # Ollama

# Docker health checks
docker-compose ps
```

**Database:**
```bash
# Check database connection
python -c "
import sqlite3
conn = sqlite3.connect('data/dashboard.db')
print('Tables:', [t[0] for t in conn.execute('SELECT name FROM sqlite_master WHERE type=\"table\"').fetchall()])
conn.close()
"
```

## 🚀 Production Deployment

### Docker Production Build

**Multi-stage optimized builds:**
```dockerfile
# Frontend production build
FROM node:18-alpine as frontend-build
WORKDIR /app
COPY frontend/package*.json ./
RUN npm ci --only=production
COPY frontend/ .
RUN npm run build

# Backend production  
FROM python:3.11-slim as backend
WORKDIR /app
COPY backend/requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt
COPY backend/app/ ./app/
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

**Production docker-compose:**
```yaml
# docker-compose.prod.yml
version: '3.8'
services:
  frontend:
    build:
      context: .
      dockerfile: docker/frontend.Dockerfile
    ports:
      - "80:80"
    depends_on:
      - backend
      
  backend:
    build:
      context: .
      dockerfile: docker/backend.Dockerfile
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=production
      - DEBUG=false
    volumes:
      - ./data:/app/data
      
  ollama:
    image: ollama/ollama:latest
    ports:
**Health Monitoring:**
```python
# Custom health check endpoint
@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow(),
        "services": {
            "database": check_database_connection(),
            "ollama": check_ollama_connection(),
            "openrouter": check_openrouter_api()
        }
    }
```

**Logging Configuration:**
```python
import logging
from app.config import settings

logging.basicConfig(
    level=getattr(logging, settings.log_level.upper()),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Structured logging for production
logger = logging.getLogger(__name__)
logger.info("Application started", extra={
    "environment": settings.environment,
    "version": "1.0.0"
})
```

## 🤝 Contributing

### Development Setup

1. **Fork & Clone**
```bash
git clone https://github.com/yourusername/ai-powered-dashboard.git
cd ai-powered-dashboard
```

2. **Install Pre-commit Hooks**
```bash
pip install pre-commit
pre-commit install
```

3. **Run Tests Before Committing**
```bash
# Backend tests
cd backend && pytest

# Frontend tests  
cd frontend && npm test

# E2E tests
cd tests/e2e && python -m pytest
```

### Code Style

**Python (Backend):**
- **Black** for formatting
- **isort** for import sorting
- **mypy** for type checking
- **ruff** for linting

**TypeScript (Frontend):**
- **Prettier** for formatting
- **ESLint** for linting
- **TypeScript strict mode**

### Commit Convention

```bash
# Format: type(scope): description
feat(agents): add semantic search functionality
fix(websocket): resolve connection timeout issues
docs(readme): update installation instructions
test(integration): add end-to-end workflow tests
```

## 📄 License

MIT License - see [LICENSE](LICENSE) file for details.

## 🔗 Resources & Links

### Documentation
- **[Mirascope](https://github.com/Mirascope/mirascope)** - AI agent framework
- **[OpenRouter](https://openrouter.ai/docs)** - LLM API platform
- **[Framer Motion](https://www.framer.com/motion/)** - Animation library
- **[FastAPI](https://fastapi.tiangolo.com/)** - Backend framework
- **[React](https://react.dev/)** - Frontend framework

### Community
- **Issues**: [GitHub Issues](https://github.com/yourusername/ai-powered-dashboard/issues)
- **Discussions**: [GitHub Discussions](https://github.com/yourusername/ai-powered-dashboard/discussions)
- **Discord**: [Community Discord](https://discord.gg/your-invite)

### Related Projects
- **[LangSearch](https://langsearch.ai/)** - Web search API
- **[Ollama](https://ollama.ai/)** - Local LLM & embeddings
- **[Tailwind CSS](https://tailwindcss.com/)** - Utility-first CSS

---

## 🙏 Acknowledgments

Built with modern AI-first architecture following context engineering principles. Special thanks to:

- **Mirascope** team for the excellent AI agent framework
- **OpenRouter** for democratizing access to LLM APIs  
- **React** and **FastAPI** communities for robust foundations
- **Open source** contributors making this possible

---

## 📈 Stats

![GitHub stars](https://img.shields.io/github/stars/yourusername/ai-powered-dashboard)
![GitHub forks](https://img.shields.io/github/forks/yourusername/ai-powered-dashboard)
![GitHub issues](https://img.shields.io/github/issues/yourusername/ai-powered-dashboard)
![GitHub license](https://img.shields.io/github/license/yourusername/ai-powered-dashboard)

**Made with ❤️ and AI-powered development**

---

*Last updated: July 21, 2025 - Version 1.0.0*
      - ollama_data:/root/.ollama

volumes:
  ollama_data:
```

### Nginx Configuration

**Reverse proxy with SSL:**
```nginx
server {
    listen 80;
    server_name yourdomain.com;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    location /api/ {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    location /ws {
        proxy_pass http://localhost:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
```

## 📊 Monitoring & Analytics
- Place relevant code patterns in `examples/`
- Reference specific files and patterns to follow
- Explain what aspects should be mimicked

**DOCUMENTATION**: Include all relevant resources
- API documentation URLs
- Library guides
- MCP server documentation
- Database schemas

**OTHER CONSIDERATIONS**: Capture important details
- Authentication requirements
- Rate limits or quotas
- Common pitfalls
- Performance requirements

## The PRP Workflow

### How /generate-prp Works

The command follows this process:

1. **Research Phase**
   - Analyzes your codebase for patterns
   - Searches for similar implementations
   - Identifies conventions to follow

2. **Documentation Gathering**
   - Fetches relevant API docs
   - Includes library documentation
   - Adds gotchas and quirks

3. **Blueprint Creation**
   - Creates step-by-step implementation plan
   - Includes validation gates
   - Adds test requirements

4. **Quality Check**
   - Scores confidence level (1-10)
   - Ensures all context is included

### How /execute-prp Works

1. **Load Context**: Reads the entire PRP
2. **Plan**: Creates detailed task list using TodoWrite
3. **Execute**: Implements each component
4. **Validate**: Runs tests and linting
5. **Iterate**: Fixes any issues found
6. **Complete**: Ensures all requirements met

See `PRPs/EXAMPLE_multi_agent_prp.md` for a complete example of what gets generated.

## Using Examples Effectively

The `examples/` folder is **critical** for success. AI coding assistants perform much better when they can see patterns to follow.

### What to Include in Examples

1. **Code Structure Patterns**
   - How you organize modules
   - Import conventions
   - Class/function patterns

2. **Testing Patterns**
   - Test file structure
   - Mocking approaches
   - Assertion styles

3. **Integration Patterns**
   - API client implementations
   - Database connections
   - Authentication flows

4. **CLI Patterns**
   - Argument parsing
   - Output formatting
   - Error handling

### Example Structure

```
examples/
├── README.md           # Explains what each example demonstrates
├── cli.py             # CLI implementation pattern
├── agent/             # Agent architecture patterns
│   ├── agent.py      # Agent creation pattern
│   ├── tools.py      # Tool implementation pattern
│   └── providers.py  # Multi-provider pattern
└── tests/            # Testing patterns
    ├── test_agent.py # Unit test patterns
    └── conftest.py   # Pytest configuration
```

## Best Practices

### 1. Be Explicit in INITIAL.md
- Don't assume the AI knows your preferences
- Include specific requirements and constraints
- Reference examples liberally

### 2. Provide Comprehensive Examples
- More examples = better implementations
- Show both what to do AND what not to do
- Include error handling patterns

### 3. Use Validation Gates
- PRPs include test commands that must pass
- AI will iterate until all validations succeed
- This ensures working code on first try

### 4. Leverage Documentation
- Include official API docs
- Add MCP server resources
- Reference specific documentation sections

### 5. Customize CLAUDE.md
- Add your conventions
- Include project-specific rules
- Define coding standards

## 🎯 Advanced PRP Method - Multi-Agent Research Approach

This template demonstrates an advanced PRP creation method using multiple parallel research agents for comprehensive documentation gathering.

### See Advanced AI Automation Examples
- **SEO Grove**: https://seogrove.ai/ - Example of advanced AI automation (built with different methods)
- **YouTube Channel**: https://www.youtube.com/c/incomestreamsurfers - Learn more about AI automation methodologies
- **AI Automation School**: https://www.skool.com/iss-ai-automation-school-6342/about - Join our community

### Advanced PRP Creation Process

#### Prompt 1: Initialize Research Framework
```
read my incredibly specific instructions about how to create a prp document then summarise them, also store how to do a jina scrapein order to create a llm.txt in your memory

If a page 404s or does not scrape properly, scrape it again

Do not use Jina to scrape CSS of the design site.

All SEPARATE pages must be stored in /research/[technology]/ directories with individual .md files.

curl
  "https://r.jina.ai/https://platform.openai.com/docs/" \
    -H "Authorization: Bearer jina_033257e7cdf14fd3b948578e2d34986bNtfCCkjHt7_j1Bkp5Kx521rDs2Eb"
```

#### Prompt 2: Generate PRP with Parallel Research
```
/generate-prp initial.md
```

**Wait until it gets to the research phase, then press escape and say:**

```
can you spin up multiple research agents and do this all at the same time
```

This approach enables:
- **Parallel Documentation Scraping**: 6+ agents simultaneously research different technologies
- **Comprehensive Coverage**: 30-100+ pages of official documentation scraped and organized
- **Technology-Specific Organization**: Each technology gets its own `/research/[tech]/` directory
- **Production-Ready PRPs**: Complete implementation blueprints with real-world examples

### Research Directory Structure
```
research/
├── pydantic-ai/      # 22+ documentation pages
├── openai/           # 20+ API documentation pages  
├── anthropic/        # 18+ Claude documentation pages
├── jina/             # 12+ scraping API pages
├── shopify/          # 18+ GraphQL/REST API pages
└── seo-apis/         # 24+ Search Console/Ahrefs pages
```

This multi-agent research approach results in PRPs with 9/10 confidence scores for one-pass implementation success.

## Resources

- [Claude Code Documentation](https://docs.anthropic.com/en/docs/claude-code)
- [Context Engineering Best Practices](https://www.philschmid.de/context-engineering)
- [SEO Grove - Live Implementation](https://seogrove.ai/)
- [Income Stream Surfers - YouTube Channel](https://www.youtube.com/c/incomestreamsurfers)