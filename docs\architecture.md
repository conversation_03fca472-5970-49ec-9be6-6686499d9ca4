```mermaid
graph TB
    A[User Input] --> B{AI Orchestrator}
    B --> C[Task Agent]
    B --> D[Calendar Agent] 
    B --> E[Search Agent]
    
    C --> F[Task Tool]
    D --> G[Calendar Tool]
    E --> H[Web Search Tool]
    E --> I[Database Search Tool]
    
    F --> J[(SQLite Database)]
    G --> J
    I --> J
    I --> K[Ollama Embeddings]
    H --> L[LangSearch API]
    
    J --> M[WebSocket Updates]
    L --> M
    K --> M
    
    M --> N[Frontend Animations]
    N --> O[Visual Feedback]
    N --> P[Result Display]
    
    style A fill:#4f46e5,stroke:#312e81,color:#fff
    style B fill:#7c3aed,stroke:#553c9a,color:#fff
    style J fill:#059669,stroke:#047857,color:#fff
    style M fill:#dc2626,stroke:#991b1b,color:#fff
    style O fill:#f59e0b,stroke:#d97706,color:#fff
```

### 🔄 AI Processing Flow (Following Mermaid Diagram)

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant W as WebSocket
    participant AI as AI Orchestrator
    participant T as Tools
    participant DB as Database
    
    U->>F: Types input
    F->>W: Send input via WebSocket
    W->>AI: Process input
    
    AI->>W: "Analyzing input..." 🎭
    W->>F: Show analyzing animation
    
    AI->>W: "Categorizing..." 🤖
    W->>F: Show categorizing animation
    
    AI->>T: Execute appropriate tool
    T->>DB: Store/retrieve data
    DB->>T: Return results
    
    T->>AI: Tool results
    AI->>W: "Task/Event/Question identified!" ✨
    W->>F: Show completion animation
    
    F->>U: Display results with smooth animations
```

## 🏛️ Project Structure
