"""
Test Configuration and Dependencies
Task 40: Testing infrastructure configuration

PATTERN: Centralized test configuration and dependency management
Features:
- Test environment configuration
- Dependency isolation
- Test data management
- Performance benchmarking settings
- Security testing parameters
"""

import os
import sys
from pathlib import Path

# Add the tests directory to Python path
TEST_DIR = Path(__file__).parent
sys.path.insert(0, str(TEST_DIR))
sys.path.insert(0, str(TEST_DIR / "e2e"))

# Add the project root to Python path
PROJECT_ROOT = TEST_DIR.parent
sys.path.insert(0, str(PROJECT_ROOT))

# Test Configuration
TEST_CONFIG = {
    "backend_url": "http://localhost:8000",
    "frontend_url": "http://localhost:3000",
    "websocket_url": "ws://localhost:8000/ws",
    
    "timeouts": {
        "default": 30,
        "load_page": 60,
        "api_request": 15,
        "websocket_connect": 10,
        "animation_complete": 5
    },
    
    "performance": {
        "max_response_time": 2.0,
        "min_fps": 45,
        "max_frame_time": 16.67,  # 60 FPS
        "memory_threshold_mb": 500,
        "concurrent_users": 10,
        "requests_per_user": 50
    },
    
    "security": {
        "rate_limit_threshold": 100,
        "block_rate_minimum": 0.8,
        "required_headers": [
            "X-Content-Type-Options",
            "X-Frame-Options", 
            "X-XSS-Protection",
            "Strict-Transport-Security",
            "Content-Security-Policy"
        ],
        "vulnerability_tolerance": {
            "high": 0,
            "medium": 2,
            "low": 5
        }
    },
    
    "browser_configs": [
        {"name": "chromium", "headless": True},
        {"name": "firefox", "headless": True},
        {"name": "webkit", "headless": True}
    ],
    
    "test_data": {
        "sample_inputs": [
            "Finish the AI dashboard documentation by Friday",
            "Schedule team meeting tomorrow at 2 PM",
            "What are React best practices?",
            "Add reminder for dentist appointment",
            "Research machine learning algorithms"
        ],
        
        "security_payloads": {
            "sql_injection": [
                "'; DROP TABLE users; --",
                "1' OR '1'='1",
                "'; INSERT INTO users (name) VALUES ('hacked'); --",
                "1' UNION SELECT password FROM users --"
            ],
            "xss": [
                "<script>alert('XSS')</script>",
                "javascript:alert('XSS')",
                "<img src=x onerror=alert('XSS')>",
                "<iframe src='javascript:alert(`XSS`)'></iframe>"
            ],
            "command_injection": [
                "; cat /etc/passwd",
                "| ls -la",
                "&& rm -rf /",
                "`whoami`"
            ]
        }
    },
    
    "reporting": {
        "generate_html": True,
        "generate_json": True,
        "generate_summary": True,
        "screenshot_on_failure": True,
        "save_performance_metrics": True
    }
}

# Environment Setup
def setup_test_environment():
    """Setup test environment variables and paths"""
    os.environ["TESTING"] = "true"
    os.environ["LOG_LEVEL"] = "INFO"
    
    # Create required directories
    test_dirs = [
        "logs",
        "reports", 
        "screenshots",
        "test_results",
        "performance_data"
    ]
    
    for dir_name in test_dirs:
        Path(dir_name).mkdir(exist_ok=True)

# Initialize test environment
setup_test_environment()
