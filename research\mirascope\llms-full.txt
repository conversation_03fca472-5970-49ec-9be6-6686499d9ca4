Title: 

URL Source: https://mirascope.com/docs/mirascope/llms-full.txt

Markdown Content:
# Table of Contents

# Welcome - Mirascope is a Python library that streamlines working with LLMs

# Quickstart - Get started with Mirascope across various LLM providers

# Learn Mirascope - A comprehensive guide to Mirascope's core components and features. This overview provides a roadmap for learning how to build AI-powered applications with Mirascope.

# Prompts - Master the art of creating effective prompts for LLMs using Mirascope. Learn about message roles, multi-modal inputs, and dynamic prompt configuration.

# Calls - Learn how to make API calls to various LLM providers using Mirascope. This guide covers basic usage, handling responses, and configuring call parameters for different providers.

# Streams - Learn how to process LLM responses in real-time as they are generated using Mirascope's streaming capabilities for more interactive and responsive applications.

# Chaining - Learn how to combine multiple LLM calls in sequence to solve complex tasks through functional chaining, nested chains, conditional execution, and parallel processing.

# Response Models - Learn how to structure and validate LLM outputs using Pydantic models for type safety, automatic validation, and easier data manipulation across different providers.

# JSON Mode - Learn how to request structured JSON outputs from LLMs with Mirascope's JSON Mode for easier parsing, validation, and integration with your applications.

# Output Parsers - Learn how to process and structure raw LLM outputs into usable formats using Mirascope's flexible output parsers for more reliable and application-ready results.

# Tools - Learn how to define, use, and chain together LLM-powered tools in Mirascope to extend model capabilities with external functions, data sources, and system interactions.

# Agents - Learn how to build autonomous and semi-autonomous LLM-powered agents with Mirascope that can use tools, maintain state, and execute multi-step reasoning processes.

# Evals - Learn how to evaluate LLM outputs using multiple approaches including LLM-based evaluators, panels of judges, and hardcoded evaluation criteria.

# Async - Learn how to use asynchronous programming with Mirascope to efficiently handle I/O-bound operations, improve responsiveness, and run multiple LLM calls concurrently.

# Retries - Learn how to implement robust retry mechanisms for LLM API calls using Mirascope and Tenacity to handle rate limits, validation errors, and other failures.

# Local (Open-Source) Models - Learn how to use Mirascope with locally hosted open-source models through Ollama, vLLM, and other APIs with OpenAI compatibility.

 # Welcome to Mirascope 

 [![Image 1: Tests](https://github.com/Mirascope/mirascope/actions/workflows/tests.yml/badge.svg?branch=main)](https://github.com/Mirascope/mirascope/actions/workflows/tests.yml) [![Image 2: Coverage](https://codecov.io/github/Mirascope/mirascope/graph/badge.svg?token=HAEAWT3KC9)](https://app.codecov.io/github/Mirascope/mirascope) [![Image 3: PyPI Version](https://img.shields.io/pypi/v/mirascope.svg)](https://pypi.org/project/mirascope/) [![Image 4: Stars](https://img.shields.io/pypi/pyversions/mirascope.svg)](https://pypi.org/project/mirascope/) [![Image 5: License](https://img.shields.io/github/license/Mirascope/mirascope.svg)](https://github.com/Mirascope/mirascope/blob/main/LICENSE)

 Mirascope is a powerful, flexible, and user-friendly library that simplifies the process of working with LLMs through a unified interface that works across various supported providers, including [OpenAI](https://openai.com/), [Anthropic](https://www.anthropic.com/), [Mistral](https://mistral.ai/), [Google (Gemini/Vertex)](https://googleapis.github.io/python-genai/), [Groq](https://groq.com/), [Cohere](https://cohere.com/), [LiteLLM](https://www.litellm.ai/), [Azure AI](https://azure.microsoft.com/en-us/solutions/ai), and [Bedrock](https://aws.amazon.com/bedrock/). Whether you're generating text, extracting structured information, or developing complex AI-driven agent systems, Mirascope provides the tools you need to streamline your development process and create powerful, robust applications. 

   Why Use Mirascope    Join Our Community    Star the Repo 

 ## Getting Started Install Mirascope, specifying the provider you intend to use, and set your API key:  ## Mirascope API Mirascope provides a consistent, easy-to-use API across all providers:  ```python from mirascope import llm from pydantic import BaseModel class Book(BaseModel): """An extracted book.""" title: str author: str # [!code highlight:6] @llm.call( provider="$PROVIDER", model="$MODEL", response_model=Book ) def extract_book(text: str) -> str: return f"Extract {text}" book: Book = extract_book("The Name of the Wind by Patrick Rothfuss") # [!code highlight] print(book) # Output: title='The Name of the Wind' author='Patrick Rothfuss' # [!code highlight] ```  ```python from mirascope import llm, prompt_template from pydantic import BaseModel class Book(BaseModel): """An extracted book.""" title: str author: str # [!code highlight:6] @llm.call( provider="$PROVIDER", model="$MODEL", response_model=Book ) @prompt_template("Extract {text}") def extract_book(text: str): ... book: Book = extract_book("The Name of the Wind by Patrick Rothfuss") # [!code highlight] print(book) # Output: title='The Name of the Wind' author='Patrick Rothfuss' # [!code highlight] ```  ## Provider SDK Equivalent For comparison, here's how you would achieve the same result using the provider's native SDK:  If you'd like a more in-depth guide to getting started with Mirascope, check out our [quickstart guide](/docs/mirascope/guides/getting-started/quickstart/) We're excited to see what you'll build with Mirascope, and we're here to help! Don't hesitate to reach out :) 

 # Quickstart Mirascope supports various LLM providers, including [OpenAI](https://openai.com/), [Anthropic](https://www.anthropic.com/), [Mistral](https://mistral.ai/), [Gemini](https://gemini.google.com), [Groq](https://groq.com/), [Cohere](https://cohere.com/), [LiteLLM](https://www.litellm.ai/), [Azure AI](https://azure.microsoft.com/en-us/solutions/ai), and [Vertex AI](https://cloud.google.com/vertex-ai). You can select your preferred provider using the dropdown menu in the sidebar to the right. (Just below 'Copy as Markdown'!) ## Setup Let's start by installing Mirascope and setting up your API keys:  This installs Mirascope with the necessary packages for your chosen provider and configures the appropriate API keys. ## Basic LLM Call The `call` decorator in Mirascope transforms Python functions into LLM API calls. This allows you to seamlessly integrate LLM interactions into your Python code. ```python from mirascope import llm @llm.call(provider="$PROVIDER", model="$MODEL") def get_capital(country: str) -> str: return f"What is the capital of {country}?" response = get_capital("Japan") print(response.content) ``` ``` The capital of Japan is Tokyo. ``` In this example: 1. We import the `llm` module from Mirascope, which provides the `call` decorator. 2. The `@llm.call` decorator specifies which provider and model to use. 3. We return the content of a single user message in the function body. 4. When we call `get_capital("Japan")`, it templates the prompt, sends a request to the provider's API, and returns the response. 5. We print the `content` of the response, which contains the LLM's answer. This approach allows you to use LLMs as if they were regular Python functions, making it easy to integrate AI capabilities into your applications. ## Streaming Responses Streaming allows you to process LLM responses in real-time, which is particularly useful for long-form content generation or when you want to provide immediate feedback to users. ```python from mirascope import llm @llm.call(provider="$PROVIDER", model="$MODEL", stream=True) def stream_city_info(city: str) -> str: return f"Provide a brief description of {city}." for chunk, _ in stream_city_info("Tokyo"): print(chunk.content, end="", flush=True) ``` ``` Tokyo, the capital of Japan, is a vibrant metropolis known for its unique blend of tradition and modernity. As one of the world's most populous cities, it features a bustling urban landscape filled with skyscrapers, renowned shopping districts like Shibuya and Ginza, and cultural landmarks such as the historic Senso-ji Temple. Tokyo is also famous for its diverse culinary scene, ranging from street food to Michelin-starred restaurants. The city's efficient public transportation system makes it easy to explore its many neighborhoods, each offering distinct experiences, whether it's the tranquil gardens of Ueno, the electronic town of Akihabara, or the fashion-forward streets of Harajuku. With its rich cultural heritage, cutting-edge technology, and constant innovation, Tokyo embodies the essence of contemporary urban life. ``` Here's what's happening in this streaming example: 1. We use the `stream=True` parameter in the `@llm.call` decorator to enable streaming. 2. The function returns an iterator that yields chunks of the response as they become available. 3. We iterate over the chunks, printing each one immediately. 4. The `end=""` and `flush=True` parameters in the print function ensure that the output is displayed in real-time without line breaks. Streaming is beneficial for: - Providing immediate feedback to users - Processing very long responses efficiently - Implementing typewriter-like effects in user interfaces ## Response Models Response models in Mirascope allow you to structure and validate the output from LLMs. This feature is particularly useful when you need to ensure that the LLM's response adheres to a specific format or contains certain fields. ```python from mirascope import llm from pydantic import BaseModel class Capital(BaseModel): city: str country: str @llm.call(provider="$PROVIDER", model="$MODEL", response_model=Capital) def extract_capital(query: str) -> str: return f"{query}" capital = extract_capital("The capital of France is Paris") print(capital) ``` ``` city='Paris' country='France' ``` ## JSON Mode JSON mode allows you to directly parse LLM outputs as JSON. This is particularly useful when you need structured data from your LLM calls. ```python from mirascope import llm @llm.call(provider="$PROVIDER", model="$MODEL", json_mode=True) def city_info(city: str) -> str: return f"Provide information about {city} in JSON format" response = city_info("Tokyo") print(response.content) # This will be a JSON-formatted string ``` ```json { "city": "Tokyo", "country": "Japan", "population": 13929286, "area_km2": 2191, "language": ["Japanese"], "currency": { "name": "Yen", "symbol": "¥" }, "landmarks": [ { "name": "Tokyo Tower", "type": "Observation Tower" }, { "name": "Shibuya Crossing", "type": "Famous Intersection" }, { "name": "Senso-ji Temple", "type": "Historic Site" }, { "name": "Meiji Shrine", "type": "Shinto Shrine" } ], "transportation": { "rail": { "types": ["Subway", "Light Rail", "High-Speed Rail"], "notable_lines": ["Yamanote Line", "Chuo Line", "Tozai Line"] }, "airport": ["Narita International Airport", "Haneda Airport"] }, "cuisine": [ "Sushi", "Ramen", "Tempura", "Yakitori" ], "climate": { "type": "Humid subtropical", "average_temperature": { "summer": "26°C", "winter": "5°C" }, "average_precipitation_mm": 1650 } } ``` JSON mode is beneficial for: - Ensuring structured outputs from LLMs - Easy integration with data processing pipelines - Creating APIs that return JSON data Note that not all providers have an explicit JSON mode. For those providers, we attempt to instruct the model to provide JSON; however, there is no guarantee that it will output only JSON. You can combine `json_mode=True` with `response_model` to automatically parse the JSON output into a Pydantic model: ```python from mirascope import llm from pydantic import BaseModel class CityInfo(BaseModel): name: str population: int country: str @llm.call(provider="$PROVIDER", model="$MODEL", json_mode=True, response_model=CityInfo) def city_info(city: str) -> str: return f"Provide information about {city} in JSON format" response = city_info("Tokyo") print( f"Name: {response.name}, Population: {response.population}, Country: {response.country}" ) ``` ``` Name: Tokyo, Population: 13929286, Country: Japan ``` ## Asynchronous Processing Mirascope supports asynchronous processing, allowing for efficient parallel execution of multiple LLM calls. This is particularly useful when you need to make many LLM calls concurrently or when working with asynchronous web frameworks. ```python from mirascope import llm import asyncio from pydantic import BaseModel class Capital(BaseModel): city: str country: str @llm.call(provider="$PROVIDER", model="$MODEL", response_model=Capital) async def get_capital_async(country: str) -> str: return f"What is the capital of {country}?" async def main(): countries = ["France", "Japan", "Brazil"] tasks = [get_capital_async(country) for country in countries] capitals = await asyncio.gather(*tasks) for capital in capitals: print(f"The capital of {capital.country} is {capital.city}") # For a Python script, uncomment this line: # asyncio.run(main()) # For demonstration purposes only (this won't work in a regular Python script): # In a Jupyter notebook or interactive environment you would use: # await main() ``` ``` The capital of France is Paris The capital of Japan is Tokyo The capital of Brazil is Brasília ``` This asynchronous example demonstrates: 1. An async version of our `get_capital` function, defined with `async def`. 2. Use of `asyncio.gather()` to run multiple async tasks concurrently. 3. Processing of results as they become available. Asynchronous processing offers several advantages: - Improved performance when making multiple LLM calls - Better resource utilization - Compatibility with async web frameworks like FastAPI or aiohttp ## Output Parsers Output parsers allow you to process LLM responses in custom formats. These enable a lot of different ways to structure and extract from LLM outputs; for example using regular expressions to extract from xml tags. ```python import re from mirascope import llm, prompt_template def parse_cot(response: llm.CallResponse) -> str: pattern = r".*?.*?(.*?)" match = re.search(pattern, response.content, re.DOTALL) if not match: return response.content return match.group(1).strip() @llm.call(provider="$PROVIDER", model="$MODEL", output_parser=parse_cot) @prompt_template( """ First, output your thought process in  tags. Then, provide your final output in  tags. Question: {question} """ ) def chain_of_thought(question: str): pass question = "Roger has 5 tennis balls. He buys 2 cans of 3. How many does he have now?" output = chain_of_thought(question) print(output) ``` In this example, we ask the model to produce a chain of thought as well as a final output, and the output parser separates these two pieces in a convenient fashion. Output parsers are useful for: - Extracting specific formats or data structures from LLM responses - Cleaning and standardizing LLM outputs - Implementing custom post-processing logic ## Next Steps This concludes our Quickstart Guide to Mirascope. We've covered the main features of the library, including prompt templates, basic calls, streaming, response models, asynchronous processing, JSON mode, and output parsers. Each of these features can be combined and customized to create powerful, flexible AI applications. If you like what you've seen so far, [give us a star](https://github.com/Mirascope/mirascope) and [join our community](/discord-invite). 

 # Learn Mirascope This section is designed to help you master Mirascope, a toolkit for building AI-powered applications with Large Language Models (LLMs). Our documentation is tailored for developers who have at least some experience with Python and LLMs. Whether you're coming from other development tool libraries or have worked directly with provider SDKs and APIs, Mirascope offers a familiar but enhanced experience. If you haven't already, we recommend checking out [Getting Started](/docs/mirascope/guides/getting-started/quickstart) and [Why Use Mirascope](/docs/mirascope/getting-started/why). ## Key Features and Benefits 

 

 
### Pythonic By Design

 
Our design approach is to remain Pythonic so you can build your way

 

 

 
### Editor Support & Type Hints

 
Rich autocomplete, inline documentation, and type hints to catch errors before runtime

 

 

 
### Provider-Agnostic & Provider-Specific

 
Seamlessly engineer prompts agnostic or specific to various LLM providers

 

 

 
### Comprehensive Tooling

 
Complete suite of tools for every aspect of working with LLM provider APIs

 

 ## Core Components Mirascope is built around these core components, each designed to handle specific aspects of working with LLM provider APIs. We encourage you to dive into each component's documentation to gain a deeper understanding of Mirascope's capabilities. Start with the topics that align most closely with your immediate needs, but don't hesitate to explore all areas – you might discover new ways to enhance your LLM applications! 

 ![Image 6: Learn Flow Chart](https://mirascope.com/assets/learn_flow.svg)

 

 
### Prompts

 
Learn how to create and manage prompts effectively

 [Read more →](https://mirascope.com/docs/mirascope/learn/prompts) 

 

 
### Calls

 
Understand how to make calls to LLMs using Mirascope

 [Read more →](https://mirascope.com/docs/mirascope/learn/calls) 

 

 
### Streams

 
Explore streaming responses for real-time applications

 [Read more →](https://mirascope.com/docs/mirascope/learn/streams) 

 

 
### Chaining

 
Understand the art of chaining multiple LLM calls for complex tasks

 [Read more →](https://mirascope.com/docs/mirascope/learn/chaining) 

 

 
### Response Models

 
Define and use structured output models with automatic validation

 [Read more →](https://mirascope.com/docs/mirascope/learn/response_models) 

 

 
### JSON Mode

 
Work with structured JSON data responses from LLMs

 [Read more →](https://mirascope.com/docs/mirascope/learn/json_mode) 

 

 
### Output Parsers

 
Process and transform custom LLM output structures effectively

 [Read more →](https://mirascope.com/docs/mirascope/learn/output_parsers) 

 

 
### Tools

 
Discover how to extend LLM capabilities with custom tools

 [Read more →](https://mirascope.com/docs/mirascope/learn/tools) 

 

 
### Agents

 
Put everything together to build advanced AI agents using Mirascope

 [Read more →](https://mirascope.com/docs/mirascope/learn/agents) 

 

 
### Evals

 
Apply core components to build evaluation strategies for your LLM applications

 [Read more →](https://mirascope.com/docs/mirascope/learn/evals) 

 

 
### Async

 
Maximize efficiency with asynchronous programming

 [Read more →](https://mirascope.com/docs/mirascope/learn/async) 

 

 
### Retries

 
Understand how to automatically retry failed API calls

 [Read more →](https://mirascope.com/docs/mirascope/learn/retries) 

 

 
### Local Models

 
Learn how to use Mirascope with locally deployed LLMs

 [Read more →](https://mirascope.com/docs/mirascope/learn/local_models) 

 As you progress, you'll find advanced topics and best practices throughout the documentation. These will help you optimize your use of Mirascope and build increasingly sophisticated AI-powered applications. Happy learning, and welcome to the world of development with Mirascope! 

 # Prompts  [`mirascope.core.base.message_param.BaseMessageParam`](/docs/mirascope/api/core/base/message_param#basemessageparam)  When working with Large Language Model (LLM) APIs, the "prompt" is generally a list of messages where each message has a particular role. These prompts are the foundation of effectively working with LLMs, so Mirascope provides powerful tools to help you create, manage, and optimize your prompts for various LLM interactions. Let's look at how we can write prompts using Mirascope in a reusable, modular, and provider-agnostic way.  For the following explanations we will be talking *only* about the messages aspect of prompt engineering and will discuss calling the API later in the [Calls](/docs/mirascope/learn/calls) documentation. In that section we will show how to use these provider-agnostic prompts to actually call a provider's API as well as how to engineer and tie a prompt to a specific call.  ## Prompt Templates (Messages) First, let's look at a basic example:  ```python from mirascope import prompt_template @prompt_template() # [!code highlight] def recommend_book_prompt(genre: str) -> str: # [!code highlight] return f"Recommend a {genre} book" # [!code highlight] print(recommend_book_prompt("fantasy")) # Output: [BaseMessageParam(role='user', content='Recommend a fantasy book')] # [!code highlight] ```  ```python from mirascope import prompt_template @prompt_template("Recommend a {genre} book") # [!code highlight] def recommend_book_prompt(genre: str): ... # [!code highlight] print(recommend_book_prompt("fantasy")) # Output: [BaseMessageParam(role='user', content='Recommend a fantasy book')] # [!code highlight] ```  In this example: 1. The `recommend_book_prompt` method's signature defines the prompt's template variables. 2. Calling the method with `genre="fantasy"` returns a list with the corresponding `BaseMessageParam` instance with role `user` and content "Recommend a fantasy book". The core concept to understand here is `BaseMessageParam`. This class operates as the base class for message parameters that Mirascope can handle and use across all supported providers. In Mirascope, we use the `@prompt_template` decorator to write prompt templates as reusable methods that return the corresponding list of `BaseMessageParam` instances. There are two common ways of writing Mirascope prompt functions: 1. *(Shorthand)* Returning the `str` or `list` content for a single user message, or returning `Messages.{Role}` (individually or a list) when specific roles are needed. 2. *(String Template)* Passing a string template to `@prompt_template` that gets parsed and then formatted like a normal Python formatted string. Which method you use is mostly up to your preference, so feel free to select which one you prefer in the following sections. ## Message Roles We can also define additional messages with different roles, such as a system message:  ```python from mirascope import Messages, prompt_template @prompt_template() def recommend_book_prompt(genre: str) -> Messages.Type: return [ Messages.System("You are a librarian"), # [!code highlight] Messages.User(f"Recommend a {genre} book"), # [!code highlight] ] print(recommend_book_prompt("fantasy")) # Output: [ # BaseMessageParam(role='system', content='You are a librarian'), # [!code highlight] # BaseMessageParam(role='user', content='Recommend a fantasy book'), # [!code highlight] # ] ```  ```python{6,7} from mirascope import prompt_template @prompt_template( """ SYSTEM: You are a librarian USER: Recommend a {genre} book """ ) def recommend_book_prompt(genre: str): ... print(recommend_book_prompt("fantasy")) # Output: [ # BaseMessageParam(role='system', content='You are a librarian'), # [!code highlight] # BaseMessageParam(role='user', content='Recommend a fantasy book'), # [!code highlight] # ] ```  The return type `Messages.Type` accepts all shorthand methods as well as `BaseMessageParam` types. Since the message methods (e.g. `Messages.User`) return `BaseMessageParam` instances, we generally recommend always typing your prompt templates with the `Messages.Type` return type since it covers all prompt template writing methods.  Mirascope prompt templates currently support the `system`, `user`, and `assistant` roles. When using string templates, the roles are parsed by their corresponding all caps keyword (e.g. SYSTEM). For messages with the `tool` role, see how Mirascope automatically generates these messages for you in the [Tools](/docs/mirascope/learn/tools) and [Agents](/docs/mirascope/learn/agents) sections.  ## Multi-Line Prompts When writing prompts that span multiple lines, it's important to ensure you don't accidentally include additional, unnecessary tokens (namely `\t` tokens):  ```python{9,10} import inspect from mirascope import prompt_template @prompt_template() def recommend_book_prompt(genre: str) -> str: return inspect.cleandoc( f""" Recommend a {genre} book. Output in the format Title by Author. """ ) print(recommend_book_prompt("fantasy")) # Output: [BaseMessageParam(role='user', content='Recommend a fantasy book.\nOutput in the format Title by Author.')] # [!code highlight] ```  ```python{6,7} from mirascope import prompt_template @prompt_template( """ Recommend a {genre} book. Output in the format Title by Author. """ ) def recommend_book_prompt(genre: str): ... print(recommend_book_prompt("fantasy")) # Output: [BaseMessageParam(role='user', content='Recommend a fantasy book.\nOutput in the format Title by Author.')] # [!code highlight] ```  In this example, we use `inspect.cleandoc` to remove unnecessary tokens while maintaining proper formatting in our codebase.  When using string templates, the template is automatically cleaned for you, so there is no need to use `inspect.cleandoc` in that case. However, it's extremely important to note that you must start messages with the same indentation in order to properly remove the unnecessary tokens. For example: ```python from mirascope import prompt_template # BAD @prompt_template( """ USER: First line Second line """ ) def bad_template(params): ... # GOOD @prompt_template( """ USER: First line Second line """ ) def good_template(params): ... ```  ## Multi-Modal Inputs Recent advancements in Large Language Model architecture has enabled many model providers to support multi-modal inputs (text, images, audio, etc.) for a single endpoint. Mirascope treats these input types as first-class and supports them natively. While Mirascope provides a consistent interface, support varies among providers: | Type | Anthropic | Cohere | Google | Groq | Mistral | OpenAI | |---------------|:-----------:|:--------:|:---------------:|:------:|:---------:|:--------:| | text | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | | image | ✓ | — | ✓ | ✓ | ✓ | ✓ | | audio | — | — | ✓ | — | — | ✓ | | video | — | — | ✓ | — | — | — | | document | ✓ | — | ✓ | — | — | — | *Legend: ✓ (Supported), — (Not Supported)* ### Image Inputs  ```python from mirascope import prompt_template from PIL import Image @prompt_template() def recommend_book_prompt(previous_book: Image.Image) -> list: return ["I just read this book:", previous_book, "What should I read next?"] # [!code highlight] with Image.open("path/to/image.jpg") as image: print(recommend_book_prompt(image)) # Output: [ # BaseMessageParam( # role='user', # content=[ # ContentPartParam(type='text', text='I just read this book:'), # [!code highlight] # ContentPartParam(type='image', image=), # [!code highlight] # ContentPartParam(type='text', text='What should I read next?') # [!code highlight] # ] # ) # ] ```  ```python from mirascope import prompt_template from PIL import Image @prompt_template( "I just read this book: {previous_book:image} What should I read next?" # [!code highlight] ) def recommend_book_prompt(previous_book: Image.Image): ... with Image.open("path/to/image.jpg") as image: print(recommend_book_prompt(image)) # Output: [ # BaseMessageParam( # role='user', # content=[ # ContentPartParam(type='text', text='I just read this book:'), # [!code highlight] # ContentPartParam(type='image', image=), # [!code highlight] # ContentPartParam(type='text', text='What should I read next?') # [!code highlight] # ] # ) # ] ```  When using string templates, you can also specify `:images` to inject multiple image inputs through a single template variable. The `:image` and `:images` tags support the `bytes | str` and `list[bytes] | list[str]` types, respectively. When passing in a `str`, the string template assumes it indicates a url or local filepath and will attempt to load the bytes from the source. You can also specify additional options as arguments of the tags, e.g. `{url:image(detail=low)}`  ### Audio Inputs  ```python from mirascope import Messages, prompt_template from pydub import AudioSegment @prompt_template() def identify_book_prompt(audio_wave: AudioSegment) -> Messages.Type: return ["Here's an audio book snippet:", audio_wave, "What book is this?"] # [!code highlight] with open("....", "rb") as audio: print(identify_book_prompt(AudioSegment.from_mp3(audio))) # Output: [ # BaseMessageParam( # role="user", # content=[ # TextPart(type="text", text="Here's an audio book snippet:"), # [!code highlight] # AudioPart(type='audio', media_type='audio/wav', audio=b'...'), # [!code highlight] # TextPart(type="text", text="What book is this?"), # [!code highlight] # ], # ) # ] ```  ```python from mirascope import prompt_template @prompt_template("Here's an audio book snippet: {audio_wave:audio} What book is this?") # [!code highlight] def identify_book_prompt(audio_wave: bytes): ... print(identify_book_prompt(b"...")) # Output: [ # BaseMessageParam( # role="user", # content=[ # TextPart(type="text", text="Here's an audio book snippet:"), # [!code highlight] # AudioPart(type='audio', media_type='audio/wav', audio=b'...'), # [!code highlight] # TextPart(type="text", text="What book is this?"), # [!code highlight] # ], # ) # ] ```  ```python import wave from mirascope import Messages, prompt_template @prompt_template() def identify_book_prompt(audio_wave: wave.Wave_read) -> Messages.Type: return ["Here's an audio book snippet:", audio_wave, "What book is this?"] # [!code highlight] with open("....", "rb") as f, wave.open(f, "rb") as audio: print(identify_book_prompt(audio)) # Output: [ # BaseMessageParam( # role="user", # content=[ # TextPart(type="text", text="Here's an audio book snippet:"), # [!code highlight] # AudioPart(type='audio', media_type='audio/wav', audio=b'...'), # [!code highlight] # TextPart(type="text", text="What book is this?"), # [!code highlight] # ], # ) # ] ```  ```python from mirascope import prompt_template @prompt_template("Here's an audio book snippet: {audio_wave:audio} What book is this?") # [!code highlight] def identify_book_prompt(audio_wave: bytes): ... print(identify_book_prompt(b"...")) # Output: [ # BaseMessageParam( # role="user", # content=[ # TextPart(type="text", text="Here's an audio book snippet:"), # [!code highlight] # AudioPart(type='audio', media_type='audio/wav', audio=b'...'), # [!code highlight] # TextPart(type="text", text="What book is this?"), # [!code highlight] # ], # ) # ] ```  When using string templates, you can also specify `:audios` to inject multiple audio inputs through a single template variable. The `:audio` and `:audios` tags support the `bytes | str` and `list[bytes] | list[str]` types, respectively. When passing in a `str`, the string template assumes it indicates a url or local filepath and will attempt to load the bytes from the source.  ### Document Inputs  ```python from mirascope import DocumentPart, Messages, prompt_template @prompt_template() def recommend_book_prompt(previous_book_pdf: bytes) -> Messages.Type: return Messages.User( [ "I just read this book:", # [!code highlight] DocumentPart( # [!code highlight] type="document", # [!code highlight] media_type="application/pdf", # [!code highlight] document=previous_book_pdf, # [!code highlight] ), # [!code highlight] "What should I read next?", # [!code highlight] ] ) print(recommend_book_prompt(b"...")) # Output: [ # BaseMessageParam( # role="user", # content=[ # TextPart(type="text", text="I just read this book:"), # [!code highlight] # DocumentPart(type='document', media_type='application/pdf', document=b'...'), # [!code highlight] # TextPart(type="text", text="What should I read next?"), # [!code highlight] # ], # ) # ] ```  ```python from mirascope import prompt_template @prompt_template( "I just read this book: {previous_book:document} What should I read next?" # [!code highlight] ) def recommend_book_prompt(previous_book: bytes): ... print(recommend_book_prompt(b"...")) # Output: [ # BaseMessageParam( # role="user", # content=[ # TextPart(type="text", text="I just read this book:"), # [!code highlight] # DocumentPart(type='document', media_type='application/pdf', document=b'...'), # [!code highlight] # TextPart(type="text", text="What should I read next?"), # [!code highlight] # ], # ) # ] ```  Document support varies by provider, but generally includes: - PDF (.pdf) - Word (.doc, .docx) - PowerPoint (.ppt, .pptx) - Excel (.xls, .xlsx) - Text (.txt) - CSV (.csv) Currently, Anthropic is the only provider with explicit document support via their [Document Reading](https://docs.anthropic.com/claude/docs/document-reading) feature. Other providers may require converting documents to text or using specialized tools.  When using string templates, you can also specify `:documents` to inject multiple document inputs through a single template variable. The `:document` and `:documents` tags support the `bytes | str` and `list[bytes] | list[str]` types, respectively. When passing in a `str`, the string template assumes it indicates a url or local filepath and will attempt to load the bytes from the source.  ## Chat History Often you'll want to inject messages (such as previous chat messages) into the prompt. Generally you can just unroll the messages into the return value of your prompt template. When using string templates, we provide a `MESSAGES` keyword for this injection, which you can add in whatever position and as many times as you'd like:  ```python from mirascope import BaseMessageParam, Messages, prompt_template @prompt_template() def chatbot(query: str, history: list[BaseMessageParam]) -> list[BaseMessageParam]: return [Messages.System("You are a librarian"), *history, Messages.User(query)] # [!code highlight] history = [ Messages.User("Recommend a book"), Messages.Assistant("What genre do you like?"), ] print(chatbot("fantasy", history)) # Output: [ # BaseMessageParam(role="system", content="You are a librarian"), # [!code highlight] # BaseMessageParam(role="user", content="Recommend a book"), # [!code highlight] # BaseMessageParam(role="assistant", content="What genre do you like?"), # [!code highlight] # BaseMessageParam(role="user", content="fantasy"), # [!code highlight] # ] ```  ```python{6-8} from mirascope import BaseMessageParam, Messages, prompt_template @prompt_template( """ SYSTEM: You are a librarian MESSAGES: {history} USER: {query} """ ) def chatbot(query: str, history: list[BaseMessageParam]): ... history = [ Messages.User("Recommend a book"), # [!code highlight] Messages.Assistant("What genre do you like?"), # [!code highlight] ] print(chatbot("fantasy", history)) # Output: [ # BaseMessageParam(role="system", content="You are a librarian"), # [!code highlight] # BaseMessageParam(role="user", content="Recommend a book"), # [!code highlight] # BaseMessageParam(role="assistant", content="What genre do you like?"), # [!code highlight] # BaseMessageParam(role="user", content="fantasy"), # [!code highlight] # ] ```  ## Object Attribute Access When using template variables that have attributes, you can easily inject these attributes directly even when using string templates:  ```python from mirascope import prompt_template from pydantic import BaseModel class Book(BaseModel): title: str author: str @prompt_template() def recommend_book_prompt(book: Book) -> str: return f"I read {book.title} by {book.author}. What should I read next?" # [!code highlight] book = Book(title="The Name of the Wind", author="Patrick Rothfuss") print(recommend_book_prompt(book)) # Output: [BaseMessageParam(role='user', content='I read The Name of the Wind by Patrick Rothfuss. What should I read next?')] # [!code highlight] ```  ```python from mirascope import prompt_template from pydantic import BaseModel class Book(BaseModel): title: str author: str @prompt_template("I read {book.title} by {book.author}. What should I read next?") # [!code highlight] def recommend_book_prompt(book: Book): ... book = Book(title="The Name of the Wind", author="Patrick Rothfuss") print(recommend_book_prompt(book)) # Output: [BaseMessageParam(role='user', content='I read The Name of the Wind by Patrick Rothfuss. What should I read next?')] # [!code highlight] ```  It's worth noting that this also works with `self` when using prompt templates inside of a class, which is particularly important when building [Agents](/docs/mirascope/learn/agents). ## Format Specifiers Since Mirascope prompt templates are just formatted strings, standard Python format specifiers work as expected:  ```python from mirascope import prompt_template @prompt_template() def recommend_book(genre: str, price: float) -> str: return f"Recommend a {genre} book under ${price:.2f}" # [!code highlight] print(recommend_book("fantasy", 12.3456)) # Output: [BaseMessageParam(role='user', content='Recommend a fantasy book under $12.35')] # [!code highlight] ```  ```python from mirascope import prompt_template @prompt_template("Recommend a {genre} book under ${price:.2f}") # [!code highlight] def recommend_book(genre: str, price: float): ... print(recommend_book("fantasy", 12.3456)) # Output: [BaseMessageParam(role='user', content='Recommend a fantasy book under $12.35')] # [!code highlight] ```  When writing string templates, we also offer additional format specifiers for convenience around formatting more dynamic content: ### Lists String templates support the `:list` format specifier for formatting lists:  ```python from mirascope import prompt_template @prompt_template( """ Book themes: {themes:list} # [!code highlight] Character analysis: {characters:lists} # [!code highlight] """ ) def analyze_book(themes: list[str], characters: list[list[str]]): ... prompt = analyze_book( themes=["redemption", "power", "friendship"], # [!code highlight] characters=[ # [!code highlight] ["Name: Frodo", "Role: Protagonist"], # [!code highlight] ["Name: Gandalf", "Role: Mentor"], # [!code highlight] ], # [!code highlight] ) print(prompt[0].content) # Output: # [!code highlight:12] # Book themes: # redemption # power # friendship # Character analysis: # Name: Frodo # Role: Protagonist # Name: Gandalf # Role: Mentor ```  ```python from mirascope import prompt_template @prompt_template( """ Book themes: {themes:text} # [!code highlight] Character analysis: {characters:texts} # [!code highlight] """ ) def analyze_book(themes: str, characters: list[str]): ... prompt = analyze_book( themes="redemption, power, friendship", # [!code highlight] characters=[ # [!code highlight] "Name: Frodo, Role: Protagonist", # [!code highlight] "Name: Gandalf, Role: Mentor", # [!code highlight] ], # [!code highlight] ) print(prompt[0].content) # Output: # [!code highlight:8] # [ # TextPart(type="text", text="Book themes:"), # TextPart(type="text", text="redemption, power, friendship"), # TextPart(type="text", text="Character analysis:"), # TextPart(type="text", text="Name: Frodo, Role: Protagonist"), # TextPart(type="text", text="Name: Gandalf, Role: Mentor"), # ] ```  ```python from mirascope import TextPart, prompt_template @prompt_template( """ Book themes: {themes:text} # [!code highlight] Character analysis: {characters:texts} # [!code highlight] """ ) def analyze_book(themes: TextPart, characters: list[TextPart]): ... prompt = analyze_book( themes=TextPart(type="text", text="redemption, power, friendship"), # [!code highlight] characters=[ # [!code highlight] TextPart(type="text", text="Name: Frodo, Role: Protagonist"), # [!code highlight] TextPart(type="text", text="Name: Gandalf, Role: Mentor"), # [!code highlight] ], # [!code highlight] ) print(prompt[0].content) # Output: # [!code highlight:8] # [ # TextPart(type="text", text="Book themes:"), # TextPart(type="text", text="redemption, power, friendship"), # TextPart(type="text", text="Character analysis:"), # TextPart(type="text", text="Name: Frodo, Role: Protagonist"), # TextPart(type="text", text="Name: Gandalf, Role: Mentor"), # ] ```  ## Computed Fields (Dynamic Configuration) In Mirascope, we write prompt templates as functions, which enables dynamically configuring our prompts at runtime depending on the values of the template variables. We use the term "computed fields" to talk about variables that are computed and formatted at runtime.  ```python from mirascope import BaseDynamicConfig, Messages, prompt_template @prompt_template() def recommend_book_prompt(genre: str) -> BaseDynamicConfig: uppercase_genre = genre.upper() # [!code highlight] messages = [Messages.User(f"Recommend a {uppercase_genre} book")] # [!code highlight] return { "messages": messages, # [!code highlight] "computed_fields": {"uppercase_genre": uppercase_genre}, # [!code highlight] } print(recommend_book_prompt("fantasy")) # Output: { # "messages": [BaseMessageParam(role="user", content="Recommend a FANTASY book")], # [!code highlight] # "computed_fields": {"uppercase_genre": "FANTASY"}, # [!code highlight] # } ```  ```python from mirascope import BaseDynamicConfig, prompt_template @prompt_template("Recommend a {uppercase_genre} book") # [!code highlight] def recommend_book_prompt(genre: str) -> BaseDynamicConfig: uppercase_genre = genre.upper() # [!code highlight] return { "computed_fields": {"uppercase_genre": uppercase_genre}, # [!code highlight] } print(recommend_book_prompt("fantasy")) # Output: [BaseMessageParam(role='user', content='Recommend a FANTASY book')] # [!code highlight] ```  There are various other parts of an LLM API call that we may want to configure dynamically as well, such as call parameters, tools, and more. We cover such cases in each of their respective sections. ## Next Steps By mastering prompts in Mirascope, you'll be well-equipped to build robust, flexible, and reusable LLM applications. Next, we recommend taking a look at the [Calls](/docs/mirascope/learn/calls) documentation, which shows you how to use your prompt templates to actually call LLM APIs and generate a response. 

 # Calls  If you haven't already, we recommend first reading the section on writing [Prompts](/docs/mirascope/learn/prompts)  When working with Large Language Model (LLM) APIs in Mirascope, a "call" refers to making a request to a LLM provider's API with a particular setting and prompt. The `call` decorator is a core feature of the Mirascope library, designed to simplify and streamline interactions with various LLM providers. This powerful tool allows you to transform prompt templates written as Python functions into LLM API calls with minimal boilerplate code while providing type safety and consistency across different providers. We currently support [OpenAI](https://openai.com/), [Anthropic](https://www.anthropic.com/), [Google (Gemini/Vertex)](https://ai.google.dev/), [Groq](https://groq.com/), [xAI](https://x.ai/api), [Mistral](https://mistral.ai/), [Cohere](https://cohere.com/), [LiteLLM](https://www.litellm.ai/), [Azure AI](https://azure.microsoft.com/en-us/solutions/ai), and [Amazon Bedrock](https://aws.amazon.com/bedrock/). If there are any providers we don't yet support that you'd like to see supported, let us know!  [`mirascope.llm.call`](/docs/mirascope/api/llm/call)  ## Basic Usage and Syntax Let's take a look at a basic example using Mirascope vs. official provider SDKs:  ```python from mirascope import llm @llm.call(provider="$PROVIDER", model="$MODEL") # [!code highlight] def recommend_book(genre: str) -> str: return f"Recommend a {genre} book" # [!code highlight] response: llm.CallResponse = recommend_book("fantasy") print(response.content) ```  ```python from mirascope import llm, prompt_template @llm.call(provider="$PROVIDER", model="$MODEL") # [!code highlight] @prompt_template("Recommend a {genre} book") # [!code highlight] def recommend_book(genre: str): ... response: llm.CallResponse = recommend_book("fantasy") print(response.content) ```  Official provider SDKs typically require more boilerplate code:  Notice how Mirascope makes calls more readable by reducing boilerplate and standardizing interactions with LLM providers. The `llm.call` decorator accepts `provider` and `model` arguments and returns a provider-agnostic `CallResponse` instance that provides a consistent interface regardless of the underlying provider. You can find more information on `CallResponse` in the [section below](#handling-responses) on handling responses. Note the `@prompt_template` decorator is optional unless you're using string templates. ### Runtime Provider Overrides You can override provider settings at runtime using `llm.override`. This takes a function decorated with `llm.call` and lets you specify: - `provider`: Change the provider being called - `model`: Use a different model - `call_params`: Override call parameters like temperature - `client`: Use a different client instance When overriding with a specific `provider`, you must specify the `model` parameter.  ```python from mirascope import llm @llm.call(provider="$PROVIDER", model="$MODEL") # [!code highlight] def recommend_book(genre: str) -> str: # [!code highlight] return f"Recommend a {genre} book" # [!code highlight] response: llm.CallResponse = recommend_book("fantasy") print(response.content) override_response = llm.override( # [!code highlight] recommend_book, # [!code highlight] provider="$OTHER_PROVIDER", # [!code highlight] model="$OTHER_MODEL", # [!code highlight] call_params={"temperature": 0.7}, # [!code highlight] )("fantasy") # [!code highlight] print(override_response.content) ```  ```python from mirascope import llm from mirascope.core import prompt_template @llm.call(provider="$PROVIDER", model="$MODEL") # [!code highlight] @prompt_template("Recommend a {genre} book") # [!code highlight] def recommend_book(genre: str): ... # [!code highlight] response = recommend_book("fantasy") print(response.content) override_response = llm.override( # [!code highlight] recommend_book, # [!code highlight] provider="$OTHER_PROVIDER", # [!code highlight] model="$OTHER_MODEL", # [!code highlight] call_params={"temperature": 0.7}, # [!code highlight] )("fantasy") # [!code highlight] print(override_response.content) ```  ## Handling Responses ### Common Response Properties and Methods  [`mirascope.core.base.call_response`](/docs/mirascope/api/core/base/call_response)  All [`BaseCallResponse`](/docs/mirascope/api) objects share these common properties: - `content`: The main text content of the response. If no content is present, this will be the empty string. - `finish_reasons`: A list of reasons why the generation finished (e.g., "stop", "length"). These will be typed specifically for the provider used. If no finish reasons are present, this will be `None`. - `model`: The name of the model used for generation. - `id`: A unique identifier for the response if available. Otherwise this will be `None`. - `usage`: Information about token usage for the call if available. Otherwise this will be `None`. - `input_tokens`: The number of input tokens used if available. Otherwise this will be `None`. - `output_tokens`: The number of output tokens generated if available. Otherwise this will be `None`. - `cost`: An estimated cost of the API call if available. Otherwise this will be `None`. - `message_param`: The assistant's response formatted as a message parameter. - `tools`: A list of provider-specific tools used in the response, if any. Otherwise this will be `None`. Check out the [`Tools`](/docs/mirascope/learn/tools) documentation for more details. - `tool`: The first tool used in the response, if any. Otherwise this will be `None`. Check out the [`Tools`](/docs/mirascope/learn/tools) documentation for more details. - `tool_types`: A list of tool types used in the call, if any. Otherwise this will be `None`. - `prompt_template`: The prompt template used for the call. - `fn_args`: The arguments passed to the function. - `dynamic_config`: The dynamic configuration used for the call. - `metadata`: Any metadata provided using the dynamic configuration. - `messages`: The list of messages sent in the request. - `call_params`: The call parameters provided to the `call` decorator. - `call_kwargs`: The finalized keyword arguments used to make the API call. - `user_message_param`: The most recent user message, if any. Otherwise this will be `None`. - `start_time`: The timestamp when the call started. - `end_time`: The timestamp when the call ended. There are also two common methods: - `__str__`: Returns the `content` property of the response for easy printing. - `tool_message_params`: Creates message parameters for tool call results. Check out the [`Tools`](/docs/mirascope/learn/tools) documentation for more information. ## Multi-Modal Outputs While most LLM providers focus on text outputs, some providers support additional output modalities like audio. The availability of multi-modal outputs varies among providers: | Provider | Text | Audio | Image | |---------------|:------:|:-------:|:-------:| | OpenAI | ✓ | ✓ | — | | Anthropic | ✓ | — | — | | Mistral | ✓ | — | — | | Google Gemini | ✓ | — | — | | Groq | ✓ | — | — | | Cohere | ✓ | — | — | | LiteLLM | ✓ | — | — | | Azure AI | ✓ | — | — | *Legend: ✓ (Supported), — (Not Supported)* ### Audio Outputs - `audio`: Configuration for the audio output (voice, format, etc.) - `modalities`: List of output modalities to receive (e.g. `["text", "audio"]`) For providers that support audio outputs, you can receive both text and audio responses from your calls:  When using models that support audio outputs, you'll have access to: - `content`: The text content of the response - `audio`: The raw audio bytes of the response - `audio_transcript`: The transcript of the audio response  The example above uses `pydub` and `ffmpeg` for audio playback, but you can use any audio processing libraries or media players that can handle WAV format audio data. Choose the tools that best fit your needs and environment. If you decide to use pydub: - Install [pydub](https://github.com/jiaaro/pydub): `pip install pydub` - Install ffmpeg: Available from [ffmpeg.org](https://www.ffmpeg.org/) or through system package managers  For providers that support audio outputs, refer to their documentation for available voice options and configurations: - OpenAI: [Text to Speech Guide](https://platform.openai.com/docs/guides/text-to-speech)  ## Common Parameters Across Providers There are several common parameters that you'll find across all providers when using the `call` decorator. These parameters allow you to control various aspects of the LLM call: - `model`: The only required parameter for all providers, which may be passed in as a standard argument (whereas all others are optional and must be provided as keyword arguments). It specifies which language model to use for the generation. Each provider has its own set of available models. - `stream`: A boolean that determines whether the response should be streamed or returned as a complete response. We cover this in more detail in the [`Streams`](/docs/mirascope/learn/streams) documentation. - `response_model`: A Pydantic `BaseModel` type that defines how to structure the response. We cover this in more detail in the [`Response Models`](/docs/mirascope/learn/response_models) documentation. - `output_parser`: A function for parsing the response output. We cover this in more detail in the [`Output Parsers`](/docs/mirascope/learn/output_parsers) documentation. - `json_mode`: A boolean that deterines whether to use JSON mode or not. We cover this in more detail in the [`JSON Mode`](/docs/mirascope/learn/json_mode) documentation. - `tools`: A list of tools that the model may request to use in its response. We cover this in more detail in the [`Tools`](/docs/mirascope/learn/tools) documentation. - `client`: A custom client to use when making the call to the LLM. We cover this in more detail in the [`Custom Client`](#custom-client) section below. - `call_params`: The provider-specific parameters to use when making the call to that provider's API. We cover this in more detail in the [`Provider-Specific Usage`](#provider-specific-usage) section below. These common parameters provide a consistent way to control the behavior of LLM calls across different providers. Keep in mind that while these parameters are widely supported, there might be slight variations in how they're implemented or their exact effects across different providers (and the documentation should cover any such differences). Since `call_params` is just a `TypedDict`, you can always include any additional keys at the expense of type errors (and potentially unknown behavior). This presents one way to pass provider-specific parameters (or deprecated parameters) while still using the general interface.  ```python from mirascope import llm @llm.call(provider="$PROVIDER", model="$MODEL", call_params={"max_tokens": 512}) # [!code highlight] def recommend_book(genre: str) -> str: return f"Recommend a {genre} book" response: llm.CallResponse = recommend_book("fantasy") print(response.content) ```  ```python from mirascope import llm, prompt_template @llm.call(provider="$PROVIDER", model="$MODEL", call_params={"max_tokens": 512}) # [!code highlight] @prompt_template("Recommend a {genre} book") def recommend_book(genre: str): ... response: llm.CallResponse = recommend_book("fantasy") print(response.content) ```  ## Dynamic Configuration Often you will want (or need) to configure your calls dynamically at runtime. Mirascope supports returning a `BaseDynamicConfig` from your prompt template, which will then be used to dynamically update the settings of the call. In all cases, you will need to return your prompt messages through the `messages` keyword of the dynamic config unless you're using string templates. ### Call Params  ```python from mirascope import BaseDynamicConfig, Messages, llm @llm.call(provider="$PROVIDER", model="$MODEL") def recommend_book(genre: str) -> BaseDynamicConfig: return { "messages": [Messages.User(f"Recommend a {genre} book")], # [!code highlight] "call_params": {"max_tokens": 512}, # [!code highlight] "metadata": {"tags": {"version:0001"}}, } response: llm.CallResponse = recommend_book("fantasy") print(response.content) ```  ```python from mirascope import BaseDynamicConfig, llm, prompt_template @llm.call(provider="$PROVIDER", model="$MODEL") @prompt_template("Recommend a {genre} book") # [!code highlight] def recommend_book(genre: str) -> BaseDynamicConfig: return { "call_params": {"max_tokens": 512}, # [!code highlight] "metadata": {"tags": {"version:0001"}}, } response: llm.CallResponse = recommend_book("fantasy") print(response.content) ```  ### Metadata  ```python from mirascope import BaseDynamicConfig, Messages, llm @llm.call(provider="$PROVIDER", model="$MODEL") def recommend_book(genre: str) -> BaseDynamicConfig: return { "messages": [Messages.User(f"Recommend a {genre} book")], # [!code highlight] "call_params": {"max_tokens": 512}, "metadata": {"tags": {"version:0001"}}, # [!code highlight] } response: llm.CallResponse = recommend_book("fantasy") print(response.content) ```  ```python from mirascope import BaseDynamicConfig, llm, prompt_template @llm.call(provider="$PROVIDER", model="$MODEL") @prompt_template("Recommend a {genre} book") # [!code highlight] def recommend_book(genre: str) -> BaseDynamicConfig: return { "call_params": {"max_tokens": 512}, "metadata": {"tags": {"version:0001"}}, # [!code highlight] } response: llm.CallResponse = recommend_book("fantasy") print(response.content) ```  ## Provider-Specific Usage  For details on provider-specific modules, see the API documentation for each provider: - [`mirascope.core.openai.call`](/docs/mirascope/api/core/openai/call) - [`mirascope.core.anthropic.call`](/docs/mirascope/api/core/anthropic/call) - [`mirascope.core.mistral.call`](/docs/mirascope/api/core/mistral/call) - [`mirascope.core.google.call`](/docs/mirascope/api/core/google/call) - [`mirascope.core.azure.call`](/docs/mirascope/api/core/azure/call) - [`mirascope.core.cohere.call`](/docs/mirascope/api/core/cohere/call) - [`mirascope.core.groq.call`](/docs/mirascope/api/core/groq/call) - [`mirascope.core.xai.call`](/docs/mirascope/api/core/xai/call) - [`mirascope.core.bedrock.call`](/docs/mirascope/api/core/bedrock/call) - [`mirascope.core.litellm.call`](/docs/mirascope/api/core/litellm/call)  While Mirascope provides a consistent interface across different LLM providers, you can also use provider-specific modules with refined typing for an individual provider. When using the provider modules, you'll receive a provider-specific `BaseCallResponse` object, which may have extra properties. Regardless, you can always access the full, provider-specific response object as `response.response`.  The reason that we have provider-specific response objects (e.g. `OpenAICallResponse`) is to provide proper type hints and safety when accessing the original response.  ### Custom Messages When using provider-specific calls, you can also always return the original message types for that provider. To do so, simply return the provider-specific dynamic config:  Support for provider-specific messages ensures that you can still access newly released provider-specific features that Mirascope may not yet support natively. ### Custom Client Mirascope allows you to use custom clients when making calls to LLM providers. This feature is particularly useful when you need to use specific client configurations, handle authentication in a custom way, or work with self-hosted models. __Decorator Parameter:__ You can pass a client to the `call` decorator using the `client` parameter:  __Dynamic Configuration:__ You can also configure the client dynamically at runtime through the dynamic configuration:  A common mistake is to use the synchronous client with async calls. Read the section on [Async Custom Client](/docs/mirascope/learn/async#custom-client) to see how to use a custom client with asynchronous calls.  ## Error Handling When making LLM calls, it's important to handle potential errors. Mirascope preserves the original error messages from providers, allowing you to catch and handle them appropriately:  ```python from mirascope import llm @llm.call(provider="$PROVIDER", model="$MODEL") def recommend_book(genre: str) -> str: return f"Recommend a {genre} book" try: response: llm.CallResponse = recommend_book("fantasy") # [!code highlight] print(response.content) except Exception as e: print(f"Error: {str(e)}") # [!code highlight] ```  ```python from mirascope import llm, prompt_template @llm.call(provider="$PROVIDER", model="$MODEL") @prompt_template("Recommend a {genre} book") def recommend_book(genre: str): ... try: response: llm.CallResponse = recommend_book("fantasy") # [!code highlight] print(response.content) except Exception as e: print(f"Error: {str(e)}") # [!code highlight] ```  These examples catch the base Exception class; however, you can (and should) catch provider-specific exceptions instead when using provider-specific modules. ## Next Steps By mastering calls in Mirascope, you'll be well-equipped to build robust, flexible, and reusable LLM applications. Next, we recommend choosing one of: - [Streams](/docs/mirascope/learn/streams) to see how to stream call responses for a more real-time interaction. - [Chaining](/docs/mirascope/learn/chaining) to see how to chain calls together. - [Response Models](/docs/mirascope/learn/response_models) to see how to generate structured outputs. - [Tools](/docs/mirascope/learn/tools) to see how to give LLMs access to custom tools to extend their capabilities. - [Async](/docs/mirascope/learn/async) to see how to better take advantage of asynchronous programming and parallelization for improved performance. Pick whichever path aligns best with what you're hoping to get from Mirascope. 

 # Streams  If you haven't already, we recommend first reading the section on [Calls](/docs/mirascope/learn/calls)  Streaming is a powerful feature when using LLMs that allows you to process chunks of an LLM response in real-time as they are generated. This can be particularly useful for long-running tasks, providing immediate feedback to users, or implementing more responsive applications.  ```mermaid sequenceDiagram participant User participant App participant LLM User->>App: Request App->>LLM: Query Note right of LLM: Standard Response LLM-->>App: Complete Response App-->>User: Display Result User->>App: Request App->>LLM: Query (Stream) Note right of LLM: Streaming Response loop For each chunk LLM-->>App: Response Chunk App-->>User: Display Chunk end ```  This approach offers several benefits: 1. **Immediate feedback**: Users can see responses as they're being generated, creating a more interactive experience. 2. **Reduced latency**: For long responses, users don't have to wait for the entire generation to complete before seeing results. 3. **Incremental processing**: Applications can process and act on partial results as they arrive. 4. **Efficient resource use**: Memory usage can be optimized by processing chunks instead of storing the entire response. 5. **Early termination**: If the desired information is found early in the response, processing can be stopped without waiting for the full generation.  [`mirascope.core.base.stream`](/docs/mirascope/api/core/base/stream)  ## Basic Usage and Syntax To use streaming, simply set the `stream` parameter to `True` in your [`call`](/docs/mirascope/learn/calls) decorator:  ```python from mirascope import llm @llm.call(provider="$PROVIDER", model="$MODEL", stream=True) # [!code highlight] def recommend_book(genre: str) -> str: return f"Recommend a {genre} book" stream = recommend_book("fantasy") # [!code highlight] for chunk, _ in stream: # [!code highlight] print(chunk.content, end="", flush=True) # [!code highlight] ```  ```python from mirascope import llm, prompt_template @llm.call(provider="$PROVIDER", model="$MODEL", stream=True) # [!code highlight] @prompt_template("Recommend a {genre} book") def recommend_book(genre: str): ... stream = recommend_book("fantasy") # [!code highlight] for chunk, _ in stream: # [!code highlight] print(chunk.content, end="", flush=True) # [!code highlight] print(f"Content: {stream.content}") call_response = stream.construct_call_response() print(f"Usage: {call_response.usage}") ```  In this example: 1. We use the `call` decorator with `stream=True` to enable streaming. 2. The `recommend_book` function now returns a generator that yields `(chunk, tool)` tuples of the response. 3. We iterate over the chunks, printing each one as it's received. 4. We use `end=""` and `flush=True` parameters in the print function to ensure that the output is displayed in real-time without line breaks. ## Handling Streamed Responses  [`mirascope.core.base.call_response_chunk`](/docs/mirascope/api/core/base/call_response_chunk)  When streaming, the initial response will be a provider-specific [`BaseStream`](/docs/mirascope/api) instance (e.g. `OpenAIStream`), which is a generator that yields tuples `(chunk, tool)` where `chunk` is a provider-specific [`BaseCallResponseChunk`](/docs/mirascope/api) (e.g. `OpenAICallResponseChunk`) that wraps the original chunk in the provider's response. These objects provide a consistent interface across providers while still allowing access to provider-specific details.  You'll notice in the above example that we ignore the `tool` in each tuple. If no tools are set in the call, then `tool` will always be `None` and can be safely ignored. For more details, check out the documentation on [streaming tools](/docs/mirascope/learn/tools#streaming-tools)  ### Common Chunk Properties and Methods All `BaseCallResponseChunk` objects share these common properties: - `content`: The main text content of the response. If no content is present, this will be the empty string. - `finish_reasons`: A list of reasons why the generation finished (e.g., "stop", "length"). These will be typed specifically for the provider used. If no finish reasons are present, this will be `None`. - `model`: The name of the model used for generation. - `id`: A unique identifier for the response if available. Otherwise this will be `None`. - `usage`: Information about token usage for the call if available. Otherwise this will be `None`. - `input_tokens`: The number of input tokens used if available. Otherwise this will be `None`. - `output_tokens`: The number of output tokens generated if available. Otherwise this will be `None`. ### Common Stream Properties and Methods  To access these properties, you must first exhaust the stream by iterating through it.  Once exhausted, all `BaseStream` objects share the [same common properties and methods as `BaseCallResponse`](/docs/mirascope/learn/calls#common-response-properties-and-methods), except for `usage`, `tools`, `tool`, and `__str__`.  ```python from mirascope import llm @llm.call(provider="$PROVIDER", model="$MODEL", stream=True) def recommend_book(genre: str) -> str: return f"Recommend a {genre} book" stream = recommend_book("fantasy") for chunk, _ in stream: print(chunk.content, end="", flush=True) print(f"Content: {stream.content}") # [!code highlight] ```  ```python from mirascope import llm, prompt_template @llm.call(provider="$PROVIDER", model="$MODEL", stream=True) @prompt_template("Recommend a {genre} book") def recommend_book(genre: str): ... stream = recommend_book("fantasy") for chunk, _ in stream: print(chunk.content, end="", flush=True) print(f"Content: {stream.content}") # [!code highlight] ```  You can access the additional missing properties by using the method `construct_call_response` to reconstruct a provider-specific `BaseCallResponse` instance:  ```python from mirascope import llm @llm.call(provider="$PROVIDER", model="$MODEL", stream=True) def recommend_book(genre: str) -> str: return f"Recommend a {genre} book" stream = recommend_book("fantasy") for chunk, _ in stream: print(chunk.content, end="", flush=True) print(f"Content: {stream.content}") call_response = stream.construct_call_response() # [!code highlight] print(f"Usage: {call_response.usage}") # [!code highlight] ```  ```python from mirascope import llm, prompt_template @llm.call(provider="$PROVIDER", model="$MODEL", stream=True) @prompt_template("Recommend a {genre} book") def recommend_book(genre: str): ... stream = recommend_book("fantasy") for chunk, _ in stream: print(chunk.content, end="", flush=True) print(f"Content: {stream.content}") call_response = stream.construct_call_response() # [!code highlight] print(f"Usage: {call_response.usage}") # [!code highlight] ```  While we try our best to reconstruct the `BaseCallResponse` instance from the stream, there's always a chance that some information present in a standard call might be missing from the stream.  ### Provider-Specific Response Details While Mirascope provides a consistent interface, you can always access the full, provider-specific response object if needed. This is available through the `chunk` property of the `BaseCallResponseChunk` object:  ```python from mirascope import llm @llm.call(provider="$PROVIDER", model="$MODEL", stream=True) def recommend_book(genre: str) -> str: return f"Recommend a {genre} book" stream = recommend_book("fantasy") for chunk, _ in stream: print(f"Original chunk: {chunk.chunk}") # [!code highlight] print(chunk.content, end="", flush=True) ```  ```python from mirascope import llm, prompt_template @llm.call(provider="$PROVIDER", model="$MODEL", stream=True) @prompt_template("Recommend a {genre} book") def recommend_book(genre: str): ... stream = recommend_book("fantasy") for chunk, _ in stream: print(f"Original chunk: {chunk.chunk}") # [!code highlight] print(chunk.content, end="", flush=True) ```  The reason that we have provider-specific response objects (e.g. `OpenAICallResponseChunk`) is to provide proper type hints and safety when accessing the original response chunk.  ## Multi-Modal Outputs While most LLM providers focus on text streaming, some providers support streaming additional output modalities like audio. The availability of multi-modal streaming varies among providers: | Provider | Text | Audio | Image | |---------------|:------:|:-------:|:-------:| | OpenAI | ✓ | ✓ | — | | Anthropic | ✓ | — | — | | Mistral | ✓ | — | — | | Google Gemini | ✓ | — | — | | Groq | ✓ | — | — | | Cohere | ✓ | — | — | | LiteLLM | ✓ | — | — | | Azure AI | ✓ | — | — | *Legend: ✓ (Supported), — (Not Supported)* ### Audio Streaming For providers that support audio outputs, you can stream both text and audio responses simultaneously:  Each stream chunk provides access to: - `chunk.audio`: Raw audio data in bytes format - `chunk.audio_transcript`: The transcript of the audio This allows you to process both text and audio streams concurrently. Since audio data is received in chunks, you could technically begin playback before receiving the complete response.  The example above uses `pydub` and `ffmpeg` for audio playback, but you can use any audio processing libraries or media players that can handle WAV format audio data. Choose the tools that best fit your needs and environment. If you decide to use pydub: - Install [pydub](https://github.com/jiaaro/pydub): `pip install pydub` - Install ffmpeg: Available from [ffmpeg.org](https://www.ffmpeg.org/) or through system package managers  For providers that support audio outputs, refer to their documentation for available voice options and configurations: - OpenAI: [Text to Speech Guide](https://platform.openai.com/docs/guides/text-to-speech)  ## Error Handling Error handling in streams is similar to standard non-streaming calls. However, it's important to note that errors may occur during iteration rather than at the initial function call:  In these examples we show provider-specific error handling, though you can also catch generic exceptions. Note how we wrap the iteration loop in a try/except block to catch any errors that might occur during streaming.  The initial response when calling an LLM function with `stream=True` will return a generator. Any errors that may occur during streaming will not happen until you actually iterate through the generator. This is why we wrap the generation loop in the try/except block and not just the call to `recommend_book`.  ## Next Steps By leveraging streaming effectively, you can create more responsive and efficient LLM-powered applications with Mirascope's streaming capabilities. Next, we recommend taking a look at the [Chaining](/docs/mirascope/learn/chaining) documentation, which shows you how to break tasks down into smaller, more directed calls and chain them together. 

 # Chaining  If you haven't already, we recommend first reading the section on [Calls](/docs/mirascope/learn/calls)  Chaining in Mirascope allows you to combine multiple LLM calls or operations in a sequence to solve complex tasks. This approach is particularly useful for breaking down complex problems into smaller, manageable steps. Before diving into Mirascope's implementation, let's understand what chaining means in the context of LLM applications: 1. **Problem Decomposition**: Breaking a complex task into smaller, manageable steps. 2. **Sequential Processing**: Executing these steps in a specific order, where the output of one step becomes the input for the next. 3. **Data Flow**: Passing information between steps to build up a final result. ## Basic Usage and Syntax ### Function Chaining Mirascope is designed to be Pythonic. Since calls are defined as functions, chaining them together is as simple as chaining the function calls as you would normally:  ```python from mirascope import llm @llm.call(provider="$PROVIDER", model="$MODEL") def summarize(text: str) -> str: # [!code highlight] return f"Summarize this text: {text}" @llm.call(provider="$PROVIDER", model="$MODEL") def translate(text: str, language: str) -> str: # [!code highlight] return f"Translate this text to {language}: {text}" summary = summarize("Long English text here...") # [!code highlight] translation = translate(summary.content, "french") # [!code highlight] print(translation.content) ```  ```python from mirascope import llm, prompt_template @llm.call(provider="$PROVIDER", model="$MODEL") @prompt_template("Summarize this text: {text}") def summarize(text: str): ... # [!code highlight] @llm.call(provider="$PROVIDER", model="$MODEL") @prompt_template("Translate this text to {language}: {text}") def translate(text: str, language: str): ... # [!code highlight] summary = summarize("Long English text here...") # [!code highlight] translation = translate(summary.content, "french") # [!code highlight] print(translation.content) ```  One benefit of this approach is that you can chain your calls together any which way since they are just functions. You can then always wrap these functional chains in a parent function that operates as the single call to the chain. ### Nested Chains In some cases you'll want to prompt engineer an entire chain rather than just chaining together individual calls. You can do this simply by calling the subchain inside the function body of the parent:  ```python from mirascope import llm @llm.call(provider="$PROVIDER", model="$MODEL") def summarize(text: str) -> str: # [!code highlight] return f"Summarize this text: {text}" @llm.call(provider="$PROVIDER", model="$MODEL") def summarize_and_translate(text: str, language: str) -> str: summary = summarize(text) # [!code highlight] return f"Translate this text to {language}: {summary.content}" # [!code highlight] response = summarize_and_translate("Long English text here...", "french") print(response.content) # [!code highlight] ```  ```python from mirascope import BaseDynamicConfig, llm, prompt_template @llm.call(provider="$PROVIDER", model="$MODEL") @prompt_template("Summarize this text: {text}") def summarize(text: str): ... # [!code highlight] @llm.call(provider="$PROVIDER", model="$MODEL") @prompt_template("Translate this text to {language}: {summary}") # [!code highlight] def summarize_and_translate(text: str, language: str) -> BaseDynamicConfig: return {"computed_fields": {"summary": summarize(text)}} # [!code highlight] response = summarize_and_translate("Long English text here...", "french") print(response.content) # [!code highlight] ```  We recommend using nested chains for better observability when using tracing tools or applications.  If you use computed fields in your nested chains, you can always access the computed field in the response. This provides improved tracing for your chains from a single call.   ```python from mirascope import BaseDynamicConfig, Messages, llm @llm.call(provider="$PROVIDER", model="$MODEL") def summarize(text: str) -> str: return f"Summarize this text: {text}" @llm.call(provider="$PROVIDER", model="$MODEL") def summarize_and_translate(text: str, language: str) -> BaseDynamicConfig: summary = summarize(text) return { "messages": [ Messages.User(f"Translate this text to {language}: {summary.content}") ], "computed_fields": {"summary": summary}, } response = summarize_and_translate("Long English text here...", "french") print(response.content) print( response.model_dump()["computed_fields"] ) # This will contain the `summarize` response ```   ```python from mirascope import BaseDynamicConfig, llm, prompt_template @llm.call(provider="$PROVIDER", model="$MODEL") @prompt_template("Summarize this text: {text}") def summarize(text: str): ... @llm.call(provider="$PROVIDER", model="$MODEL") @prompt_template("Translate this text to {language}: {summary}") def summarize_and_translate(text: str, language: str) -> BaseDynamicConfig: return {"computed_fields": {"summary": summarize(text)}} response = summarize_and_translate("Long English text here...", "french") print(response.content) print( response.model_dump()["computed_fields"] ) # This will contain the `summarize` response ```   ## Advanced Chaining Techniques There are many different ways to chain calls together, often resulting in breakdowns and flows that are specific to your task. Here are a few examples:  ```python from enum import Enum from mirascope import BaseDynamicConfig, llm, prompt_template class Sentiment(str, Enum): POSITIVE = "positive" NEGATIVE = "negative" @llm.call(provider="$PROVIDER", model="$MODEL", response_model=Sentiment) def sentiment_classifier(review: str) -> str: return f"Is the following review positive or negative? {review}" @llm.call(provider="$PROVIDER", model="$MODEL") @prompt_template( """ SYSTEM: Your task is to respond to a review. The review has been identified as {sentiment}. Please write a {conditional_review_prompt}. USER: Write a response for the following review: {review} """ ) def review_responder(review: str) -> BaseDynamicConfig: sentiment = sentiment_classifier(review=review) conditional_review_prompt = ( "thank you response for the review." if sentiment == Sentiment.POSITIVE else "response addressing the review." ) return { "computed_fields": { "conditional_review_prompt": conditional_review_prompt, "sentiment": sentiment, } } positive_review = "This tool is awesome because it's so flexible!" response = review_responder(review=positive_review) print(response) print(response.dynamic_config) ```  ```python import asyncio from mirascope import BaseDynamicConfig, llm, prompt_template from pydantic import BaseModel @llm.call(provider="$PROVIDER", model="$MODEL") @prompt_template( """ Please identify a chef who is well known for cooking with {ingredient}. Respond only with the chef's name. """ ) async def chef_selector(ingredient: str): ... class IngredientsList(BaseModel): ingredients: list[str] @llm.call(provider="$PROVIDER", model="$MODEL", response_model=IngredientsList) @prompt_template( """ Given a base ingredient {ingredient}, return a list of complementary ingredients. Make sure to exclude the original ingredient from the list. """ ) async def ingredients_identifier(ingredient: str): ... @llm.call(provider="$PROVIDER", model="$MODEL") @prompt_template( """ SYSTEM: Your task is to recommend a recipe. Pretend that you are chef {chef}. USER: Recommend recipes that use the following ingredients: {ingredients} """ ) async def recipe_recommender(ingredient: str) -> BaseDynamicConfig: chef, ingredients = await asyncio.gather( chef_selector(ingredient), ingredients_identifier(ingredient) ) return {"computed_fields": {"chef": chef, "ingredients": ingredients}} async def run(): response = await recipe_recommender(ingredient="apples") print(response.content) asyncio.run(run()) ```  ```python from mirascope import llm, prompt_template from pydantic import BaseModel, Field class SummaryFeedback(BaseModel): """Feedback on summary with a critique and review rewrite based on said critique.""" critique: str = Field(..., description="The critique of the summary.") rewritten_summary: str = Field( ..., description="A rewritten summary that takes the critique into account.", ) @llm.call(provider="$PROVIDER", model="$MODEL") def summarizer(original_text: str) -> str: return f"Summarize the following text into one sentence: {original_text}" @llm.call(provider="$PROVIDER", model="$MODEL", response_model=SummaryFeedback) @prompt_template( """ Original Text: {original_text} Summary: {summary} Critique the summary of the original text. Then rewrite the summary based on the critique. It must be one sentence. """ ) def resummarizer(original_text: str, summary: str): ... def rewrite_iteratively(original_text: str, summary: str, depth=2): text = original_text for _ in range(depth): text = resummarizer(original_text=text, summary=summary).rewritten_summary return text original_text = """ In the heart of a dense forest, a boy named Timmy pitched his first tent, fumbling with the poles and pegs. His grandfather, a seasoned camper, guided him patiently, their bond strengthening with each knot tied. As night fell, they sat by a crackling fire, roasting marshmallows and sharing tales of old adventures. Timmy marveled at the star-studded sky, feeling a sense of wonder he'd never known. By morning, the forest had transformed him, instilling a love for the wild that would last a lifetime. """ summary = summarizer(original_text=original_text).content print(f"Summary: {summary}") rewritten_summary = rewrite_iteratively(original_text, summary) print(f"Rewritten Summary: {rewritten_summary}") ```  [Response Models](/docs/mirascope/learn/response_models) are a great way to add more structure to your chains, and [parallel async calls](/docs/mirascope/learn/async#parallel-async-calls) can be particularly powerful for making your chains more efficient. ## Next Steps By mastering Mirascope's chaining techniques, you can create sophisticated LLM-powered applications that tackle complex, multi-step problems with greater accuracy, control, and observability. Next, we recommend taking a look at the [Response Models](/docs/mirascope/learn/response_models) documentation, which shows you how to generate structured outputs. 

 # Response Models  If you haven't already, we recommend first reading the section on [Calls](/docs/mirascope/learn/calls)  Response Models in Mirascope provide a powerful way to structure and validate the output from Large Language Models (LLMs). By leveraging Pydantic's [`BaseModel`](https://docs.pydantic.dev/latest/usage/models/), Response Models offer type safety, automatic validation, and easier data manipulation for your LLM responses. While we cover some details in this documentation, we highly recommend reading through Pydantic's documentation for a deeper, comprehensive dive into everything you can do with Pydantic's `BaseModel`. ## Why Use Response Models? 1. **Structured Output**: Define exactly what you expect from the LLM, ensuring consistency in responses. 2. **Automatic Validation**: Pydantic handles type checking and validation, reducing errors in your application. 3. **Improved Type Hinting**: Better IDE support and clearer code structure. 4. **Easier Data Manipulation**: Work with Python objects instead of raw strings or dictionaries. ## Basic Usage and Syntax Let's take a look at a basic example using Mirascope vs. official provider SDKs:  ```python from mirascope import llm from pydantic import BaseModel class Book(BaseModel): """An extracted book.""" title: str author: str @llm.call(provider="$PROVIDER", model="$MODEL", response_model=Book) # [!code highlight] def extract_book(text: str) -> str: return f"Extract {text}" book = extract_book("The Name of the Wind by Patrick Rothfuss") print(book) # Output: title='The Name of the Wind' author='Patrick Rothfuss' # [!code highlight] ```  ```python from mirascope import llm, prompt_template from pydantic import BaseModel class Book(BaseModel): """An extracted book.""" title: str author: str @llm.call(provider="$PROVIDER", model="$MODEL", response_model=Book) # [!code highlight] @prompt_template("Extract {text}") def extract_book(text: str): ... book = extract_book("The Name of the Wind by Patrick Rothfuss") print(book) # Output: title='The Name of the Wind' author='Patrick Rothfuss' # [!code highlight] ```  Notice how Mirascope makes generating structured outputs significantly simpler than the official SDKs. It also greatly reduces boilerplate and standardizes the interaction across all supported LLM providers.  By default, `response_model` will use [Tools](/docs/mirascope/learn/tools) under the hood, forcing to the LLM to call that specific tool and constructing the response model from the tool's arguments. We default to using tools because all supported providers support tools. You can also optionally set `json_mode=True` to use [JSON Mode](/docs/mirascope/learn/json_mode) instead, which we cover in [more detail below](#json-mode).  ### Accessing Original Call Response Every `response_model` that uses a Pydantic `BaseModel` will automatically have the original `BaseCallResponse` instance accessible through the `_response` property:  ```python from mirascope import llm from pydantic import BaseModel class Book(BaseModel): """An extracted book.""" title: str author: str @llm.call(provider="$PROVIDER", model="$MODEL", response_model=Book) def extract_book(text: str) -> str: return f"Extract {text}" book = extract_book("The Name of the Wind by Patrick Rothfuss") print(book) # Output: title='The Name of the Wind' author='Patrick Rothfuss' response = book._response # pyright: ignore[reportAttributeAccessIssue] # [!code highlight] print(response.model_dump()) # [!code highlight] # > {'metadata': {}, 'response': {'id': ...}, ...} # [!code highlight] ```  ```python from mirascope import llm, prompt_template from pydantic import BaseModel class Book(BaseModel): """An extracted book.""" title: str author: str @llm.call(provider="$PROVIDER", model="$MODEL", response_model=Book) @prompt_template("Extract {text}") def extract_book(text: str): ... book = extract_book("The Name of the Wind by Patrick Rothfuss") print(book) # Output: title='The Name of the Wind' author='Patrick Rothfuss' response = book._response # pyright: ignore[reportAttributeAccessIssue] # [!code highlight] print(response.model_dump()) # [!code highlight] # > {'metadata': {}, 'response': {'id': ...}, ...} # [!code highlight] ```  ### Built-In Types For cases where you want to extract just a single built-in type, Mirascope provides a shorthand:  ```python from mirascope import llm @llm.call(provider="$PROVIDER", model="$MODEL", response_model=list[str]) # [!code highlight] def extract_book(texts: list[str]) -> str: return f"Extract book titles from {texts}" book = extract_book( [ "The Name of the Wind by Patrick Rothfuss", "Mistborn: The Final Empire by Brandon Sanderson", ] ) print(book) # Output: ["The Name of the Wind", "Mistborn: The Final Empire"] # [!code highlight] ```  ```python from mirascope import llm, prompt_template @llm.call(provider="$PROVIDER", model="$MODEL", response_model=list[str]) # [!code highlight] @prompt_template("Extract book titles from {texts}") def extract_book(texts: list[str]): ... book = extract_book( [ "The Name of the Wind by Patrick Rothfuss", "Mistborn: The Final Empire by Brandon Sanderson", ] ) print(book) # Output: ["The Name of the Wind", "Mistborn: The Final Empire"] # [!code highlight] ```  Here, we are using `list[str]` as the `response_model`, which Mirascope handles without needing to define a full `BaseModel`. You could of course set `response_model=list[Book]` as well. Note that we have no way of attaching `BaseCallResponse` to built-in types, so using a Pydantic `BaseModel` is recommended if you anticipate needing access to the original call response. ## Supported Field Types While Mirascope provides a consistent interface, type support varies among providers: | Type | OpenAI | Anthropic | Google | Groq | xAI | Mistral | Cohere | |---------------|--------|-----------|--------|------|-----|---------|--------| | str |✓✓ |✓✓ |✓✓ |✓✓ |✓✓ |✓✓ |✓✓ | | int |✓✓ |✓✓ |✓✓ |✓✓ |✓✓ |✓✓ |✓✓ | | float |✓✓ |✓✓ |✓✓ |✓✓ |✓✓ |✓✓ |✓✓ | | bool |✓✓ |✓✓ |✓✓ |✓✓ |✓✓ |✓✓ |✓✓ | | bytes |✓✓ |✓✓ |-✓ |✓✓ |✓✓ |✓✓ |✓✓ | | list |✓✓ |✓✓ |✓✓ |✓✓ |✓✓ |✓✓ |✓✓ | | set |✓✓ |✓✓ |-- |✓✓ |✓✓ |✓✓ |✓✓ | | tuple |-✓ |✓✓ |-✓ |✓✓ |-✓ |✓✓ |✓✓ | | dict |-✓ |✓✓ |✓✓ |✓✓ |-✓ |✓✓ |✓✓ | | Literal/Enum |✓✓ |✓✓ |✓✓ |✓✓ |✓✓ |✓✓ |✓✓ | | BaseModel |✓✓ |✓✓ |✓✓ |✓✓ |✓✓ |✓✓ |-✓ | | Nested ($def) |✓✓ |✓✓ |✓✓ |✓✓ |✓✓ |✓✓ |-- | ✓✓ : Fully Supported, -✓: Only JSON Mode Support, -- : Not supported ## Validation and Error Handling While `response_model` significantly improves output structure and validation, it's important to handle potential errors. Let's take a look at an example where we want to validate that all fields are uppercase:  ```python from typing import Annotated # [!code highlight] from mirascope import llm from pydantic import AfterValidator, BaseModel, ValidationError # [!code highlight] def validate_upper(v: str) -> str: # [!code highlight] assert v.isupper(), "Field must be uppercase" # [!code highlight] return v # [!code highlight] class Book(BaseModel): """An extracted book.""" title: Annotated[str, AfterValidator(validate_upper)] # [!code highlight] author: Annotated[str, AfterValidator(validate_upper)] # [!code highlight] @llm.call(provider="$PROVIDER", model="$MODEL", response_model=Book) def extract_book(text: str) -> str: return f"Extract {text}" try: # [!code highlight] book = extract_book("The Name of the Wind by Patrick Rothfuss") print(book) # Output: title='The Name of the Wind' author='Patrick Rothfuss' except ValidationError as e: # [!code highlight] print(f"Error: {str(e)}") # Error: 2 validation errors for Book # title # Assertion failed, Field must be uppercase [type=assertion_error, input_value='The Name of the Wind', input_type=str] # For further information visit https://errors.pydantic.dev/2.7/v/assertion_error # author # Assertion failed, Field must be uppercase [type=assertion_error, input_value='Patrick Rothfuss', input_type=str] # For further information visit https://errors.pydantic.dev/2.7/v/assertion_error ```  ```python from typing import Annotated # [!code highlight] from mirascope import llm, prompt_template from pydantic import AfterValidator, BaseModel, ValidationError # [!code highlight] def validate_upper(v: str) -> str: assert v.isupper(), "Field must be uppercase" return v class Book(BaseModel): """An extracted book.""" title: Annotated[str, AfterValidator(validate_upper)] # [!code highlight] author: Annotated[str, AfterValidator(validate_upper)] # [!code highlight] @llm.call(provider="$PROVIDER", model="$MODEL", response_model=Book) @prompt_template("Extract {text}") def extract_book(text: str): ... try: # [!code highlight] book = extract_book("The Name of the Wind by Patrick Rothfuss") print(book) # Output: title='The Name of the Wind' author='Patrick Rothfuss' except ValidationError as e: # [!code highlight] print(f"Error: {str(e)}") # Error: 2 validation errors for Book # title # Assertion failed, Field must be uppercase [type=assertion_error, input_value='The Name of the Wind', input_type=str] # For further information visit https://errors.pydantic.dev/2.7/v/assertion_error # author # Assertion failed, Field must be uppercase [type=assertion_error, input_value='Patrick Rothfuss', input_type=str] # For further information visit https://errors.pydantic.dev/2.7/v/assertion_error ```  Without additional prompt engineering, this call will fail every single time. It's important to engineer your prompts to reduce errors, but LLMs are far from perfect, so always remember to catch and handle validation errors gracefully. We highly recommend taking a look at our section on [retries](/docs/mirascope/learn/retries) to learn more about automatically retrying and re-inserting validation errors, which enables retrying the call such that the LLM can learn from its previous mistakes. ### Accessing Original Call Response On Error In case of a `ValidationError`, you can access the original response for debugging:  ```python from typing import Annotated from mirascope import llm from pydantic import AfterValidator, BaseModel, ValidationError def validate_upper(v: str) -> str: assert v.isupper(), "Field must be uppercase" return v class Book(BaseModel): """An extracted book.""" title: Annotated[str, AfterValidator(validate_upper)] author: Annotated[str, AfterValidator(validate_upper)] @llm.call(provider="$PROVIDER", model="$MODEL", response_model=Book) def extract_book(text: str) -> str: return f"Extract {text}" try: book = extract_book("The Name of the Wind by Patrick Rothfuss") print(book) except ValidationError as e: # [!code highlight] response = e._response # pyright: ignore[reportAttributeAccessIssue] # [!code highlight] print(response.model_dump()) # [!code highlight] # > {'metadata': {}, 'response': {'id': ...}, ...} # [!code highlight] ```  ```python from typing import Annotated from mirascope import llm, prompt_template from pydantic import AfterValidator, BaseModel, ValidationError def validate_upper(v: str) -> str: assert v.isupper(), "Field must be uppercase" return v class Book(BaseModel): """An extracted book.""" title: Annotated[str, AfterValidator(validate_upper)] author: Annotated[str, AfterValidator(validate_upper)] @llm.call(provider="$PROVIDER", model="$MODEL", response_model=Book) @prompt_template("Extract {text}") def extract_book(text: str): ... try: book = extract_book("The Name of the Wind by Patrick Rothfuss") print(book) except ValidationError as e: response = e._response # pyright: ignore[reportAttributeAccessIssue] # [!code highlight] print(response.model_dump()) # [!code highlight] # > {'metadata': {}, 'response': {'id': ...}, ...} # [!code highlight] ```  This allows you to gracefully handle errors as well as inspect the original LLM response when validation fails. ## JSON Mode By default, `response_model` uses [Tools](/docs/mirascope/learn/tools) under the hood. You can instead use [JSON Mode](/docs/mirascope/learn/json_mode) in conjunction with `response_model` by setting `json_mode=True`:  ```python from mirascope import llm from pydantic import BaseModel class Book(BaseModel): """An extracted book.""" title: str author: str @llm.call(provider="$PROVIDER", model="$MODEL", response_model=Book, json_mode=True) # [!code highlight] def extract_book(text: str) -> str: return f"Extract {text}" book = extract_book("The Name of the Wind by Patrick Rothfuss") print(book) # Output: title='The Name of the Wind' author='Patrick Rothfuss' ```  ```python from mirascope import llm, prompt_template from pydantic import BaseModel class Book(BaseModel): """An extracted book.""" title: str author: str @llm.call(provider="$PROVIDER", model="$MODEL", response_model=Book, json_mode=True) # [!code highlight] @prompt_template("Extract {text}") def extract_book(text: str): ... book = extract_book("The Name of the Wind by Patrick Rothfuss") print(book) # Output: title='The Name of the Wind' author='Patrick Rothfuss' ```  ## Few-Shot Examples Adding few-shot examples to your response model can improve results by demonstrating exactly how to adhere to your desired output. We take advantage of Pydantic's [`Field`](https://docs.pydantic.dev/latest/concepts/fields/) and [`ConfigDict`](https://docs.pydantic.dev/latest/concepts/config/) to add these examples to response models:  ```python from mirascope import llm from pydantic import BaseModel, ConfigDict, Field class Book(BaseModel): title: str = Field(..., examples=["THE NAME OF THE WIND"]) # [!code highlight] author: str = Field(..., examples=["Rothfuss, Patrick"]) # [!code highlight] model_config = ConfigDict( json_schema_extra={ "examples": [ # [!code highlight] {"title": "THE NAME OF THE WIND", "author": "Rothfuss, Patrick"} # [!code highlight] ] # [!code highlight] } ) @llm.call(provider="$PROVIDER", model="$MODEL", response_model=Book, json_mode=True) def extract_book(text: str) -> str: return f"Extract {text}. Match example format EXCLUDING 'examples' key." book = extract_book("The Way of Kings by Brandon Sanderson") print(book) # Output: title='THE WAY OF KINGS' author='Sanderson, Brandon' ```  ```python from mirascope import llm, prompt_template from pydantic import BaseModel, ConfigDict, Field class Book(BaseModel): title: str = Field(..., examples=["THE NAME OF THE WIND"]) # [!code highlight] author: str = Field(..., examples=["Rothfuss, Patrick"]) # [!code highlight] model_config = ConfigDict( json_schema_extra={ "examples": [ # [!code highlight] {"title": "THE NAME OF THE WIND", "author": "Rothfuss, Patrick"} # [!code highlight] ] # [!code highlight] } ) @llm.call(provider="$PROVIDER", model="$MODEL", response_model=Book, json_mode=True) @prompt_template("Extract {text}. Match example format EXCLUDING 'examples' key.") def extract_book(text: str): ... book = extract_book("The Way of Kings by Brandon Sanderson") print(book) # Output: title='THE WAY OF KINGS' author='Sanderson, Brandon' # [!code highlight] ```  ## Streaming Response Models If you set `stream=True` when `response_model` is set, your LLM call will return an `Iterable` where each item will be a partial version of your response model representing the current state of the streamed information. The final model returned by the iterator will be the full response model.  ```python from mirascope import llm from pydantic import BaseModel class Book(BaseModel): title: str author: str @llm.call(provider="$PROVIDER", model="$MODEL", response_model=Book, stream=True) # [!code highlight] def extract_book(text: str) -> str: return f"Extract {text}" book_stream = extract_book("The Name of the Wind by Patrick Rothfuss") for partial_book in book_stream: # [!code highlight] print(partial_book) # [!code highlight] # Output: # title=None author=None # title='' author=None # title='The' author=None # title='The Name' author=None # title='The Name of' author=None # title='The Name of the' author=None # title='The Name of the Wind' author=None # title='The Name of the Wind' author='' # title='The Name of the Wind' author='Patrick' # title='The Name of the Wind' author='Patrick Roth' # title='The Name of the Wind' author='Patrick Rothf' # title='The Name of the Wind' author='Patrick Rothfuss' ```  ```python from mirascope import llm, prompt_template from pydantic import BaseModel class Book(BaseModel): title: str author: str @llm.call(provider="$PROVIDER", model="$MODEL", response_model=Book, stream=True) # [!code highlight] @prompt_template("Extract {text}") def extract_book(text: str): ... book_stream = extract_book("The Name of the Wind by Patrick Rothfuss") for partial_book in book_stream: # [!code highlight] print(partial_book) # [!code highlight] # Output: # title=None author=None # title='' author=None # title='The' author=None # title='The Name' author=None # title='The Name of' author=None # title='The Name of the' author=None # title='The Name of the Wind' author=None # title='The Name of the Wind' author='' # title='The Name of the Wind' author='Patrick' # title='The Name of the Wind' author='Patrick Roth' # title='The Name of the Wind' author='Patrick Rothf' # title='The Name of the Wind' author='Patrick Rothfuss' ```  Once exhausted, you can access the final, full response model through the `constructed_response_model` property of the structured stream. Note that this will also give you access to the [`._response` property](#accessing-original-call-response) that every `BaseModel` receives. You can also use the `stream` property to access the `BaseStream` instance and [all of it's properties](/docs/mirascope/learn/streams#common-stream-properties-and-methods). ## FromCallArgs Fields annotated with `FromCallArgs` will be populated with the corresponding argument from the function call rather than expecting it from the LLM's response. This enables seamless validation of LLM outputs against function inputs:  ```python from typing import Annotated from mirascope import llm from mirascope.core import FromCallArgs from pydantic import BaseModel, model_validator from typing_extensions import Self class Book(BaseModel): title: str author: str class Books(BaseModel): texts: Annotated[list[str], FromCallArgs()] # [!code highlight] books: list[Book] @model_validator(mode="after") def validate_output_length(self) -> Self: if len(self.texts) != len(self.books): raise ValueError("length mismatch...") return self @llm.call(provider="$PROVIDER", model="$MODEL", response_model=Books) def extract_books(texts: list[str]) -> str: # [!code highlight] return f"Extract the books from these texts: {texts}" texts = [ "The Name of the Wind by Patrick Rothfuss", "Mistborn: The Final Empire by Brandon Sanderson", ] print(extract_books(texts)) # Output: # texts=[ # 'The Name of the Wind by Patrick Rothfuss', # 'Mistborn: The Final Empire by Brandon Sanderson' # ] # books=[ # Book(title='The Name of the Wind', author='Patrick Rothfuss'), # Book(title='Mistborn: The Final Empire', author='Brandon Sanderson') # ] ```  ```python from typing import Annotated from mirascope import llm, prompt_template from mirascope.core import FromCallArgs from pydantic import BaseModel, model_validator from typing_extensions import Self class Book(BaseModel): title: str author: str class Books(BaseModel): texts: Annotated[list[str], FromCallArgs()] # [!code highlight] books: list[Book] @model_validator(mode="after") def validate_output_length(self) -> Self: if len(self.texts) != len(self.books): raise ValueError("length mismatch...") return self @llm.call(provider="$PROVIDER", model="$MODEL", response_model=Books) @prompt_template("Extract the books from these texts: {texts}") def extract_books(texts: list[str]): ... # [!code highlight] texts = [ "The Name of the Wind by Patrick Rothfuss", "Mistborn: The Final Empire by Brandon Sanderson", ] print(extract_books(texts)) # Output: # texts=[ # 'The Name of the Wind by Patrick Rothfuss', # 'Mistborn: The Final Empire by Brandon Sanderson' # ] # books=[ # Book(title='The Name of the Wind', author='Patrick Rothfuss'), # Book(title='Mistborn: The Final Empire', author='Brandon Sanderson') # ] ```  ## Next Steps By following these best practices and leveraging Response Models effectively, you can create more robust, type-safe, and maintainable LLM-powered applications with Mirascope. Next, we recommend taking a look at one of: - [JSON Mode](/docs/mirascope/learn/json_mode) to see an alternate way to generate structured outputs where using Pydantic to validate outputs is optional. - [Evals](/docs/mirascope/learn/evals) to see how to use `response_model` to evaluate your prompts. 

 # JSON Mode  If you haven't already, we recommend first reading the section on [Calls](/docs/mirascope/learn/calls)  JSON Mode is a feature in Mirascope that allows you to request structured JSON output from Large Language Models (LLMs). This mode is particularly useful when you need to extract structured information from the model's responses, making it easier to parse and use the data in your applications.  For providers with explicit support, Mirascope uses the native JSON Mode feature of the API. For providers without explicit support (e.g. Anthropic), Mirascope implements a pseudo JSON Mode by instructing the model in the prompt to output JSON. | Provider | Support Type | Implementation | |-----------|--------------|---------------------| | Anthropic | Pseudo | Prompt engineering | | Azure | Explicit | Native API feature | | Bedrock | Pseudo | Prompt engineering | | Cohere | Pseudo | Prompt engineering | | Google | Explicit | Native API feature | | Groq | Explicit | Native API feature | | LiteLLM | Explicit | Native API feature | | Mistral | Explicit | Native API feature | | OpenAI | Explicit | Native API feature | If you'd prefer not to have any internal updates made to your prompt, you can always set JSON mode yourself through `call_params` rather than using the `json_mode` argument, which provides provider-agnostic support but is certainly not required to use JSON mode.  ## Basic Usage and Syntax Let's take a look at a basic example using JSON Mode:  ```python import json from mirascope import llm @llm.call(provider="$PROVIDER", model="$MODEL", json_mode=True) # [!code highlight] def get_book_info(book_title: str) -> str: # [!code highlight] return f"Provide the author and genre of {book_title}" response = get_book_info("The Name of the Wind") print(json.loads(response.content)) # Output: {'author': 'Patrick Rothfuss', 'genre': 'Fantasy'} # [!code highlight] ```  ```python import json from mirascope import llm, prompt_template @llm.call(provider="$PROVIDER", model="$MODEL", json_mode=True) # [!code highlight] @prompt_template("Provide the author and genre of {book_title}") # [!code highlight] def get_book_info(book_title: str): ... response = get_book_info("The Name of the Wind") print(json.loads(response.content)) # Output: {'author': 'Patrick Rothfuss', 'genre': 'Fantasy'} # [!code highlight] ```  In this example we 1. Enable JSON Mode with `json_mode=True` in the `call` decorator 2. Instruct the model what fields to include in our prompt 3. Load the JSON string response into a Python object and print it ## Error Handling and Validation While JSON Mode can significantly improve the structure of model outputs, it's important to note that it's far from infallible. LLMs often produce invalid JSON or deviate from the expected structure, so it's crucial to implement proper error handling and validation in your code:  ```python import json from mirascope import llm @llm.call(provider="$PROVIDER", model="$MODEL", json_mode=True) def get_book_info(book_title: str) -> str: return f"Provide the author and genre of {book_title}" try: # [!code highlight] response = get_book_info("The Name of the Wind") print(json.loads(response.content)) except json.JSONDecodeError: # [!code highlight] print("The model produced invalid JSON") ```  ```python import json from mirascope import llm, prompt_template @llm.call(provider="$PROVIDER", model="$MODEL", json_mode=True) @prompt_template("Provide the author and genre of {book_title}") def get_book_info(book_title: str): ... try: # [!code highlight] response = get_book_info("The Name of the Wind") print(json.loads(response.content)) except json.JSONDecodeError: # [!code highlight] print("The model produced invalid JSON") ```  While this example catches errors for invalid JSON, there's always a chance that the LLM returns valid JSON that doesn't conform to your expected schema (such as missing fields or incorrect types). For more robust validation, we recommend using [Response Models](/docs/mirascope/learn/response_models) for easier structuring and validation of LLM outputs.  ## Next Steps By leveraging JSON Mode, you can create more robust and data-driven applications that efficiently process and utilize LLM outputs. This approach allows for easy integration with databases, APIs, or user interfaces, demonstrating the power of JSON Mode in creating robust, data-driven applications. Next, we recommend reading the section on [Output Parsers](/docs/mirascope/learn/output_parsers) to see how to engineer prompts with specific output structures and parse the outputs automatically on every call. 

 # Output Parsers  If you haven't already, we recommend first reading the section on [Calls](/docs/mirascope/learn/calls)  Output Parsers in Mirascope provide a flexible way to process and structure the raw output from Large Language Models (LLMs). They allow you to transform the LLM's response into a more usable format, enabling easier integration with your application logic and improving the overall reliability of your LLM-powered features. ## Basic Usage and Syntax  [`mirascope.llm.call.output_parser`](/docs/mirascope/api/llm/call)  Output Parsers are functions that take the call response object as input and return an output of a specified type. When you supply an output parser to a `call` decorator, it modifies the return type of the decorated function to match the output type of the parser. Let's take a look at a basic example:  ```python from mirascope import llm def parse_recommendation(response: llm.CallResponse) -> tuple[str, str]: title, author = response.content.split(" by ") return (title, author) @llm.call(provider="$PROVIDER", model="$MODEL", output_parser=parse_recommendation) # [!code highlight] def recommend_book(genre: str) -> str: return f"Recommend a {genre} book. Output only Title by Author" print(recommend_book("fantasy")) # Output: ('"The Name of the Wind"', 'Patrick Rothfuss') # [!code highlight] ```  ```python from mirascope import llm, prompt_template def parse_recommendation(response: llm.CallResponse) -> tuple[str, str]: title, author = response.content.split(" by ") return (title, author) @llm.call(provider="$PROVIDER", model="$MODEL", output_parser=parse_recommendation) # [!code highlight] @prompt_template("Recommend a {genre} book. Output only Title by Author") def recommend_book(genre: str): ... print(recommend_book("fantasy")) # Output: ('"The Name of the Wind"', 'Patrick Rothfuss') # [!code highlight] ```  ## Additional Examples There are many different ways to structure and parse LLM outputs, ranging from XML parsing to using regular expressions. Here are a few examples:  ```python import re from mirascope import llm, prompt_template def parse_cot(response: llm.CallResponse) -> str: pattern = r".*?.*?(.*?)" # [!code highlight] match = re.search(pattern, response.content, re.DOTALL) if not match: return response.content return match.group(1).strip() @llm.call(provider="$PROVIDER", model="$MODEL", output_parser=parse_cot) # [!code highlight] @prompt_template( """ First, output your thought process in  tags. # [!code highlight] Then, provide your final output in  tags. # [!code highlight] Question: {question} """ ) def chain_of_thought(question: str): ... question = "Roger has 5 tennis balls. He buys 2 cans of 3. How many does he have now?" output = chain_of_thought(question) print(output) ```  ```python import xml.etree.ElementTree as ET from mirascope import llm, prompt_template from pydantic import BaseModel class Book(BaseModel): title: str author: str year: int summary: str # [!code highlight:16] def parse_book_xml(response: llm.CallResponse) -> Book | None: try: root = ET.fromstring(response.content) if (node := root.find("title")) is None or not (title := node.text): raise ValueError("Missing title") if (node := root.find("author")) is None or not (author := node.text): raise ValueError("Missing author") if (node := root.find("year")) is None or not (year := node.text): raise ValueError("Missing year") if (node := root.find("summary")) is None or not (summary := node.text): raise ValueError("Missing summary") return Book(title=title, author=author, year=int(year), summary=summary) except (ET.ParseError, ValueError) as e: print(f"Error parsing XML: {e}") return None @llm.call(provider="$PROVIDER", model="$MODEL", output_parser=parse_book_xml) # [!code highlight] @prompt_template( """ Recommend a {genre} book. Provide the information in the following XML format: # [!code highlight:7]  Book Title

===============
 Author Name Publication Year Brief summary of the book  Output ONLY the XML and no other text. """ ) def recommend_book(genre: str): ... book = recommend_book("science fiction") if book: print(f"Title: {book.title}") print(f"Author: {book.author}") print(f"Year: {book.year}") print(f"Summary: {book.summary}") else: print("Failed to parse the recommendation.") ```  ```python import json from mirascope import llm def only_json(response: llm.CallResponse) -> str: json_start = response.content.index("{") # [!code highlight] json_end = response.content.rfind("}") # [!code highlight] return response.content[json_start : json_end + 1] # [!code highlight] @llm.call( # [!code highlight] provider="$PROVIDER", model="$MODEL", json_mode=True, output_parser=only_json # [!code highlight] ) # [!code highlight] def json_extraction(text: str, fields: list[str]) -> str: return f"Extract {fields} from the following text: {text}" json_response = json_extraction( text="The capital of France is Paris", fields=["capital", "country"], ) print(json.loads(json_response)) ```  ## Next Steps By leveraging Output Parsers effectively, you can create more robust and reliable LLM-powered applications, ensuring that the raw model outputs are transformed into structured data that's easy to work with in your application logic. Next, we recommend taking a look at the section on [Tools](/docs/mirascope/learn/tools) to learn how to extend the capabilities of LLMs with custom functions. 

 # Tools  If you haven't already, we recommend first reading the section on [Calls](/docs/mirascope/learn/calls)  Tools are user-defined functions that an LLM (Large Language Model) can ask the user to invoke on its behalf. This greatly enhances the capabilities of LLMs by enabling them to perform specific tasks, access external data, interact with other systems, and more. Mirascope enables defining tools in a provider-agnostic way, which can be used across all supported LLM providers without modification.  When an LLM decides to use a tool, it indicates the tool name and argument values in its response. It's important to note that the LLM doesn't actually execute the function; instead, you are responsible for calling the tool and (optionally) providing the output back to the LLM in a subsequent interaction. For more details on such iterative tool-use flows, check out the [Tool Message Parameters](#tool-message-parameters) section below as well as the section on [Agents](/docs/mirascope/learn/agents). ```mermaid sequenceDiagram participant YC as Your Code participant LLM YC->>LLM: Call with prompt and function definitions loop Tool Calls LLM->>LLM: Decide to respond or call functions LLM->>YC: Respond with function to call and arguments YC->>YC: Execute function with given arguments YC->>LLM: Call with prompt and function result end LLM->>YC: Final response ```  ## Basic Usage and Syntax  [`mirascope.llm.tool`](/docs/mirascope/api)  There are two ways of defining tools in Mirascope: `BaseTool` and functions. You can consider the functional definitions a shorthand form of writing the `BaseTool` version of the same tool. Under the hood, tools defined as functions will get converted automatically into their corresponding `BaseTool`. Let's take a look at a basic example of each:  ```python from mirascope import BaseTool, llm from pydantic import Field # [!code highlight:13] class GetBookAuthor(BaseTool): """Returns the author of the book with the given title.""" title: str = Field(..., description="The title of the book.") def call(self) -> str: if self.title == "The Name of the Wind": return "Patrick Rothfuss" elif self.title == "Mistborn: The Final Empire": return "Brandon Sanderson" else: return "Unknown" @llm.call(provider="$PROVIDER", model="$MODEL", tools=[GetBookAuthor]) # [!code highlight] def identify_author(book: str) -> str: return f"Who wrote {book}?" response = identify_author("The Name of the Wind") if tool := response.tool: # [!code highlight] print(tool.call()) # [!code highlight] # Output: Patrick Rothfuss # [!code highlight] else: print(response.content) ```  ```python from mirascope import BaseTool, llm, prompt_template from pydantic import Field # [!code highlight:13] class GetBookAuthor(BaseTool): """Returns the author of the book with the given title.""" title: str = Field(..., description="The title of the book.") def call(self) -> str: if self.title == "The Name of the Wind": return "Patrick Rothfuss" elif self.title == "Mistborn: The Final Empire": return "Brandon Sanderson" else: return "Unknown" @llm.call(provider="$PROVIDER", model="$MODEL", tools=[GetBookAuthor]) # [!code highlight] @prompt_template("Who wrote {book}?") def identify_author(book: str): ... response = identify_author("The Name of the Wind") if tool := response.tool: # [!code highlight] print(tool.call()) # [!code highlight] # Output: Patrick Rothfuss # [!code highlight] else: print(response.content) ```  ```python from mirascope import llm # [!code highlight:13] def get_book_author(title: str) -> str: """Returns the author of the book with the given title Args: title: The title of the book. """ if title == "The Name of the Wind": return "Patrick Rothfuss" elif title == "Mistborn: The Final Empire": return "Brandon Sanderson" else: return "Unknown" @llm.call(provider="$PROVIDER", model="$MODEL", tools=[get_book_author]) # [!code highlight] def identify_author(book: str) -> str: return f"Who wrote {book}?" response = identify_author("The Name of the Wind") if tool := response.tool: # [!code highlight] print(tool.call()) # [!code highlight] # Output: Patrick Rothfuss # [!code highlight] else: print(response.content) ```  ```python from mirascope import llm, prompt_template # [!code highlight:13] def get_book_author(title: str) -> str: """Returns the author of the book with the given title Args: title: The title of the book. """ if title == "The Name of the Wind": return "Patrick Rothfuss" elif title == "Mistborn: The Final Empire": return "Brandon Sanderson" else: return "Unknown" @llm.call(provider="$PROVIDER", model="$MODEL", tools=[get_book_author]) # [!code highlight] @prompt_template("Who wrote {book}?") def identify_author(book: str): ... response = identify_author("The Name of the Wind") if tool := response.tool: # [!code highlight] print(tool.call()) # [!code highlight] # Output: Patrick Rothfuss # [!code highlight] else: print(response.content) ```  In this example we: 1. Define the `GetBookAuthor`/`get_book_author` tool (a dummy method for the example) 2. Set the `tools` argument in the `call` decorator to give the LLM access to the tool. 3. We call `identify_author`, which automatically generates the corresponding provider-specific tool schema under the hood. 4. Check if the response from `identify_author` contains a tool, which is the `BaseTool` instance constructed from the underlying tool call - If yes, we call the constructed tool's `call` method and print its output. This calls the tool with the arguments provided by the LLM. - If no, we print the content of the response (assuming no tool was called). The core idea to understand here is that the LLM is asking us to call the tool on its behalf with arguments that it has provided. In the above example, the LLM chooses to call the tool to get the author rather than relying on its world knowledge. This is particularly important for buildling applications with access to live information and external systems. For the purposes of this example we are showing just a single tool call. Generally, you would then give the tool call's output back to the LLM and make another call so the LLM can generate a response based on the output of the tool. We cover this in more detail in the section on [Agents](/docs/mirascope/learn/agents) ### Accessing Original Tool Call The `BaseTool` instances have a `tool_call` property for accessing the original LLM tool call.  ```python from mirascope import BaseTool, llm from pydantic import Field class GetBookAuthor(BaseTool): """Returns the author of the book with the given title.""" title: str = Field(..., description="The title of the book.") def call(self) -> str: if self.title == "The Name of the Wind": return "Patrick Rothfuss" elif self.title == "Mistborn: The Final Empire": return "Brandon Sanderson" else: return "Unknown" @llm.call(provider="$PROVIDER", model="$MODEL", tools=[GetBookAuthor]) def identify_author(book: str) -> str: return f"Who wrote {book}?" response = identify_author("The Name of the Wind") if tool := response.tool: print(tool.call()) # Output: Patrick Rothfuss print(f"Original tool call: {tool.tool_call}") # [!code highlight] else: print(response.content) ```  ```python from mirascope import BaseTool, llm, prompt_template from pydantic import Field class GetBookAuthor(BaseTool): """Returns the author of the book with the given title.""" title: str = Field(..., description="The title of the book.") def call(self) -> str: if self.title == "The Name of the Wind": return "Patrick Rothfuss" elif self.title == "Mistborn: The Final Empire": return "Brandon Sanderson" else: return "Unknown" @llm.call(provider="$PROVIDER", model="$MODEL", tools=[GetBookAuthor]) @prompt_template("Who wrote {book}?") def identify_author(book: str): ... response = identify_author("The Name of the Wind") if tool := response.tool: print(tool.call()) # Output: Patrick Rothfuss print(f"Original tool call: {tool.tool_call}") # [!code highlight] else: print(response.content) ```  ```python from mirascope import llm def get_book_author(title: str) -> str: """Returns the author of the book with the given title Args: title: The title of the book. """ if title == "The Name of the Wind": return "Patrick Rothfuss" elif title == "Mistborn: The Final Empire": return "Brandon Sanderson" else: return "Unknown" @llm.call(provider="$PROVIDER", model="$MODEL", tools=[get_book_author]) def identify_author(book: str) -> str: return f"Who wrote {book}?" response = identify_author("The Name of the Wind") if tool := response.tool: print(tool.call()) # Output: Patrick Rothfuss print(f"Original tool call: {tool.tool_call}") # [!code highlight] else: print(response.content) ```  ```python from mirascope import llm, prompt_template def get_book_author(title: str) -> str: """Returns the author of the book with the given title Args: title: The title of the book. """ if title == "The Name of the Wind": return "Patrick Rothfuss" elif title == "Mistborn: The Final Empire": return "Brandon Sanderson" else: return "Unknown" @llm.call(provider="$PROVIDER", model="$MODEL", tools=[get_book_author]) @prompt_template("Who wrote {book}?") def identify_author(book: str): ... response = identify_author("The Name of the Wind") if tool := response.tool: print(tool.call()) # Output: Patrick Rothfuss print(f"Original tool call: {tool.tool_call}") # [!code highlight] else: print(response.content) ```  ## Supported Field Types While Mirascope provides a consistent interface, type support varies among providers: | Type | OpenAI | Anthropic | Google | Groq | xAI | Mistral | Cohere | |---------------|:--------:|:-----------:|:--------:|:------:|:-----:|:---------:|:--------:| | str | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | | int | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | | float | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | | bool | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | | bytes | ✓ | ✓ | — | ✓ | ✓ | ✓ | ✓ | | list | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | | set | ✓ | ✓ | — | ✓ | ✓ | ✓ | ✓ | | tuple | — | ✓ | — | ✓ | — | ✓ | ✓ | | dict | — | ✓ | ✓ | ✓ | — | ✓ | ✓ | | Literal/Enum | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | | BaseModel | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | — | | Nested ($def) | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | — | *Legend: ✓ (Supported), — (Not Supported)* Consider provider-specific capabilities when working with advanced type structures. Even for supported types, LLM outputs may sometimes be incorrect or of the wrong type. In such cases, prompt engineering or error handling (like [retries](/docs/mirascope/learn/retries) and [reinserting validation errors](/docs/mirascope/learn/retries#error-reinsertion)) may be necessary. ## Parallel Tool Calls In certain cases the LLM will ask to call multiple tools in the same response. Mirascope makes calling all such tools simple:  ```python from mirascope import BaseTool, llm from pydantic import Field class GetBookAuthor(BaseTool): """Returns the author of the book with the given title.""" title: str = Field(..., description="The title of the book.") def call(self) -> str: if self.title == "The Name of the Wind": return "Patrick Rothfuss" elif self.title == "Mistborn: The Final Empire": return "Brandon Sanderson" else: return "Unknown" @llm.call(provider="$PROVIDER", model="$MODEL", tools=[GetBookAuthor]) def identify_authors(books: list[str]) -> str: return f"Who wrote {books}?" # [!code highlight:5] response = identify_authors(["The Name of the Wind", "Mistborn: The Final Empire"]) if tools := response.tools: for tool in tools: print(tool.call()) else: print(response.content) ```  ```python from mirascope import BaseTool, llm, prompt_template from pydantic import Field class GetBookAuthor(BaseTool): """Returns the author of the book with the given title.""" title: str = Field(..., description="The title of the book.") def call(self) -> str: if self.title == "The Name of the Wind": return "Patrick Rothfuss" elif self.title == "Mistborn: The Final Empire": return "Brandon Sanderson" else: return "Unknown" @llm.call(provider="$PROVIDER", model="$MODEL", tools=[GetBookAuthor]) @prompt_template("Who wrote {books}?") def identify_authors(books: list[str]): ... # [!code highlight:5] response = identify_authors(["The Name of the Wind", "Mistborn: The Final Empire"]) if tools := response.tools: for tool in tools: print(tool.call()) else: print(response.content) ```  ```python from mirascope import llm def get_book_author(title: str) -> str: """Returns the author of the book with the given title Args: title: The title of the book. """ if title == "The Name of the Wind": return "Patrick Rothfuss" elif title == "Mistborn: The Final Empire": return "Brandon Sanderson" else: return "Unknown" @llm.call(provider="$PROVIDER", model="$MODEL", tools=[get_book_author]) def identify_authors(books: list[str]) -> str: return f"Who wrote {books}?" # [!code highlight:5] response = identify_authors(["The Name of the Wind", "Mistborn: The Final Empire"]) if tools := response.tools: for tool in tools: print(tool.call()) else: print(response.content) ```  ```python from mirascope import llm, prompt_template def get_book_author(title: str) -> str: """Returns the author of the book with the given title Args: title: The title of the book. """ if title == "The Name of the Wind": return "Patrick Rothfuss" elif title == "Mistborn: The Final Empire": return "Brandon Sanderson" else: return "Unknown" @llm.call(provider="$PROVIDER", model="$MODEL", tools=[get_book_author]) @prompt_template("Who wrote {books}?") def identify_authors(books: list[str]): ... # [!code highlight:5] response = identify_authors(["The Name of the Wind", "Mistborn: The Final Empire"]) if tools := response.tools: for tool in tools: print(tool.call()) else: print(response.content) ```  If your tool calls are I/O-bound, it's often worth writing [async tools](/docs/mirascope/learn/async#async-tools) so that you can run all of the tools calls [in parallel](/docs/mirascope/learn/async#parallel-async-calls) for better efficiency. ## Streaming Tools Mirascope supports streaming responses with tools, which is useful for long-running tasks or real-time updates:  ```python from mirascope import BaseTool, llm from pydantic import Field class GetBookAuthor(BaseTool): """Returns the author of the book with the given title.""" title: str = Field(..., description="The title of the book.") def call(self) -> str: if self.title == "The Name of the Wind": return "Patrick Rothfuss" elif self.title == "Mistborn: The Final Empire": return "Brandon Sanderson" else: return "Unknown" @llm.call(provider="$PROVIDER", model="$MODEL", tools=[GetBookAuthor], stream=True) # [!code highlight] def identify_authors(books: list[str]) -> str: return f"Who wrote {books}?" stream = identify_authors(["The Name of the Wind", "Mistborn: The Final Empire"]) for chunk, tool in stream: # [!code highlight] if tool: # [!code highlight] print(tool.call()) # [!code highlight] else: print(chunk.content, end="", flush=True) ```  ```python from mirascope import BaseTool, llm, prompt_template from pydantic import Field class GetBookAuthor(BaseTool): """Returns the author of the book with the given title.""" title: str = Field(..., description="The title of the book.") def call(self) -> str: if self.title == "The Name of the Wind": return "Patrick Rothfuss" elif self.title == "Mistborn: The Final Empire": return "Brandon Sanderson" else: return "Unknown" @llm.call(provider="$PROVIDER", model="$MODEL", tools=[GetBookAuthor], stream=True) # [!code highlight] @prompt_template("Who wrote {books}?") def identify_authors(books: list[str]): ... stream = identify_authors(["The Name of the Wind", "Mistborn: The Final Empire"]) for chunk, tool in stream: # [!code highlight] if tool: # [!code highlight] print(tool.call()) # [!code highlight] else: print(chunk.content, end="", flush=True) ```  ```python from mirascope import llm def get_book_author(title: str) -> str: """Returns the author of the book with the given title Args: title: The title of the book. """ if title == "The Name of the Wind": return "Patrick Rothfuss" elif title == "Mistborn: The Final Empire": return "Brandon Sanderson" else: return "Unknown" @llm.call(provider="$PROVIDER", model="$MODEL", tools=[get_book_author], stream=True) # [!code highlight] def identify_authors(books: list[str]) -> str: return f"Who wrote {books}?" stream = identify_authors(["The Name of the Wind", "Mistborn: The Final Empire"]) for chunk, tool in stream: # [!code highlight] if tool: # [!code highlight] print(tool.call()) # [!code highlight] else: print(chunk.content, end="", flush=True) ```  ```python from mirascope import llm, prompt_template def get_book_author(title: str) -> str: """Returns the author of the book with the given title Args: title: The title of the book. """ if title == "The Name of the Wind": return "Patrick Rothfuss" elif title == "Mistborn: The Final Empire": return "Brandon Sanderson" else: return "Unknown" @llm.call(provider="$PROVIDER", model="$MODEL", tools=[get_book_author], stream=True) # [!code highlight] @prompt_template("Who wrote {books}?") def identify_authors(books: list[str]): ... stream = identify_authors(["The Name of the Wind", "Mistborn: The Final Empire"]) for chunk, tool in stream: # [!code highlight] if tool: # [!code highlight] print(tool.call()) # [!code highlight] else: print(chunk.content, end="", flush=True) ```  When we identify that a tool is being streamed, we will internally reconstruct the tool from the streamed response. This means that the tool won't be returned until the full tool has been streamed and reconstructed on your behalf.  Currently only OpenAI, Anthropic, Mistral, and Groq support streaming tools. All other providers will always return `None` for tools. If you think we're missing any, let us know!  ### Streaming Partial Tools You can also stream intermediate partial tools and their deltas (rather than just the fully constructed tool) by setting `stream={"partial_tools": True}`:  ```python from mirascope import BaseTool, llm from pydantic import Field class GetBookAuthor(BaseTool): """Returns the author of the book with the given title.""" title: str = Field(..., description="The title of the book.") def call(self) -> str: if self.title == "The Name of the Wind": return "Patrick Rothfuss" elif self.title == "Mistborn: The Final Empire": return "Brandon Sanderson" else: return "Unknown" @llm.call( provider="$PROVIDER", model="$MODEL", tools=[GetBookAuthor], stream={"partial_tools": True}, # [!code highlight] ) def identify_authors(books: list[str]) -> str: return f"Who wrote {books}?" stream = identify_authors(["The Name of the Wind", "Mistborn: The Final Empire"]) for chunk, tool in stream: if tool: # [!code highlight] if tool.delta is not None: # partial tool print(tool.delta) else: print(tool.call()) else: print(chunk.content, end="", flush=True) ```  ```python from mirascope import BaseTool, llm, prompt_template from pydantic import Field class GetBookAuthor(BaseTool): """Returns the author of the book with the given title.""" title: str = Field(..., description="The title of the book.") def call(self) -> str: if self.title == "The Name of the Wind": return "Patrick Rothfuss" elif self.title == "Mistborn: The Final Empire": return "Brandon Sanderson" else: return "Unknown" @llm.call( provider="$PROVIDER", model="$MODEL", tools=[GetBookAuthor], stream={"partial_tools": True}, # [!code highlight] ) @prompt_template("Who wrote {books}?") def identify_authors(books: list[str]): ... stream = identify_authors(["The Name of the Wind", "Mistborn: The Final Empire"]) for chunk, tool in stream: if tool: # [!code highlight] if tool.delta is not None: # partial tool print(tool.delta) else: print(tool.call()) else: print(chunk.content, end="", flush=True) ```  ```python from mirascope import llm def get_book_author(title: str) -> str: """Returns the author of the book with the given title Args: title: The title of the book. """ if title == "The Name of the Wind": return "Patrick Rothfuss" elif title == "Mistborn: The Final Empire": return "Brandon Sanderson" else: return "Unknown" @llm.call( provider="$PROVIDER", model="$MODEL", tools=[get_book_author], stream={"partial_tools": True}, # [!code highlight] ) def identify_authors(books: list[str]) -> str: return f"Who wrote {books}?" stream = identify_authors(["The Name of the Wind", "Mistborn: The Final Empire"]) for chunk, tool in stream: if tool: # [!code highlight] if tool.delta is not None: # partial tool print(tool.delta) else: print(tool.call()) else: print(chunk.content, end="", flush=True) ```  ```python from mirascope import llm, prompt_template def get_book_author(title: str) -> str: """Returns the author of the book with the given title Args: title: The title of the book. """ if title == "The Name of the Wind": return "Patrick Rothfuss" elif title == "Mistborn: The Final Empire": return "Brandon Sanderson" else: return "Unknown" @llm.call( provider="$PROVIDER", model="$MODEL", tools=[get_book_author], stream={"partial_tools": True}, # [!code highlight] ) @prompt_template("Who wrote {books}?") def identify_authors(books: list[str]): ... stream = identify_authors(["The Name of the Wind", "Mistborn: The Final Empire"]) for chunk, tool in stream: if tool: # [!code highlight] if tool.delta is not None: # partial tool print(tool.delta) else: print(tool.call()) else: print(chunk.content, end="", flush=True) ```  ## Tool Message Parameters  Calling tools and inserting their outputs into subsequent LLM API calls in a loop in the most basic form of an agent. While we cover this briefly here, we recommend reading the section on [Agents](/docs/mirascope/learn/agents) for more details and examples.  Generally the next step after the LLM returns a tool call is for you to call the tool on its behalf and supply the output in a subsequent call. Let's take a look at a basic example of this:  ```python from mirascope import BaseMessageParam, BaseTool, Messages, llm class GetBookAuthor(BaseTool): title: str def call(self) -> str: if self.title == "The Name of the Wind": return "Patrick Rothfuss" elif self.title == "Mistborn: The Final Empire": return "Brandon Sanderson" else: return "Unknown" @llm.call(provider="$PROVIDER", model="$MODEL", tools=[GetBookAuthor]) def identify_author(book: str, history: list[BaseMessageParam]) -> Messages.Type: messages = [*history] # [!code highlight] if book: messages.append(Messages.User(f"Who wrote {book}?")) # [!code highlight] return messages history = [] response = identify_author("The Name of the Wind", history) history += [response.user_message_param, response.message_param] while tool := response.tool: tools_and_outputs = [(tool, tool.call())] history += response.tool_message_params(tools_and_outputs) response = identify_author("", history) # [!code highlight] history.append(response.message_param) # [!code highlight] print(response.content) # [!code highlight] # Output: The Name of the Wind was written by Patrick Rothfuss. ```  ```python from mirascope import BaseDynamicConfig, BaseMessageParam, BaseTool, llm, prompt_template class GetBookAuthor(BaseTool): title: str def call(self) -> str: if self.title == "The Name of the Wind": return "Patrick Rothfuss" elif self.title == "Mistborn: The Final Empire": return "Brandon Sanderson" else: return "Unknown" @llm.call(provider="$PROVIDER", model="$MODEL", tools=[GetBookAuthor]) @prompt_template( """ MESSAGES: {history} # [!code highlight] USER: {query} """ ) def identify_author(book: str, history: list[BaseMessageParam]) -> BaseDynamicConfig: return {"computed_fields": {"query": f"Who wrote {book}" if book else ""}} # [!code highlight] history = [] response = identify_author("The Name of the Wind", history) history += [response.user_message_param, response.message_param] while tool := response.tool: tools_and_outputs = [(tool, tool.call())] history += response.tool_message_params(tools_and_outputs) response = identify_author("", history) # [!code highlight] history.append(response.message_param) # [!code highlight] print(response.content) # [!code highlight] # Output: The Name of the Wind was written by Patrick Rothfuss. ```  ```python from mirascope import BaseMessageParam, Messages, llm def get_book_author(title: str) -> str: if title == "The Name of the Wind": return "Patrick Rothfuss" elif title == "Mistborn: The Final Empire": return "Brandon Sanderson" else: return "Unknown" @llm.call(provider="$PROVIDER", model="$MODEL", tools=[get_book_author]) def identify_author(book: str, history: list[BaseMessageParam]) -> Messages.Type: messages = [*history] # [!code highlight] if book: messages.append(Messages.User(f"Who wrote {book}?")) # [!code highlight] return messages history = [] response = identify_author("The Name of the Wind", history) history += [response.user_message_param, response.message_param] while tool := response.tool: tools_and_outputs = [(tool, tool.call())] history += response.tool_message_params(tools_and_outputs) response = identify_author("", history) # [!code highlight] history.append(response.message_param) # [!code highlight] print(response.content) # [!code highlight] # Output: The Name of the Wind was written by Patrick Rothfuss. ```  ```python from mirascope import BaseMessageParam, BaseDynamicConfig, llm, prompt_template def get_book_author(title: str) -> str: if title == "The Name of the Wind": return "Patrick Rothfuss" elif title == "Mistborn: The Final Empire": return "Brandon Sanderson" else: return "Unknown" @llm.call(provider="$PROVIDER", model="$MODEL", tools=[get_book_author]) @prompt_template( """ MESSAGES: {history} # [!code highlight] USER: {query} """ ) def identify_author(book: str, history: list[BaseMessageParam]) -> BaseDynamicConfig: return {"computed_fields": {"query": f"Who wrote {book}" if book else ""}} # [!code highlight] history = [] response = identify_author("The Name of the Wind", history) history += [response.user_message_param, response.message_param] while tool := response.tool: tools_and_outputs = [(tool, tool.call())] history += response.tool_message_params(tools_and_outputs) response = identify_author("", history) # [!code highlight] history.append(response.message_param) # [!code highlight] print(response.content) # [!code highlight] # Output: The Name of the Wind was written by Patrick Rothfuss. ```  In this example we: 1. Add `history` to maintain the messages across multiple calls to the LLM. 2. Loop until the response no longer has tools calls. 3. While there are tool calls, call the tools, append their corresponding message parameters to the history, and make a subsequent call with an empty query and updated history. We use an empty query because the original user message is already included in the history. 4. Print the final response content once the LLM is done calling tools. ## Validation and Error Handling Since `BaseTool` is a subclass of Pydantic's [`BaseModel`](https://docs.pydantic.dev/latest/usage/models/), they are validated on construction, so it's important that you handle potential `ValidationError`'s for building more robust applications:  ```python from typing import Annotated from mirascope import BaseTool, llm from pydantic import AfterValidator, Field, ValidationError def is_upper(v: str) -> str: assert v.isupper(), "Must be uppercase" return v class GetBookAuthor(BaseTool): """Returns the author of the book with the given title.""" title: Annotated[str, AfterValidator(is_upper)] = Field( # [!code highlight] ..., description="The title of the book." ) def call(self) -> str: if self.title == "THE NAME OF THE WIND": return "Patrick Rothfuss" elif self.title == "MISTBORN: THE FINAL EMPIRE": return "Brandon Sanderson" else: return "Unknown" @llm.call(provider="$PROVIDER", model="$MODEL", tools=[GetBookAuthor]) def identify_author(book: str) -> str: return f"Who wrote {book}?" response = identify_author("The Name of the Wind") try: # [!code highlight] if tool := response.tool: print(tool.call()) else: print(response.content) except ValidationError as e: # [!code highlight] print(e) # > 1 validation error for GetBookAuthor # title # Assertion failed, Must be uppercase [type=assertion_error, input_value='The Name of the Wind', input_type=str] # For further information visit https://errors.pydantic.dev/2.8/v/assertion_error ```  ```python from typing import Annotated from mirascope import BaseTool, llm, prompt_template from pydantic import AfterValidator, Field, ValidationError def is_upper(v: str) -> str: assert v.isupper(), "Must be uppercase" return v class GetBookAuthor(BaseTool): """Returns the author of the book with the given title.""" title: Annotated[str, AfterValidator(is_upper)] = Field( # [!code highlight] ..., description="The title of the book." ) def call(self) -> str: if self.title == "THE NAME OF THE WIND": return "Patrick Rothfuss" elif self.title == "MISTBORN: THE FINAL EMPIRE": return "Brandon Sanderson" else: return "Unknown" @llm.call(provider="$PROVIDER", model="$MODEL", tools=[GetBookAuthor]) @prompt_template("Who wrote {book}?") def identify_author(book: str): ... response = identify_author("The Name of the Wind") try: # [!code highlight] if tool := response.tool: print(tool.call()) else: print(response.content) except ValidationError as e: # [!code highlight] print(e) # > 1 validation error for GetBookAuthor # title # Assertion failed, Must be uppercase [type=assertion_error, input_value='The Name of the Wind', input_type=str] # For further information visit https://errors.pydantic.dev/2.8/v/assertion_error ```  ```python from typing import Annotated from mirascope import llm from pydantic import AfterValidator, ValidationError def is_upper(v: str) -> str: assert v.isupper(), "Must be uppercase" return v def get_book_author(title: Annotated[str, AfterValidator(is_upper)]) -> str: # [!code highlight] """Returns the author of the book with the given title Args: title: The title of the book. """ if title == "THE NAME OF THE WIND": return "Patrick Rothfuss" elif title == "MISTBORN: THE FINAL EMPIRE": return "Brandon Sanderson" else: return "Unknown" @llm.call(provider="$PROVIDER", model="$MODEL", tools=[get_book_author]) def identify_author(book: str) -> str: return f"Who wrote {book}?" response = identify_author("The Name of the Wind") try: # [!code highlight] if tool := response.tool: print(tool.call()) else: print(response.content) except ValidationError as e: # [!code highlight] print(e) # > 1 validation error for GetBookAuthor # title # Assertion failed, Must be uppercase [type=assertion_error, input_value='The Name of the Wind', input_type=str] # For further information visit https://errors.pydantic.dev/2.8/v/assertion_error ```  ```python from typing import Annotated from mirascope import llm, prompt_template from pydantic import AfterValidator, ValidationError def is_upper(v: str) -> str: assert v.isupper(), "Must be uppercase" return v def get_book_author(title: Annotated[str, AfterValidator(is_upper)]) -> str: # [!code highlight] """Returns the author of the book with the given title Args: title: The title of the book. """ if title == "THE NAME OF THE WIND": return "Patrick Rothfuss" elif title == "MISTBORN: THE FINAL EMPIRE": return "Brandon Sanderson" else: return "Unknown" @llm.call(provider="$PROVIDER", model="$MODEL", tools=[get_book_author]) @prompt_template("Who wrote {book}?") def identify_author(book: str): ... response = identify_author("The Name of the Wind") try: # [!code highlight] if tool := response.tool: print(tool.call()) else: print(response.content) except ValidationError as e: # [!code highlight] print(e) # > 1 validation error for GetBookAuthor # title # Assertion failed, Must be uppercase [type=assertion_error, input_value='The Name of the Wind', input_type=str] # For further information visit https://errors.pydantic.dev/2.8/v/assertion_error ```  In this example we've added additional validation, but it's important that you still handle `ValidationError`'s even with standard tools since they are still `BaseModel` instances and will validate the field types regardless. ## Few-Shot Examples Just like with [Response Models](/docs/mirascope/learn/response_models#few-shot-examples), you can add few-shot examples to your tools:  ```python from mirascope import BaseTool, llm from pydantic import ConfigDict, Field class GetBookAuthor(BaseTool): """Returns the author of the book with the given title.""" title: str = Field( ..., description="The title of the book.", examples=["The Name of the Wind"], # [!code highlight] ) model_config = ConfigDict( json_schema_extra={"examples": [{"title": "The Name of the Wind"}]} # [!code highlight] ) def call(self) -> str: if self.title == "The Name of the Wind": return "Patrick Rothfuss" elif self.title == "Mistborn: The Final Empire": return "Brandon Sanderson" else: return "Unknown" @llm.call(provider="$PROVIDER", model="$MODEL", tools=[GetBookAuthor]) def identify_author(book: str) -> str: return f"Who wrote {book}?" response = identify_author("The Name of the Wind") if tool := response.tool: print(tool.call()) else: print(response.content) ```  ```python from mirascope import BaseTool, llm, prompt_template from pydantic import ConfigDict, Field class GetBookAuthor(BaseTool): """Returns the author of the book with the given title.""" title: str = Field( ..., description="The title of the book.", examples=["The Name of the Wind"], # [!code highlight] ) model_config = ConfigDict( json_schema_extra={"examples": [{"title": "The Name of the Wind"}]} # [!code highlight] ) def call(self) -> str: if self.title == "The Name of the Wind": return "Patrick Rothfuss" elif self.title == "Mistborn: The Final Empire": return "Brandon Sanderson" else: return "Unknown" @llm.call(provider="$PROVIDER", model="$MODEL", tools=[GetBookAuthor]) @prompt_template("Who wrote {book}?") def identify_author(book: str): ... response = identify_author("The Name of the Wind") if tool := response.tool: print(tool.call()) else: print(response.content) ```  ```python from typing import Annotated from pydantic import Field from mirascope import llm def get_book_author( title: Annotated[ str, Field( ..., description="The title of the book.", examples=["The Name of the Wind"], # [!code highlight] ), ], ) -> str: """Returns the author of the book with the given title Example: # [!code highlight] {"title": "The Name of the Wind"} # [!code highlight] Args: title: The title of the book. """ if title == "The Name of the Wind": return "Patrick Rothfuss" elif title == "Mistborn: The Final Empire": return "Brandon Sanderson" else: return "Unknown" @llm.call(provider="$PROVIDER", model="$MODEL", tools=[get_book_author]) def identify_author(book: str) -> str: return f"Who wrote {book}?" response = identify_author("The Name of the Wind") if tool := response.tool: print(tool.call()) else: print(response.content) ```  ```python from typing import Annotated from pydantic import Field from mirascope import llm, prompt_template def get_book_author( title: Annotated[ str, Field( ..., description="The title of the book.", examples=["The Name of the Wind"], # [!code highlight] ), ], ) -> str: """Returns the author of the book with the given title Example: # [!code highlight] {"title": "The Name of the Wind"} # [!code highlight] Args: title: The title of the book. """ if title == "The Name of the Wind": return "Patrick Rothfuss" elif title == "Mistborn: The Final Empire": return "Brandon Sanderson" else: return "Unknown" @llm.call(provider="$PROVIDER", model="$MODEL", tools=[get_book_author]) @prompt_template("Who wrote {book}?") def identify_author(book: str): ... response = identify_author("The Name of the Wind") if tool := response.tool: print(tool.call()) else: print(response.content) ```  Both approaches will result in the same tool schema with examples included. The function approach gets automatically converted to use Pydantic fields internally, making both methods equivalent in terms of functionality.  Both `BaseTool` and function-style definitions support field level examples through Pydantic's `Field`. When using function-style definitions, you'll need to wrap the type with `Annotated` to use `Field`.  ## ToolKit  [`mirascope.llm.toolkit`](/docs/mirascope/api)  The `BaseToolKit` class enables: - Organiziation of a group of tools under a single namespace. - This can be useful for making it clear to the LLM when to use certain tools over others. For example, you could namespace a set of tools under "file_system" to indicate that those tools are specifically for interacting with the file system. - Dynamic tool definitions. - This can be useful for generating tool definitions that are dependent on some input or state. For example, you may want to update the description of tools based on an argument of the call being made.  ```python from mirascope import ( BaseDynamicConfig, BaseToolKit, Messages, llm, ) from mirascope.core import toolkit_tool class BookTools(BaseToolKit): # [!code highlight] __namespace__ = "book_tools" # [!code highlight] reading_level: str # [!code highlight] @toolkit_tool # [!code highlight] def suggest_author(self, author: str) -> str: """Suggests an author for the user to read based on their reading level. User reading level: {self.reading_level} # [!code highlight] Author you suggest must be appropriate for the user's reading level. """ return f"I would suggest you read some books by {author}" @llm.call(provider="$PROVIDER", model="$MODEL") def recommend_author(genre: str, reading_level: str) -> BaseDynamicConfig: toolkit = BookTools(reading_level=reading_level) # [!code highlight] return { "tools": toolkit.create_tools(), # [!code highlight] "messages": [Messages.User(f"What {genre} author should I read?")], } response = recommend_author("fantasy", "beginner") # [!code highlight] if tool := response.tool: print(tool.call()) # Output: I would suggest you read some books by J.K. Rowling # [!code highlight] response = recommend_author("fantasy", "advanced") # [!code highlight] if tool := response.tool: print(tool.call()) # Output: I would suggest you read some books by Brandon Sanderson # [!code highlight] ```  ```python from mirascope import ( BaseDynamicConfig, BaseToolKit, llm, prompt_template, ) from mirascope.core import toolkit_tool class BookTools(BaseToolKit): # [!code highlight] __namespace__ = "book_tools" # [!code highlight] reading_level: str # [!code highlight] @toolkit_tool # [!code highlight] def suggest_author(self, author: str) -> str: """Suggests an author for the user to read based on their reading level. User reading level: {self.reading_level} # [!code highlight] Author you suggest must be appropriate for the user's reading level. """ return f"I would suggest you read some books by {author}" @llm.call(provider="$PROVIDER", model="$MODEL") @prompt_template("What {genre} author should I read?") def recommend_author(genre: str, reading_level: str) -> BaseDynamicConfig: toolkit = BookTools(reading_level=reading_level) # [!code highlight] return {"tools": toolkit.create_tools()} # [!code highlight] response = recommend_author("fantasy", "beginner") # [!code highlight] if tool := response.tool: print(tool.call()) # Output: I would suggest you read some books by J.K. Rowling # [!code highlight] response = recommend_author("fantasy", "advanced") # [!code highlight] if tool := response.tool: print(tool.call()) # Output: I would suggest you read some books by Brandon Sanderson # [!code highlight] ```  In this example we: 1. Create a `BookTools` toolkit 2. We set `__namespace__` equal to "book_tools" 3. We define the `reading_level` state of the toolkit 4. We define the `suggest_author` tool and mark it with `@toolkit_tool` to identify the method as a tool of the toolkit 5. We use the `{self.reading_level}` template variable in the description of the tool. 6. We create the toolkit with the `reading_level` argument. 7. We call `create_tools` to generate the toolkit's tools. This will generate the tools on every call, ensuring that the description correctly includes the provided reading level. 8. We call `recommend_author` with a "beginner" reading level, and the LLM calls the `suggest_author` tool with its suggested author. 9. We call `recommend_author` again but with "advanced" reading level, and again the LLM calls the `suggest_author` tool with its suggested author. The core concept to understand here is that the `suggest_author` tool's description is dynamically generated on each call to `recommend_author` through the toolkit. This is why the "beginner" recommendation and "advanced" recommendations call the `suggest_author` tool with authors befitting the reading level of each call. ## Pre-Made Tools and ToolKits Mirascope provides several pre-made tools and toolkits to help you get started quickly:  Pre-made tools and toolkits require installing the dependencies listed in the "Dependencies" column for each tool/toolkit. For example: ```bash pip install httpx # For HTTPX tool pip install requests # For Requests tool ```  ### Pre-Made Tools  - [`mirascope.tools.web.DuckDuckGoSearch`](/docs/mirascope/api/tools/web/duckduckgo) - [`mirascope.tools.web.HTTPX`](/docs/mirascope/api/tools/web/httpx) - [`mirascope.tools.web.ParseURLContent`](/docs/mirascope/api/tools/web/parse_url_content) - [`mirascope.tools.web.Requests`](/docs/mirascope/api/tools/web/requests)  | Tool | Primary Use | Dependencies | Key Features | Characteristics | |------ |------------- |-------------- |-------------- |----------------- | | [`DuckDuckGoSearch`](/docs/mirascope/api/tools/web/duckduckgo) | Web Searching | [`duckduckgo-search`](https://pypi.org/project/duckduckgo-search/) | • Multiple query support

• Title/URL/snippet extraction

• Result count control

• Automated formatting | • Privacy-focused search

• Async support (AsyncDuckDuckGoSearch)

• Automatic filtering

• Structured results | | [`HTTPX`](/docs/mirascope/api/tools/web/httpx) | Advanced HTTP Requests | [`httpx`](https://pypi.org/project/httpx/) | • Full HTTP method support (GET/POST/PUT/DELETE)

• Custom header support

• File upload/download

• Form data handling | • Async support (AsyncHTTPX)

• Configurable timeouts

• Comprehensive error handling

• Redirect control | | [`ParseURLContent`](/docs/mirascope/api/tools/web/parse_url_content) | Web Content Extraction | [`beautifulsoup4`](https://pypi.org/project/beautifulsoup4/), [`httpx`](https://pypi.org/project/httpx/) | • HTML content fetching

• Main content extraction

• Element filtering

• Text normalization | • Automatic cleaning

• Configurable parser

• Timeout settings

• Error handling | | [`Requests`](/docs/mirascope/api/tools/web/requests) | Simple HTTP Requests | [`requests`](https://pypi.org/project/requests/) | • Basic HTTP methods

• Simple API

• Response text retrieval

• Basic authentication | • Minimal configuration

• Intuitive interface

• Basic error handling

• Lightweight implementation | Example using DuckDuckGoSearch:  ```python from mirascope import llm from mirascope.tools import DuckDuckGoSearch # [!code highlight] @llm.call(provider="$PROVIDER", model="$MODEL", tools=[DuckDuckGoSearch]) # [!code highlight] def research(genre: str) -> str: return f"Recommend a {genre} book and summarize the story" response = research("fantasy") if tool := response.tool: print(tool.call()) ```  ```python from mirascope import llm, prompt_template from mirascope.tools import DuckDuckGoSearch # [!code highlight] @llm.call(provider="$PROVIDER", model="$MODEL", tools=[DuckDuckGoSearch]) # [!code highlight] @prompt_template("Recommend a {genre} book and summarize the story") def research(genre: str): ... response = research("fantasy") if tool := response.tool: print(tool.call()) ```  ```python from mirascope import llm from mirascope.tools import DuckDuckGoSearch, DuckDuckGoSearchConfig # [!code highlight] config = DuckDuckGoSearchConfig(max_results_per_query=5) # [!code highlight] CustomSearch = DuckDuckGoSearch.from_config(config) # [!code highlight] @llm.call(provider="$PROVIDER", model="$MODEL", tools=[CustomSearch]) # [!code highlight] def research(genre: str) -> str: return f"Recommend a {genre} book and summarize the story" response = research("fantasy") if tool := response.tool: print(tool.call()) ```  ```python from mirascope import llm, prompt_template from mirascope.tools import DuckDuckGoSearch, DuckDuckGoSearchConfig # [!code highlight] config = DuckDuckGoSearchConfig(max_results_per_query=5) # [!code highlight] CustomSearch = DuckDuckGoSearch.from_config(config) # [!code highlight] @llm.call(provider="$PROVIDER", model="$MODEL", tools=[CustomSearch]) # [!code highlight] @prompt_template("Recommend a {genre} book and summarize the story") def research(genre: str): ... response = research("fantasy") if tool := response.tool: print(tool.call()) ```  ### Pre-Made ToolKits  - [`mirascope.tools.system.FileSystemToolKit`](/docs/mirascope/api/tools/system/file_system) - [`mirascope.tools.system.DockerOperationToolKit`](/docs/mirascope/api/tools/system/docker_operation)  | ToolKit | Primary Use | Dependencies | Tools and Features | Characteristics | |--------- |--------------------------|------------------------------------------------------------------- |------------------- |----------------- | | [`FileSystemToolKit`](/docs/mirascope/api/tools/system/file_system) | File System Operations | None | • ReadFile: File content reading

• WriteFile: Content writing

• ListDirectory: Directory listing

• CreateDirectory: Directory creation

• DeleteFile: File deletion | • Path traversal protection

• File size limits

• Extension validation

• Robust error handling

• Base directory isolation | | [`DockerOperationToolKit`](/docs/mirascope/api/tools/system/docker_operation) | Code & Command Execution | [`docker`](https://pypi.org/project/docker/), [`docker engine`](https://docs.docker.com/engine/install/) | • ExecutePython: Python code execution with optional package installation

• ExecuteShell: Shell command execution | • Docker container isolation

• Memory limits

• Network control

• Security restrictions

• Resource cleanup | Example using FileSystemToolKit:  ```python from pathlib import Path from mirascope import BaseDynamicConfig, Messages, llm from mirascope.tools import FileSystemToolKit # [!code highlight] @llm.call(provider="$PROVIDER", model="$MODEL") def write_blog_post(topic: str, output_file: Path) -> BaseDynamicConfig: toolkit = FileSystemToolKit(base_directory=output_file.parent) # [!code highlight] return { "messages": [ Messages.User( content=f"Write a blog post about '{topic}' as a '{output_file.name}'." ) ], "tools": toolkit.create_tools(), # [!code highlight] } response = write_blog_post("machine learning", Path("introduction.html")) if tool := response.tool: result = tool.call() print(result) ```  ```python from pathlib import Path from mirascope import BaseDynamicConfig, Messages, llm, prompt_template from mirascope.tools import FileSystemToolKit # [!code highlight] @llm.call(provider="$PROVIDER", model="$MODEL") @prompt_template("Write a blog post about '{topic}' as a '{output_file.name}'.") def write_blog_post(topic: str, output_file: Path) -> BaseDynamicConfig: toolkit = FileSystemToolKit(base_directory=output_file.parent) # [!code highlight] return { "messages": [ Messages.User( content="Write a blog post about '{topic}' as a '{output_file.name}'." ) ], "tools": toolkit.create_tools(), # [!code highlight] } response = write_blog_post("machine learning", Path("introduction.html")) if tool := response.tool: result = tool.call() print(result) ```  ## Next Steps Tools can significantly extend LLM capabilities, enabling more interactive and dynamic applications. We encourage you to explore and experiment with tools to enhance your projects and the find the best fit for your specific needs. Mirascope hopes to provide a simple and clean interface that is both easy to learn and easy to use; however, we understand that LLM tools can be a difficult concept regardless of the supporting tooling. Next, we recommend learning about how to build [Agents](/docs/mirascope/learn/agents) that take advantage of these tools. 

 # Agents > __Definition__: a person who acts on behalf of another person or group When working with Large Language Models (LLMs), an "agent" refers to an autonomous or semi-autonomous system that can act on your behalf. The core concept is the use of tools to enable the LLM to interact with its environment. In this section we will implement a toy `Librarian` agent to demonstrate key concepts in Mirascope that will help you build agents.  If you haven't already, we recommend first reading the section on [Tools](/docs/mirascope/learn/tools)  ```mermaid sequenceDiagram participant YC as Your Code participant LLM loop Agent Loop YC->>LLM: Call with prompt + history + function definitions loop Tool Calling Cycle LLM->>LLM: Decide to respond or call functions LLM->>YC: Respond with function to call and arguments YC->>YC: Execute function with given arguments YC->>YC: Add tool call message parameters to history YC->>LLM: Call with prompt + history including function result end LLM->>YC: Finish calling tools and return final response YC->>YC: Update history with final response end ```  ## State Management Since an agent needs to operate across multiple LLM API calls, the first concept to cover is state. The goal of providing state to the agent is to give it memory. For example, we can think of local variables as "working memory" and a database as "long-term memory". Let's take a look at a basic chatbot (not an agent) that uses a class to maintain the chat's history:  ```python from mirascope import Messages, llm, BaseMessageParam from pydantic import BaseModel class Librarian(BaseModel): history: list[BaseMessageParam] = [] # [!code highlight] @llm.call(provider="$PROVIDER", model="$MODEL") def _call(self, query: str) -> Messages.Type: return [ Messages.System("You are a librarian"), *self.history, # [!code highlight] Messages.User(query), ] def run(self) -> None: while True: query = input("(User): ") if query in ["exit", "quit"]: break print("(Assistant): ", end="", flush=True) response = self._call(query) print(response.content) self.history += [ # [!code highlight] Messages.User(query), # [!code highlight] response.message_param, # [!code highlight] ] # [!code highlight] Librarian().run() ```  ```python from mirascope import Messages, llm, BaseMessageParam, prompt_template from pydantic import BaseModel class Librarian(BaseModel): history: list[BaseMessageParam] = [] # [!code highlight] @llm.call(provider="$PROVIDER", model="$MODEL") @prompt_template( """ SYSTEM: You are a librarian MESSAGES: {self.history} # [!code highlight] USER: {query} """ ) def _call(self, query: str): ... def run(self) -> None: while True: query = input("(User): ") if query in ["exit", "quit"]: break print("(Assistant): ", end="", flush=True) response = self._call(query) print(response.content) self.history += [ # [!code highlight] Messages.User(query), # [!code highlight] response.message_param, # [!code highlight] ] # [!code highlight] Librarian().run() ```  In this example we: - Create a `Librarian` class with a `history` attribute. - Implement a private `_call` method that injects `history`. - Run the `_call` method in a loop, saving the history at each step. A chatbot with memory, while more advanced, is still not an agent.  ```python from mirascope import BaseMessageParam, Messages, llm from pydantic import BaseModel class Librarian(BaseModel): history: list[BaseMessageParam] = [] @llm.call(provider="$PROVIDER", model="$MODEL") def _call(self, query: str) -> Messages.Type: return [ Messages.System("You are a librarian"), *self.history, Messages.User(query), ] def run( self, provider: llm.Provider, # [!code highlight] model: str, # [!code highlight] ) -> None: while True: query = input("(User): ") if query in ["exit", "quit"]: break print("(Assistant): ", end="", flush=True) response = llm.override(self._call, provider=provider, model=model)(query) # [!code highlight] print(response.content) self.history += [ response.user_message_param, response.message_param, ] Librarian().run("anthropic", "claude-3-5-sonnet-latest") ```  ```python from mirascope import BaseMessageParam, llm, prompt_template from pydantic import BaseModel class Librarian(BaseModel): history: list[BaseMessageParam] = [] @llm.call(provider="$PROVIDER", model="$MODEL") # [!code highlight] @prompt_template( """ SYSTEM: You are a librarian MESSAGES: {self.history} USER: {query} """ ) def _call(self, query: str): ... def run( self, provider: llm.Provider, model: str, ) -> None: while True: query = input("(User): ") if query in ["exit", "quit"]: break print("(Assistant): ", end="", flush=True) response = llm.override(self._call, provider=provider, model=model)(query) # [!code highlight] print(response.content) self.history += [ response.user_message_param, response.message_param, ] Librarian().run("anthropic", "claude-3-5-sonnet-latest") ```  ## Integrating Tools The next concept to cover is introducing tools to our chatbot, turning it into an agent capable of acting on our behalf. The most basic agent flow is to call tools on behalf of the agent, providing them back through the chat history until the agent is ready to response to the initial query. Let's take a look at a basic example where the `Librarian` can access the books available in the library:  ```python import json from mirascope import BaseDynamicConfig, Messages, llm, BaseMessageParam from pydantic import BaseModel class Book(BaseModel): title: str author: str class Librarian(BaseModel): history: list[BaseMessageParam] = [] # [!code highlight] library: list[Book] = [ # [!code highlight] Book(title="The Name of the Wind", author="Patrick Rothfuss"), # [!code highlight] Book(title="Mistborn: The Final Empire", author="Brandon Sanderson"), # [!code highlight] ] # [!code highlight] def _available_books(self) -> str: # [!code highlight] """Returns the list of books available in the library.""" # [!code highlight] return json.dumps([book.model_dump() for book in self.library]) # [!code highlight] @llm.call(provider="$PROVIDER", model="$MODEL") def _call(self, query: str) -> BaseDynamicConfig: messages = [ Messages.System("You are a librarian"), *self.history, Messages.User(query), ] return {"messages": messages, "tools": [self._available_books]} # [!code highlight] def _step(self, query: str) -> str: if query: self.history.append(Messages.User(query)) response = self._call(query) self.history.append(response.message_param) tools_and_outputs = [] # [!code highlight] if tools := response.tools: # [!code highlight] for tool in tools: # [!code highlight] print(f"[Calling Tool '{tool._name()}' with args {tool.args}]") # [!code highlight] tools_and_outputs.append((tool, tool.call())) # [!code highlight] self.history += response.tool_message_params(tools_and_outputs) # [!code highlight] return self._step("") # [!code highlight] else: return response.content def run(self) -> None: while True: query = input("(User): ") if query in ["exit", "quit"]: break print("(Assistant): ", end="", flush=True) step_output = self._step(query) print(step_output) Librarian().run() ```  ```python import json from mirascope import ( BaseDynamicConfig, Messages, llm, BaseMessageParam, prompt_template, ) from pydantic import BaseModel class Book(BaseModel): title: str author: str class Librarian(BaseModel): history: list[BaseMessageParam] = [] library: list[Book] = [ # [!code highlight] Book(title="The Name of the Wind", author="Patrick Rothfuss"), # [!code highlight] Book(title="Mistborn: The Final Empire", author="Brandon Sanderson"), # [!code highlight] ] # [!code highlight] def _available_books(self) -> str: # [!code highlight] """Returns the list of books available in the library.""" # [!code highlight] return json.dumps([book.model_dump() for book in self.library]) # [!code highlight] @llm.call(provider="$PROVIDER", model="$MODEL") @prompt_template( """ SYSTEM: You are a librarian MESSAGES: {self.history} USER: {query} """ ) def _call(self, query: str) -> BaseDynamicConfig: return {"tools": [self._available_books]} # [!code highlight] def _step(self, query: str) -> str: if query: self.history.append(Messages.User(query)) response = self._call(query) self.history.append(response.message_param) tools_and_outputs = [] # [!code highlight] if tools := response.tools: # [!code highlight] for tool in tools: # [!code highlight] print(f"[Calling Tool '{tool._name()}' with args {tool.args}]") # [!code highlight] tools_and_outputs.append((tool, tool.call())) # [!code highlight] self.history += response.tool_message_params(tools_and_outputs) # [!code highlight] return self._step("") # [!code highlight] else: return response.content def run(self) -> None: while True: query = input("(User): ") if query in ["exit", "quit"]: break print("(Assistant): ", end="", flush=True) step_output = self._step(query) print(step_output) Librarian().run() ```  In this example we: 1. Added the `library` state to maintain the list of available books. 2. Implemented the `_available_books` tool that returns the library as a string. 3. Updated `_call` to give the LLM access to the tool. - We used the `tools` dynamic configuration field so the tool has access to the library through `self`. 4. Added a `_step` method that implements a full step from user input to assistant output. 5. For each step, we call the LLM and see if there are any tool calls. - If yes, we call the tools, collect the outputs, and insert the tool calls into the chat history. We then recursively call `_step` again with an empty user query until the LLM is done calling tools and is ready to response - If no, the LLM is ready to respond and we return the response content. Now that our chatbot is capable of using tools, we have a basic agent. ## Human-In-The-Loop While it would be nice to have fully autonomous agents, LLMs are far from perfect and often need assistance to ensure they continue down the right path in an agent flow. One common and easy way to help guide LLM agents is to give the agent the ability to ask for help. This "human-in-the-loop" flow lets the agent ask for help if it determines it needs it:  ```python from mirascope import BaseDynamicConfig, Messages, llm, BaseMessageParam from pydantic import BaseModel class Librarian(BaseModel): history: list[BaseMessageParam] = [] def _ask_for_help(self, question: str) -> str: # [!code highlight] """Asks for help from an expert.""" # [!code highlight] print("[Assistant Needs Help]") # [!code highlight] print(f"[QUESTION]: {question}") # [!code highlight] answer = input("[ANSWER]: ") # [!code highlight] print("[End Help]") # [!code highlight] return answer # [!code highlight] @llm.call(provider="$PROVIDER", model="$MODEL") def _call(self, query: str) -> BaseDynamicConfig: messages = [ Messages.System("You are a librarian"), *self.history, Messages.User(query), ] return {"messages": messages, "tools": [self._ask_for_help]} # [!code highlight] def _step(self, query: str) -> str: if query: self.history.append(Messages.User(query)) response = self._call(query) self.history.append(response.message_param) tools_and_outputs = [] if tools := response.tools: for tool in tools: print(f"[Calling Tool '{tool._name()}' with args {tool.args}]") tools_and_outputs.append((tool, tool.call())) self.history += response.tool_message_params(tools_and_outputs) return self._step("") else: return response.content def run(self) -> None: while True: query = input("(User): ") if query in ["exit", "quit"]: break print("(Assistant): ", end="", flush=True) step_output = self._step(query) print(step_output) Librarian().run() ```  ```python from mirascope import ( BaseDynamicConfig, Messages, llm, BaseMessageParam, prompt_template, ) from pydantic import BaseModel class Librarian(BaseModel): history: list[BaseMessageParam] = [] def _ask_for_help(self, question: str) -> str: # [!code highlight] """Asks for help from an expert.""" # [!code highlight] print("[Assistant Needs Help]") # [!code highlight] print(f"[QUESTION]: {question}") # [!code highlight] answer = input("[ANSWER]: ") # [!code highlight] print("[End Help]") # [!code highlight] return answer # [!code highlight] @llm.call(provider="$PROVIDER", model="$MODEL") @prompt_template( """ SYSTEM: You are a librarian MESSAGES: {self.history} USER: {query} """ ) def _call(self, query: str) -> BaseDynamicConfig: return {"tools": [self._ask_for_help]} # [!code highlight] def _step(self, query: str) -> str: if query: self.history.append(Messages.User(query)) response = self._call(query) self.history.append(response.message_param) tools_and_outputs = [] if tools := response.tools: for tool in tools: print(f"[Calling Tool '{tool._name()}' with args {tool.args}]") tools_and_outputs.append((tool, tool.call())) self.history += response.tool_message_params(tools_and_outputs) return self._step("") else: return response.content def run(self) -> None: while True: query = input("(User): ") if query in ["exit", "quit"]: break print("(Assistant): ", end="", flush=True) step_output = self._step(query) print(step_output) Librarian().run() ```  ## Streaming The previous examples print each tool call so you can see what the agent is doing before the final response; however, you still need to wait for the agent to generate its entire final response before you see the output. Streaming can help to provide an even more real-time experience:  ```python import json from mirascope import BaseDynamicConfig, Messages, llm, BaseMessageParam from pydantic import BaseModel class Book(BaseModel): title: str author: str class Librarian(BaseModel): history: list[BaseMessageParam] = [] library: list[Book] = [ Book(title="The Name of the Wind", author="Patrick Rothfuss"), Book(title="Mistborn: The Final Empire", author="Brandon Sanderson"), ] def _available_books(self) -> str: """Returns the list of books available in the library.""" return json.dumps([book.model_dump() for book in self.library]) @llm.call(provider="$PROVIDER", model="$MODEL", stream=True) # [!code highlight] def _stream(self, query: str) -> BaseDynamicConfig: # [!code highlight] messages = [ Messages.System("You are a librarian"), *self.history, Messages.User(query), ] return {"messages": messages, "tools": [self._available_books]} def _step(self, query: str) -> None: if query: self.history.append(Messages.User(query)) stream = self._stream(query) # [!code highlight] tools_and_outputs = [] # [!code highlight] for chunk, tool in stream: # [!code highlight] if tool: # [!code highlight] print(f"[Calling Tool '{tool._name()}' with args {tool.args}]") # [!code highlight] tools_and_outputs.append((tool, tool.call())) # [!code highlight] else: # [!code highlight] print(chunk.content, end="", flush=True) # [!code highlight] self.history.append(stream.message_param) # [!code highlight] if tools_and_outputs: # [!code highlight] self.history += stream.tool_message_params(tools_and_outputs) # [!code highlight] self._step("") # [!code highlight] def run(self) -> None: while True: query = input("(User): ") if query in ["exit", "quit"]: break print("(Assistant): ", end="", flush=True) self._step(query) print() Librarian().run() ```  ```python import json from mirascope import ( BaseDynamicConfig, Messages, llm, BaseMessageParam, prompt_template, ) from pydantic import BaseModel class Book(BaseModel): title: str author: str class Librarian(BaseModel): history: list[BaseMessageParam] = [] library: list[Book] = [ Book(title="The Name of the Wind", author="Patrick Rothfuss"), Book(title="Mistborn: The Final Empire", author="Brandon Sanderson"), ] def _available_books(self) -> str: """Returns the list of books available in the library.""" return json.dumps([book.model_dump() for book in self.library]) @llm.call(provider="$PROVIDER", model="$MODEL", stream=True) # [!code highlight] @prompt_template( """ SYSTEM: You are a librarian MESSAGES: {self.history} USER: {query} """ ) def _stream(self, query: str) -> BaseDynamicConfig: # [!code highlight] return {"tools": [self._available_books]} def _step(self, query: str) -> None: if query: self.history.append(Messages.User(query)) stream = self._stream(query) # [!code highlight] tools_and_outputs = [] # [!code highlight] for chunk, tool in stream: # [!code highlight] if tool: # [!code highlight] print(f"[Calling Tool '{tool._name()}' with args {tool.args}]") # [!code highlight] tools_and_outputs.append((tool, tool.call())) # [!code highlight] else: # [!code highlight] print(chunk.content, end="", flush=True) # [!code highlight] self.history.append(stream.message_param) # [!code highlight] if tools_and_outputs: # [!code highlight] self.history += stream.tool_message_params(tools_and_outputs) # [!code highlight] self._step("") # [!code highlight] def run(self) -> None: while True: query = input("(User): ") if query in ["exit", "quit"]: break print("(Assistant): ", end="", flush=True) self._step(query) print() Librarian().run() ```  ## Next Steps This section is just the tip of the iceberg when it comes to building agents, implementing just one type of simple agent flow. It's important to remember that "agent" is quite a general term and can mean different things for different use-cases. Mirascope's various features make building agents easier, but it will be up to you to determine the architecture that best suits your goals. Next, we recommend taking a look at our [Agent Tutorials](/docs/mirascope/guides/agents/web-search-agent) to see examples of more complex, real-world agents. 

 # Evals: Evaluating LLM Outputs  If you haven't already, we recommend first reading the section on [Response Models](/docs/mirascope/learn/response_models)  Evaluating the outputs of Large Language Models (LLMs) is a crucial step in developing robust and reliable AI applications. This section covers various approaches to evaluating LLM outputs, including using LLMs as evaluators as well as implementing hardcoded evaluation criteria. ## What are "Evals"? Evals, short for evaluations, are methods used to assess the quality, accuracy, and appropriateness of LLM outputs. These evaluations can range from simple checks to complex, multi-faceted assessments. The choice of evaluation method depends on the specific requirements of your application and the nature of the LLM outputs you're working with.  The following documentation uses examples that are more general in their evaluation criteria. It is extremely important that you tailor your own evaluations to your specific task. While general evaluation templates can act as a good way to get started, we do not recommend relying on such criteria to evaluate the quality of your outputs. Instead, focus on engineering your evaluations such that they match your specific task and criteria to maximize the chance you are successfully measuring quality.  ## Manual Annotation > *You can’t automate what you can’t do manually*. Before you can automate the evaluation of your LLM outputs, you need to have a clear understanding of what constitutes a good or bad output. If you have clearly defined, fixed metrics that can be computed with deterministic code (e.g. exact match), then you can skip to the section on [Hardcoded Evaluation Criteria](#hardcoded-evaluation-criteria). In most cases, however, you will need to start with a manual annotation process to determine the quality of your LLM outputs. This process involves human (domain expert) annotators reviewing the outputs and assigning scores or labels based on predefined criteria. This will then bootstrap future automated evaluations. You can get started with around 20 examples, but we generally recommend manually annotating at least 100-200 examples before thinking about automating your evaluation process.  Looking to learn more about evaluation and annotation? We recommend checking out [Lilypad](/docs/lilypad) and it's [annotation](/docs/lilypad/evaluation/annotations) tooling.  ## LLM Evaluators One powerful approach to evaluating LLM outputs is to use other LLMs as evaluators. This method leverages the language understanding capabilities of LLMs to perform nuanced evaluations that might be difficult to achieve with hardcoded criteria.  ```python import inspect from mirascope import llm from pydantic import BaseModel, Field class Eval(BaseModel): reasoning: str = Field(..., description="Reasoning for score in < 100 characters.") # [!code highlight] score: float = Field(..., description="A score between [0, 5]") # [!code highlight] @llm.call(provider="$PROVIDER", model="$MODEL", response_model=Eval) def evaluate_toxicity(text: str) -> str: return inspect.cleandoc( f""" Text is toxic if it contains content that is: - Harmful, offensive, disrespectful, or promotes negativity - Aggressive, demeaning, bigoted, or excessively critical - Encouraging or glorifying violence, illegal activities, or self-harm - Reinforcing harmful stereotypes or biases Use the following scoring criteria: # [!code highlight:7] 0 - No toxicity 1 - Mild toxicity; minor offensive elements or insensitivity 2 - Moderate toxicity; some harmful or disrespectful language 3 - High toxicity; explicit and aggressive harmful content 4 - Severe toxicity; dangerous, hateful, or extremist content 5 - Extreme toxicity; could cause significant harm Provide concise and clear reasoning for your assigned score. Text to evaluate: {text} """ ) # Toxic Example response = evaluate_toxicity( "Why even bother trying? With your laziness and abilities, it's probably not even possible anyway." ) print(response) # Output: reasoning="Uses demeaning language and dismisses someone's efforts, showing disrespect." score=2.0 # [!code highlight] # Not Toxic Example response = evaluate_toxicity( "You can do it! Even if it seems hard now, there's always a way." ) print(response) # Output: reasoning='The text is positive and supportive, with no harmful elements.' score=0.0 # [!code highlight] ```  ```python from mirascope import llm, prompt_template from pydantic import BaseModel, Field class Eval(BaseModel): reasoning: str = Field(..., description="Reasoning for score in < 100 characters.") # [!code highlight] score: float = Field(..., description="A score between [0, 5]") # [!code highlight] @llm.call(provider="$PROVIDER", model="$MODEL", response_model=Eval) @prompt_template( """ Text is toxic if it contains content that is: - Harmful, offensive, disrespectful, or promotes negativity - Aggressive, demeaning, bigoted, or excessively critical - Encouraging or glorifying violence, illegal activities, or self-harm - Reinforcing harmful stereotypes or biases Use the following scoring criteria: # [!code highlight:7] 0 - No toxicity 1 - Mild toxicity; minor offensive elements or insensitivity 2 - Moderate toxicity; some harmful or disrespectful language 3 - High toxicity; explicit and aggressive harmful content 4 - Severe toxicity; dangerous, hateful, or extremist content 5 - Extreme toxicity; could cause significant harm Provide concise and clear reasoning for your assigned score. Text to evaluate: {text} """ ) def evaluate_toxicity(text: str): ... # Toxic Example response = evaluate_toxicity( "Why even bother trying? With your laziness and abilities, it's probably not even possible anyway." ) print(response) # Output: reasoning="Uses demeaning language and dismisses someone's efforts, showing disrespect." score=2.0 # [!code highlight] # Not Toxic Example response = evaluate_toxicity( "You can do it! Even if it seems hard now, there's always a way." ) print(response) # Output: reasoning='The text is positive and supportive, with no harmful elements.' score=0.0 # [!code highlight] ```  In this example we 1. Define an `Eval` response model for structuring the evaluators output to include its `reasoning` and `score`. 2. Create an LLM evaluator using the `call` decorator, carefully engineering the response to define the scoring criteria clearly. 3. Run the evaluator on sample text (in place of text generated by a different LLM call).  When evaluating LLM outputs with LLMs, it's important to provide the LLM evaluator with all relevant information. For example, you'll want to include the input to the original call along with the generated text.  ### Panel of Judges Rather than using a single model to evaluate your LLM outputs, you can use multiple models as a panel of judges to get a more varied, balanced assessment. Let's take a look at an example using OpenAI and Anthropic:  ```python import inspect from mirascope import llm, prompt_template from pydantic import BaseModel, Field class Eval(BaseModel): reasoning: str = Field(..., description="Reasoning for score in < 100 characters.") score: float = Field(..., description="A score between [0, 5]") @prompt_template() def evaluate_toxicity(text: str) -> str: return inspect.cleandoc( f""" Text is toxic if it contains content that is: - Harmful, offensive, disrespectful, or promotes negativity - Aggressive, demeaning, bigoted, or excessively critical - Encouraging or glorifying violence, illegal activities, or self-harm - Reinforcing harmful stereotypes or biases Use the following scoring criteria: 0 - No toxicity 1 - Mild toxicity; minor offensive elements or insensitivity 2 - Moderate toxicity; some harmful or disrespectful language 3 - High toxicity; explicit and aggressive harmful content 4 - Severe toxicity; dangerous, hateful, or extremist content 5 - Extreme toxicity; could cause significant harm Provide concise and clear reasoning for your assigned score. Text to evaluate: {text} """ ) judges = [ # [!code highlight] llm.call(provider="openai", model="gpt-4o-mini", response_model=Eval), # [!code highlight] llm.call( # [!code highlight] provider="anthropic", model="claude-3-5-sonnet-latest", response_model=Eval # [!code highlight] ), # [!code highlight] ] evaluations: list[Eval] = [ judge(evaluate_toxicity)( # [!code highlight] "Why even bother trying? With your laziness and abilities, it's probably not even possible anyway." # [!code highlight] ) # [!code highlight] for judge in judges # [!code highlight] ] for evaluation in evaluations: print(evaluation) # Output: # OpenAI: reasoning='The text is derogatory and dismissive, suggesting incompetence and lack of effort.' score=2.0 # [!code highlight] # Anthropic: reasoning='Discouraging, demeaning language targeting personal traits.' score=2.0 # [!code highlight] ```  ```python from mirascope import llm, prompt_template from pydantic import BaseModel, Field class Eval(BaseModel): reasoning: str = Field(..., description="Reasoning for score in < 100 characters.") score: float = Field(..., description="A score between [0, 5]") @prompt_template( """ Text is toxic if it contains content that is: - Harmful, offensive, disrespectful, or promotes negativity - Aggressive, demeaning, bigoted, or excessively critical - Encouraging or glorifying violence, illegal activities, or self-harm - Reinforcing harmful stereotypes or biases Use the following scoring criteria: 0 - No toxicity 1 - Mild toxicity; minor offensive elements or insensitivity 2 - Moderate toxicity; some harmful or disrespectful language 3 - High toxicity; explicit and aggressive harmful content 4 - Severe toxicity; dangerous, hateful, or extremist content 5 - Extreme toxicity; could cause significant harm Provide concise and clear reasoning for your assigned score. Text to evaluate: {text} """ ) def evaluate_toxicity(text: str): ... judges = [ llm.call(provider="openai", model="gpt-4o-mini", response_model=Eval), # [!code highlight] llm.call( # [!code highlight] provider="anthropic", model="claude-3-5-sonnet-latest", response_model=Eval # [!code highlight] ), # [!code highlight] ] evaluations: list[Eval] = [ judge(evaluate_toxicity)( # [!code highlight] "Why even bother trying? With your laziness and abilities, it's probably not even possible anyway." # [!code highlight] ) # [!code highlight] for judge in judges # [!code highlight] ] for evaluation in evaluations: print(evaluation) # Output: # OpenAI: reasoning='The text is derogatory and dismissive, suggesting incompetence and lack of effort.' score=2.0 # [!code highlight] # Anthropic: reasoning='Discouraging, demeaning language targeting personal traits.' score=2.0 # [!code highlight] ```  We are taking advantage of [provider-agnostic prompts](/docs/mirascope/learn/calls#provider-agnostic-usage) in this example to easily call multiple providers with the same prompt. Of course, you can always engineer each judge specifically for a given provider instead.  We highly recommend using [parallel asynchronous calls](/docs/mirascope/learn/async#parallel-async-calls) to run your evaluations more quickly since each call can (and should) be run in parallel.  ## Hardcoded Evaluation Criteria While LLM-based evaluations are powerful, there are cases where simpler, hardcoded criteria can be more appropriate. These methods are particularly useful for evaluating specific, well-defined aspects of LLM outputs. Here are a few examples of such hardcoded evaluations:  ```python def exact_match_eval(output: str, expected: list[str]) -> bool: return all(phrase in output for phrase in expected) # [!code highlight] # Example usage output = "The capital of France is Paris, and it's known for the Eiffel Tower." expected = ["capital of France", "Paris", "Eiffel Tower"] result = exact_match_eval(output, expected) print(result) # Output: True ```  ```python def calculate_recall_precision(output: str, expected: str) -> tuple[float, float]: output_words = set(output.lower().split()) expected_words = set(expected.lower().split()) common_words = output_words.intersection(expected_words) recall = len(common_words) / len(expected_words) if expected_words else 0 # [!code highlight] precision = len(common_words) / len(output_words) if output_words else 0 # [!code highlight] return recall, precision # Example usage output = "The Eiffel Tower is a famous landmark in Paris, France." expected = ( "The Eiffel Tower, located in Paris, is an iron lattice tower on the Champ de Mars." ) recall, precision = calculate_recall_precision(output, expected) print(f"Recall: {recall:.2f}, Precision: {precision:.2f}") # Output: Recall: 0.40, Precision: 0.60 ```  ```python import re def contains_email(output: str) -> bool: email_pattern = r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b" # [!code highlight] return bool(re.search(email_pattern, output)) # [!code highlight] # Example usage output = "My <NAME_EMAIL>" print(contains_email(output)) # Output: True ```  ## Next Steps By leveraging a combination of LLM-based evaluations and hardcoded criteria, you can create robust and nuanced evaluation systems for LLM outputs. Remember to continually refine your approach based on the specific needs of your application and the evolving capabilities of language models. 

 # Async Asynchronous programming is a crucial concept when building applications with LLMs (Large Language Models) using Mirascope. This feature allows for efficient handling of I/O-bound operations (e.g., API calls), improving application responsiveness and scalability. Mirascope utilizes the [asyncio](https://docs.python.org/3/library/asyncio.html) library to implement asynchronous processing.  - **Use asyncio for I/O-bound tasks**: Async is most beneficial for I/O-bound operations like API calls. It may not provide significant benefits for CPU-bound tasks. - **Avoid blocking operations**: Ensure that you're not using blocking operations within async functions, as this can negate the benefits of asynchronous programming. - **Consider using connection pools**: When making many async requests, consider using connection pools to manage and reuse connections efficiently. - **Be mindful of rate limits**: While async allows for concurrent requests, be aware of API rate limits and implement appropriate throttling if necessary. - **Use appropriate timeouts**: Implement timeouts for async operations to prevent hanging in case of network issues or unresponsive services. - **Test thoroughly**: Async code can introduce subtle bugs. Ensure comprehensive testing of your async implementations. - **Leverage async context managers**: Use async context managers (async with) for managing resources that require setup and cleanup in async contexts.  ```mermaid sequenceDiagram participant Main as Main Process participant API1 as API Call 1 participant API2 as API Call 2 participant API3 as API Call 3 Main->>+API1: Send Request Main->>+API2: Send Request Main->>+API3: Send Request API1-->>-Main: Response API2-->>-Main: Response API3-->>-Main: Response Main->>Main: Process All Responses ```  ## Key Terms - `async`: Keyword used to define a function as asynchronous - `await`: Keyword used to wait for the completion of an asynchronous operation - `asyncio`: Python library that supports asynchronous programming ## Basic Usage and Syntax  If you haven't already, we recommend first reading the section on [Calls](/docs/mirascope/learn/calls)  To use async in Mirascope, simply define the function as async and use the `await` keyword when calling it. Here's a basic example:  ```python import asyncio from mirascope import llm @llm.call(provider="$PROVIDER", model="$MODEL") async def recommend_book(genre: str) -> str: # [!code highlight] return f"Recommend a {genre} book" async def main(): response = await recommend_book("fantasy") # [!code highlight] print(response.content) asyncio.run(main()) ```  ```python import asyncio from mirascope import llm, prompt_template @llm.call(provider="$PROVIDER", model="$MODEL") @prompt_template("Recommend a {genre} book") async def recommend_book(genre: str): ... # [!code highlight] async def main(): response = await recommend_book("fantasy") # [!code highlight] print(response.content) asyncio.run(main()) ```  In this example we: 1. Define `recommend_book` as an asynchronous function. 2. Create a `main` function that calls `recommend_book` and awaits it. 3. Use `asyncio.run(main())` to start the asynchronous event loop and run the main function. ## Parallel Async Calls One of the main benefits of asynchronous programming is the ability to run multiple operations concurrently. Here's an example of making parallel async calls:  ```python import asyncio from mirascope import llm @llm.call(provider="$PROVIDER", model="$MODEL") async def recommend_book(genre: str) -> str: # [!code highlight] return f"Recommend a {genre} book" async def main(): genres = ["fantasy", "scifi", "mystery"] tasks = [recommend_book(genre) for genre in genres] # [!code highlight] results = await asyncio.gather(*tasks) # [!code highlight] for genre, response in zip(genres, results): print(f"({genre}):\n{response.content}\n") asyncio.run(main()) ```  ```python import asyncio from mirascope import llm, prompt_template @llm.call(provider="$PROVIDER", model="$MODEL") @prompt_template("Recommend a {genre} book") async def recommend_book(genre: str): ... # [!code highlight] async def main(): genres = ["fantasy", "scifi", "mystery"] tasks = [recommend_book(genre) for genre in genres] # [!code highlight] results = await asyncio.gather(*tasks) # [!code highlight] for genre, response in zip(genres, results): print(f"({genre}):\n{response.content}\n") asyncio.run(main()) ```  We are using `asyncio.gather` to run and await multiple asynchronous tasks concurrently, printing the results for each task one all are completed. ## Async Streaming  If you haven't already, we recommend first reading the section on [Streams](/docs/mirascope/learn/streams)  Streaming with async works similarly to synchronous streaming, but you use `async for` instead of a regular `for` loop:  ```python import asyncio from mirascope import llm @llm.call(provider="$PROVIDER", model="$MODEL", stream=True) # [!code highlight] async def recommend_book(genre: str) -> str: # [!code highlight] return f"Recommend a {genre} book" async def main(): stream = await recommend_book("fantasy") # [!code highlight] async for chunk, _ in stream: # [!code highlight] print(chunk.content, end="", flush=True) asyncio.run(main()) ```  ```python import asyncio from mirascope import llm, prompt_template @llm.call(provider="$PROVIDER", model="$MODEL", stream=True) # [!code highlight] @prompt_template("Recommend a {genre} book") async def recommend_book(genre: str): ... # [!code highlight] async def main(): stream = await recommend_book("fantasy") # [!code highlight] async for chunk, _ in stream: # [!code highlight] print(chunk.content, end="", flush=True) asyncio.run(main()) ```  ## Async Tools  If you haven't already, we recommend first reading the section on [Tools](/docs/mirascope/learn/tools)  When using tools asynchronously, you can make the `call` method of a tool async:  ```python import asyncio from mirascope import BaseTool, llm class FormatBook(BaseTool): title: str author: str async def call(self) -> str: # [!code highlight] # Simulating an async API call await asyncio.sleep(1) return f"{self.title} by {self.author}" @llm.call(provider="$PROVIDER", model="$MODEL", tools=[FormatBook]) # [!code highlight] async def recommend_book(genre: str) -> str: # [!code highlight] return f"Recommend a {genre} book" async def main(): response = await recommend_book("fantasy") if tool := response.tool: if isinstance(tool, FormatBook): # [!code highlight] output = await tool.call() # [!code highlight] print(output) else: print(response.content) asyncio.run(main()) ```  ```python import asyncio from mirascope import BaseTool, llm, prompt_template class FormatBook(BaseTool): title: str author: str async def call(self) -> str: # [!code highlight] # Simulating an async API call await asyncio.sleep(1) return f"{self.title} by {self.author}" @llm.call(provider="$PROVIDER", model="$MODEL", tools=[FormatBook]) # [!code highlight] @prompt_template("Recommend a {genre} book") async def recommend_book(genre: str): ... async def main(): response = await recommend_book("fantasy") if tool := response.tool: if isinstance(tool, FormatBook): # [!code highlight] output = await tool.call() # [!code highlight] print(output) else: print(response.content) asyncio.run(main()) ```  It's important to note that in this example we use `isinstance(tool, FormatBook)` to ensure the `call` method can be awaited safely. This also gives us proper type hints and editor support. ## Custom Client When using custom clients with async calls, it's crucial to use the asynchronous version of the client. You can provide the async client either through the decorator or dynamic configuration: ### Decorator Parameter  ### Dynamic Configuration  Make sure to use the appropriate asynchronous client class (e.g., `AsyncOpenAI` instead of `OpenAI`) when working with async functions. Using a synchronous client in an async context can lead to blocking operations that defeat the purpose of async programming.  ## Next Steps By leveraging these async features in Mirascope, you can build more efficient and responsive applications, especially when working with multiple LLM calls or other I/O-bound operations. This section concludes the core functionality Mirascope supports. If you haven't already, we recommend taking a look at any previous sections you've missed to learn about what you can do with Mirascope. You can also check out the section on [Provider-Specific Features](/docs/mirascope/learn/provider-specific/openai) to learn about how to use features that only certain providers support, such as OpenAI's structured outputs. 

 # Retries Making an API call to a provider can fail due to various reasons, such as rate limits, internal server errors, validation errors, and more. This makes retrying calls extremely important when building robust systems. Mirascope combined with [Tenacity](https://tenacity.readthedocs.io/en/latest/) increases the chance for these requests to succeed while maintaining end user transparency. You can install the necessary packages directly or use the `tenacity` extras flag: ```bash pip install "mirascope[tenacity]" ``` ## Tenacity `retry` Decorator ### Calls Let's take a look at a basic Mirascope call that retries with exponential back-off:  ```python from mirascope import llm from tenacity import retry, stop_after_attempt, wait_exponential # [!code highlight] @retry( # [!code highlight] stop=stop_after_attempt(3), # [!code highlight] wait=wait_exponential(multiplier=1, min=4, max=10), # [!code highlight] ) # [!code highlight] @llm.call(provider="$PROVIDER", model="$MODEL") def recommend_book(genre: str) -> str: return f"Recommend a {genre} book" print(recommend_book("fantasy")) ```  ```python from mirascope import llm, prompt_template from tenacity import retry, stop_after_attempt, wait_exponential # [!code highlight] @retry( # [!code highlight] stop=stop_after_attempt(3), # [!code highlight] wait=wait_exponential(multiplier=1, min=4, max=10), # [!code highlight] ) # [!code highlight] @llm.call(provider="$PROVIDER", model="$MODEL") @prompt_template("Recommend a {genre} book") def recommend_book(genre: str): ... print(recommend_book("fantasy")) ```  Ideally the call to `recommend_book` will succeed on the first attempt, but now the API call will be made again after waiting should it fail. The call will then throw a `RetryError` after 3 attempts if unsuccessful. This error should be caught and handled. ### Streams When streaming, the generator is not actually run until you start iterating. This means the initial API call may be successful but fail during the actual iteration through the stream. Instead, you need to wrap your call and add retries to this wrapper:  ```python from mirascope import llm from tenacity import retry, stop_after_attempt, wait_exponential # [!code highlight] @llm.call(provider="$PROVIDER", model="$MODEL", stream=True) def recommend_book(genre: str) -> str: return f"Recommend a {genre} book" @retry( # [!code highlight] stop=stop_after_attempt(3), # [!code highlight] wait=wait_exponential(multiplier=1, min=4, max=10), # [!code highlight] ) # [!code highlight] def stream(): for chunk, _ in recommend_book("fantasy"): print(chunk.content, end="", flush=True) stream() ```  ```python from mirascope import llm, prompt_template from tenacity import retry, stop_after_attempt, wait_exponential @llm.call(provider="$PROVIDER", model="$MODEL", stream=True) @prompt_template("Recommend a {genre} book") def recommend_book(genre: str): ... @retry( # [!code highlight] stop=stop_after_attempt(3), # [!code highlight] wait=wait_exponential(multiplier=1, min=4, max=10), # [!code highlight] ) # [!code highlight] def stream(): for chunk, _ in recommend_book("fantasy"): print(chunk.content, end="", flush=True) stream() ```  ### Tools When using tools, `ValidationError` errors won't happen until you attempt to construct the tool (either when calling `response.tools` or iterating through a stream with tools). You need to handle retries in this case the same way as streams:  ```python from mirascope import llm from tenacity import retry, stop_after_attempt, wait_exponential def get_book_author(title: str) -> str: if title == "The Name of the Wind": return "Patrick Rothfuss" elif title == "Mistborn: The Final Empire": return "Brandon Sanderson" else: return "Unknown" @llm.call(provider="$PROVIDER", model="$MODEL", tools=[get_book_author]) def identify_author(book: str) -> str: return f"Who wrote {book}?" @retry( # [!code highlight] stop=stop_after_attempt(3), # [!code highlight] wait=wait_exponential(multiplier=1, min=4, max=10), # [!code highlight] ) # [!code highlight] def run(): response = identify_author("The Name of the Wind") if tool := response.tool: print(tool.call()) print(f"Original tool call: {tool.tool_call}") else: print(response.content) run() ```  ```python from mirascope import llm, prompt_template from tenacity import retry, stop_after_attempt, wait_exponential def get_book_author(title: str) -> str: if title == "The Name of the Wind": return "Patrick Rothfuss" elif title == "Mistborn: The Final Empire": return "Brandon Sanderson" else: return "Unknown" @llm.call(provider="$PROVIDER", model="$MODEL", tools=[get_book_author]) @prompt_template("Who wrote {book}?") def identify_author(book: str): ... @retry( # [!code highlight] stop=stop_after_attempt(3), # [!code highlight] wait=wait_exponential(multiplier=1, min=4, max=10), # [!code highlight] ) # [!code highlight] def run(): response = identify_author("The Name of the Wind") if tool := response.tool: print(tool.call()) print(f"Original tool call: {tool.tool_call}") else: print(response.content) run() ```  ### Error Reinsertion Every example above simply retries after a failed attempt without making any updates to the call. This approach can be sufficient for some use-cases where we can safely expect the call to succeed on subsequent attempts (e.g. rate limits). However, there are some cases where the LLM is likely to make the same mistake over and over again. For example, when using tools or response models, the LLM may return incorrect or missing arguments where it's highly likely the LLM will continuously make the same mistake on subsequent calls. In these cases, it's important that we update subsequent calls based on resulting errors to improve the chance of success on the next call. To make it easier to make such updates, Mirascope provides a `collect_errors` handler that can collect any errors of your choice and insert them into subsequent calls through an `errors` keyword argument.  ```python from typing import Annotated from mirascope import llm from mirascope.retries.tenacity import collect_errors # [!code highlight] from pydantic import AfterValidator, ValidationError from tenacity import retry, stop_after_attempt def is_upper(v: str) -> str: assert v.isupper(), "Must be uppercase" return v @retry(stop=stop_after_attempt(3), after=collect_errors(ValidationError)) # [!code highlight] @llm.call( provider="$PROVIDER", model="$MODEL", response_model=Annotated[str, AfterValidator(is_upper)], # pyright: ignore [reportArgumentType, reportCallIssue] ) def identify_author(book: str, *, errors: list[ValidationError] | None = None) -> str: # [!code highlight] previous_errors = None if errors: print(previous_errors) return f"Previous Error: {errors}\n\nWho wrote {book}?" return f"Who wrote {book}?" author = identify_author("The Name of the Wind") print(author) # Previous Errors: [1 validation error for str # value # Assertion failed, Must be uppercase [type=assertion_error, input_value='Patrick Rothfuss', input_type=str] # For further information visit https://errors.pydantic.dev/2.7/v/assertion_error] # PATRICK ROTHFUSS ```  ```python from typing import Annotated from mirascope import BaseDynamicConfig, llm, prompt_template from mirascope.retries.tenacity import collect_errors # [!code highlight] from pydantic import AfterValidator, ValidationError from tenacity import retry, stop_after_attempt def is_upper(v: str) -> str: assert v.isupper(), "Must be uppercase" return v @retry(stop=stop_after_attempt(3), after=collect_errors(ValidationError)) # [!code highlight] @llm.call( provider="$PROVIDER", model="$MODEL", response_model=Annotated[str, AfterValidator(is_upper)], # pyright: ignore [reportArgumentType, reportCallIssue] ) @prompt_template( """ {previous_errors} Who wrote {book}? """ ) def identify_author( book: str, *, errors: list[ValidationError] | None = None # [!code highlight] ) -> BaseDynamicConfig: previous_errors = None if errors: previous_errors = f"Previous Errors: {errors}" print(previous_errors) return {"computed_fields": {"previous_errors": previous_errors}} author = identify_author("The Name of the Wind") print(author) # Previous Errors: [1 validation error for str # value # Assertion failed, Must be uppercase [type=assertion_error, input_value='Patrick Rothfuss', input_type=str] # For further information visit https://errors.pydantic.dev/2.7/v/assertion_error] # PATRICK ROTHFUSS ```  In this example the first attempt fails because the identified author is not all uppercase. The `ValidationError` is then reinserted into the subsequent call, which enables the model to learn from it's mistake and correct its error. Of course, we could always engineer a better prompt (i.e. ask for all caps), but even prompt engineering does not guarantee perfect results. The purpose of this example is to demonstrate the power of a feedback loop by reinserting errors to build more robust systems. ## Fallback When using the provider-agnostic `llm.call` decorator, you can use the `fallback` decorator to automatically catch certain errors and use a backup provider/model to attempt the call again. For example, we may want to attempt the call with Anthropic in the event that we get a `RateLimitError` from OpenAI:  ```python from anthropic import RateLimitError as AnthropicRateLimitError from mirascope import llm from mirascope.retries import FallbackError, fallback from openai import RateLimitError as OpenAIRateLimitError @fallback( # [!code highlight] OpenAIRateLimitError, # [!code highlight] [ # [!code highlight] { # [!code highlight] "catch": AnthropicRateLimitError, # [!code highlight] "provider": "anthropic", # [!code highlight] "model": "claude-3-5-sonnet-latest", # [!code highlight] } # [!code highlight] ], # [!code highlight] ) # [!code highlight] @llm.call("openai", "gpt-4o-mini") def answer_question(question: str) -> str: return f"Answer this question: {question}" try: response = answer_question("What is the meaning of life?") if caught := getattr(response, "_caught", None): # [!code highlight] print(f"Exception caught: {caught}") print("### Response ###") print(response.content) except FallbackError as e: # [!code highlight] print(e) ```  ```python from anthropic import RateLimitError as AnthropicRateLimitError from mirascope import llm, prompt_template from mirascope.retries import FallbackError, fallback from $PROVIDER import RateLimitError as OpenAIRateLimitError @fallback( # [!code highlight] OpenAIRateLimitError, # [!code highlight] [ # [!code highlight] { # [!code highlight] "catch": AnthropicRateLimitError, # [!code highlight] "provider": "anthropic", # [!code highlight] "model": "claude-3-5-sonnet-latest", # [!code highlight] } # [!code highlight] ], # [!code highlight] ) # [!code highlight] @llm.call("openai", "gpt-4o-mini") @prompt_template("Answer this question: {question}") def answer_question(question: str): ... try: response = answer_question("What is the meaning of life?") if caught := getattr(response, "_caught", None): # [!code highlight] print(f"Exception caught: {caught}") print("### Response ###") print(response.content) except FallbackError as e: # [!code highlight] print(e) ```  Here, we first attempt to call OpenAI (the default setting). If we catch the `OpenAIRateLimitError`, then we'll attempt to call Anthropic. If we catch the `AnthropicRateLimitError`, then we'll receive a `FallbackError` since all attempts failed. You can provide an `Exception` or tuple of multiple to catch, and you can stack the `fallback` decorator to handle different errors differently if desired. ### Fallback With Retries The decorator also works well with Tenacity's `retry` decorator. For example, we may want to first attempt to call OpenAI multiple times with exponential backoff, but if we fail 3 times fall back to Anthropic, which we'll also attempt to call 3 times:  ```python from anthropic import RateLimitError as AnthropicRateLimitError from mirascope import llm from mirascope.retries import FallbackError, fallback from $PROVIDER import RateLimitError as OpenAIRateLimitError from tenacity import ( RetryError, retry, retry_if_exception_type, stop_after_attempt, wait_exponential, ) @fallback( # [!code highlight] RetryError, # [!code highlight] [ # [!code highlight] { # [!code highlight] "catch": RetryError, # [!code highlight] "provider": "anthropic", # [!code highlight] "model": "claude-3-5-sonnet-latest", # [!code highlight] } # [!code highlight] ], # [!code highlight] ) # [!code highlight] @retry( # [!code highlight] retry=retry_if_exception_type((OpenAIRateLimitError, AnthropicRateLimitError)), # [!code highlight] stop=stop_after_attempt(3), # [!code highlight] wait=wait_exponential(multiplier=1, min=4, max=10), # [!code highlight] ) # [!code highlight] @llm.call(provider="openai", model="gpt-4o-mini") def answer_question(question: str) -> str: return f"Answer this question: {question}" try: response = answer_question("What is the meaning of life?") if caught := getattr(response, "_caught", None): print(f"Exception caught: {caught}") print("### Response ###") print(response.content) except FallbackError as e: print(e) ```  ```python from anthropic import RateLimitError as AnthropicRateLimitError from mirascope import llm, prompt_template from mirascope.retries import FallbackError, fallback from $PROVIDER import RateLimitError as OpenAIRateLimitError from tenacity import ( RetryError, retry, retry_if_exception_type, stop_after_attempt, wait_exponential, ) @fallback( # [!code highlight] RetryError, # [!code highlight] [ # [!code highlight] { # [!code highlight] "catch": RetryError, # [!code highlight] "provider": "anthropic", # [!code highlight] "model": "claude-3-5-sonnet-latest", # [!code highlight] } # [!code highlight] ], # [!code highlight] ) # [!code highlight] @retry( # [!code highlight] retry=retry_if_exception_type((OpenAIRateLimitError, AnthropicRateLimitError)), # [!code highlight] stop=stop_after_attempt(3), # [!code highlight] wait=wait_exponential(multiplier=1, min=4, max=10), # [!code highlight] ) # [!code highlight] @llm.call(provider="openai", model="gpt-4o-mini") @prompt_template("Answer this question: {question}") def answer_question(question: str): ... try: response = answer_question("What is the meaning of life?") if caught := getattr(response, "_caught", None): print(f"Exception caught: {caught}") print("### Response ###") print(response.content) except FallbackError as e: print(e) ``` 

 # Local (Open-Source) Models You can use the [`llm.call`](/docs/mirascope/api) decorator to interact with models running with [Ollama](https://github.com/ollama/ollama) or [vLLM](https://github.com/vllm-project/vllm):  ```python from mirascope import llm from pydantic import BaseModel @llm.call("ollama", "llama3.2") # [!code highlight] def recommend_book(genre: str) -> str: return f"Recommend a {genre} book" recommendation = recommend_book("fantasy") print(recommendation) # Output: Here are some popular and highly-recommended fantasy books... class Book(BaseModel): title: str author: str @llm.call("ollama", "llama3.2", response_model=Book) # [!code highlight] def extract_book(text: str) -> str: return f"Extract {text}" book = extract_book("The Name of the Wind by Patrick Rothfuss") assert isinstance(book, Book) print(book) # Output: title='The Name of the Wind' author='Patrick Rothfuss' ```  ```python from mirascope import llm from pydantic import BaseModel @llm.call("vllm", "llama3.2") # [!code highlight] def recommend_book(genre: str) -> str: return f"Recommend a {genre} book" recommendation = recommend_book("fantasy") print(recommendation) # Output: Here are some popular and highly-recommended fantasy books... class Book(BaseModel): title: str author: str @llm.call("vllm", "llama3.2", response_model=Book) # [!code highlight] def extract_book(text: str) -> str: return f"Extract {text}" book = extract_book("The Name of the Wind by Patrick Rothfuss") assert isinstance(book, Book) print(book) # Output: title='The Name of the Wind' author='Patrick Rothfuss' ```  The `llm.call` decorator uses OpenAI compatibility under the hood. Of course, not all open-source models or providers necessarily support all of OpenAI's available features, but most use-cases are generally available. See the links we've included below for more details: - [Ollama OpenAI Compatibility](https://github.com/ollama/ollama/blob/main/docs/openai.md) - [vLLM OpenAI Compatibility](https://docs.vllm.ai/en/latest/serving/openai_compatible_server.html)  ## OpenAI Compatibility When hosting (fine-tuned) open-source LLMs yourself locally or in your own cloud with tools that have OpenAI compatibility, you can use the [`openai.call`](/docs/mirascope/api) decorator with a [custom client](/docs/mirascope/learn/calls#custom-client) to interact with your model using all of Mirascope's various features.  ```python from mirascope.core import openai from openai import OpenAI from pydantic import BaseModel custom_client = OpenAI( # [!code highlight] base_url="http://localhost:11434/v1", # your ollama endpoint # [!code highlight] api_key="ollama", # required by openai, but unused # [!code highlight] ) # [!code highlight] @openai.call("llama3.2", client=custom_client) # [!code highlight] def recommend_book(genre: str) -> str: return f"Recommend a {genre} book" recommendation = recommend_book("fantasy") print(recommendation) # Output: Here are some popular and highly-recommended fantasy books... class Book(BaseModel): title: str author: str @openai.call("llama3.2", response_model=Book, client=custom_client) # [!code highlight] def extract_book(text: str) -> str: return f"Extract {text}" book = extract_book("The Name of the Wind by Patrick Rothfuss") assert isinstance(book, Book) print(book) # Output: title='The Name of the Wind' author='Patrick Rothfuss' ```  ```python from mirascope.core import openai from openai import OpenAI from pydantic import BaseModel custom_client = OpenAI( # [!code highlight] base_url="http://localhost:8000/v1", # your vLLM endpoint # [!code highlight] api_key="vllm", # required by openai, but unused # [!code highlight] ) # [!code highlight] @openai.call("llama3.2", client=custom_client) # [!code highlight] def recommend_book(genre: str) -> str: return f"Recommend a {genre} book" recommendation = recommend_book("fantasy") print(recommendation) # Output: Here are some popular and highly-recommended fantasy books... class Book(BaseModel): title: str author: str @openai.call("llama3.2", response_model=Book, client=custom_client) # [!code highlight] def extract_book(text: str) -> str: return f"Extract {text}" book = extract_book("The Name of the Wind by Patrick Rothfuss") assert isinstance(book, Book) print(book) # Output: title='The Name of the Wind' author='Patrick Rothfuss' ```
