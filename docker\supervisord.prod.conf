[supervisord]
nodaemon=true
user=root
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid
childlogdir=/var/log/supervisor

[unix_http_server]
file=/var/run/supervisor.sock
chmod=0700

[supervisorctl]
serverurl=unix:///var/run/supervisor.sock

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

# FastAPI Application
[program:fastapi]
command=uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4 --worker-class uvicorn.workers.UvicornWorker
directory=/app/backend
environment=PYTHONPATH="/app/backend"
user=appuser
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/app/logs/app.log
stdout_logfile_maxbytes=100MB
stdout_logfile_backups=3
stderr_logfile=/app/logs/app_error.log
stderr_logfile_maxbytes=100MB
stderr_logfile_backups=3
priority=100
startsecs=10
startretries=3

# Nginx Reverse Proxy
[program:nginx]
command=nginx -g "daemon off;"
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/app/logs/nginx.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=3
stderr_logfile=/app/logs/nginx_error.log
stderr_logfile_maxbytes=50MB
stderr_logfile_backups=3
priority=200
startsecs=5
startretries=3

# Background Tasks Worker (if needed)
[program:worker]
command=python -m app.workers.background_tasks
directory=/app/backend
environment=PYTHONPATH="/app/backend"
user=appuser
autostart=false
autorestart=true
redirect_stderr=true
stdout_logfile=/app/logs/worker.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=3
priority=300
startsecs=10
startretries=3
