"""
Test to isolate the exact source of the _call() model error.
"""

import sys
import traceback
from pathlib import Path

def main():
    print("🔍 Isolating the _call() model error...")
    
    # Add backend to Python path
    project_root = Path(__file__).parent.parent
    backend_path = project_root / "backend"
    sys.path.insert(0, str(backend_path))
    
    # Load environment
    import dotenv
    dotenv.load_dotenv(project_root / ".env")
    
    print("1. Testing individual imports from main.py...")
    
    try:
        print("  - Importing settings and basic components...")
        from app.config.settings import get_settings
        from app.models.pydantic_models import UserInput
        print("  ✅ Basic imports successful")
    except Exception as e:
        print(f"  ❌ Basic imports failed: {e}")
        traceback.print_exc()
        return
    
    try:
        print("  - Importing performance metrics...")
        from app.main import performance_metrics
        print("  ✅ Performance metrics imported")
    except Exception as e:
        print(f"  ❌ Performance metrics failed: {e}")
        traceback.print_exc()
        return
    
    try:
        print("  - Importing connection manager...")
        from app.main import connection_manager
        print("  ✅ Connection manager imported")
    except Exception as e:
        print(f"  ❌ Connection manager failed: {e}")
        traceback.print_exc()
        return
        
    try:
        print("  - Testing TaskTool import specifically...")
        from app.main import task_tool
        print(f"  ✅ TaskTool imported: {type(task_tool)}")
    except Exception as e:
        print(f"  ❌ TaskTool import failed: {e}")
        traceback.print_exc()
        return
        
    try:
        print("  - Testing CalendarTool import specifically...")
        from app.main import calendar_tool
        print(f"  ✅ CalendarTool imported: {type(calendar_tool)}")
    except Exception as e:
        print(f"  ❌ CalendarTool import failed: {e}")
        traceback.print_exc()
        return
        
    try:
        print("  - Testing WebSearchTool import specifically...")
        from app.main import web_search_tool
        print(f"  ✅ WebSearchTool imported: {type(web_search_tool)}")
    except Exception as e:
        print(f"  ❌ WebSearchTool import failed: {e}")
        traceback.print_exc()
        return
        
    try:
        print("  - Testing HybridDatabaseSearchTool import specifically...")
        from app.main import database_search_tool
        print(f"  ✅ HybridDatabaseSearchTool imported: {type(database_search_tool)}")
    except Exception as e:
        print(f"  ❌ HybridDatabaseSearchTool import failed: {e}")
        traceback.print_exc()
        return
    
    print("\n🎉 All individual tool imports successful!")
    print("The error might be elsewhere in main.py or during module initialization.")

if __name__ == "__main__":
    main()
