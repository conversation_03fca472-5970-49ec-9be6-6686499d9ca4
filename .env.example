# Environment Configuration for AI-Powered Dashboard

# OpenRouter API Configuration (for LLM calls)
OPENROUTER_API_KEY=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
OPENROUTER_MODEL=tngtech/deepseek-r1t2-chimera:free

# LangSearch API Configuration (for web search)
LANGSEARCH_API_KEY=sk-255e41b73e404e198c2b326811ad9fdf

# Database Configuration
DATABASE_URL=sqlite:///./data/dashboard.db

# Ollama Configuration (for embeddings)
OLLAMA_URL=http://localhost:11434
OLLAMA_MODEL=nomic-embed-text

# Development Configuration
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=info

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
CORS_ORIGINS=["http://localhost:3000", "http://localhost"]

# Frontend Configuration
VITE_API_URL=http://localhost:8000
VITE_WS_URL=ws://localhost:8000

# Performance Configuration
MAX_WORKERS=4
CONNECTION_POOL_SIZE=20
REQUEST_TIMEOUT=30

# Animation Configuration
ANIMATION_DURATION_MS=300
SPRING_TENSION=400
SPRING_FRICTION=25
AI_MAX_RETRIES=3
AI_FALLBACK_MODEL=google/gemini-2.0-flash-exp:free

# Security Configuration
BCRYPT_SALT_ROUNDS=12
SESSION_COOKIE_MAX_AGE=604800000