"""
Security Integration Testing Suite
Task 40: Security validation and vulnerability testing

PATTERN: Comprehensive security testing for production readiness
Features:
- Authentication and authorization testing
- Input validation and sanitization verification
- SQL injection and XSS prevention testing
- Rate limiting and DDoS protection validation
- Security headers verification
- API endpoint security testing
"""

import asyncio
import json
import time
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

import pytest
import pytest_asyncio
from httpx import AsyncClient
import websockets
from concurrent.futures import ThreadPoolExecutor

from backend.app.main import app
from backend.app.config.settings import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()

class SecurityTestSuite:
    """Comprehensive security testing suite"""
    
    def __init__(self):
        self.api_client: AsyncClient = None
        self.test_results: Dict[str, Any] = {}
    
    async def setup(self):
        """Setup security test environment"""
        self.api_client = AsyncClient(app=app, base_url="http://testserver")
        logger.info("Security test setup completed")
    
    async def teardown(self):
        """Cleanup security test environment"""
        if self.api_client:
            await self.api_client.aclose()
        logger.info("Security test teardown completed")
    
    async def test_input_validation_security(self) -> Dict[str, Any]:
        """Test input validation and sanitization"""
        results = {
            "test_name": "input_validation_security",
            "start_time": datetime.utcnow().isoformat(),
            "tests": [],
            "success": False,
            "vulnerabilities": []
        }
        
        # SQL Injection test cases
        sql_injection_payloads = [
            "'; DROP TABLE users; --",
            "1' OR '1'='1",
            "'; INSERT INTO users (name) VALUES ('hacked'); --",
            "1' UNION SELECT password FROM users --",
            "admin'/*",
            "' OR 1=1#",
            "'; EXEC xp_cmdshell('dir'); --"
        ]
        
        # XSS test cases
        xss_payloads = [
            "<script>alert('XSS')</script>",
            "javascript:alert('XSS')",
            "<img src=x onerror=alert('XSS')>",
            "<iframe src='javascript:alert(`XSS`)'></iframe>",
            "<svg onload=alert('XSS')>",
            "{{constructor.constructor('alert(\"XSS\")')()}}",
            "${alert('XSS')}"
        ]
        
        # Command Injection test cases
        command_injection_payloads = [
            "; cat /etc/passwd",
            "| ls -la",
            "&& rm -rf /",
            "`whoami`",
            "$(id)",
            "; ping -c 10 127.0.0.1",
            "& net user"
        ]
        
        test_cases = [
            ("SQL Injection", sql_injection_payloads),
            ("XSS", xss_payloads),
            ("Command Injection", command_injection_payloads)
        ]
        
        passed_tests = 0
        total_tests = 0
        
        for attack_type, payloads in test_cases:
            for payload in payloads:
                total_tests += 1
                test_result = {
                    "attack_type": attack_type,
                    "payload": payload,
                    "blocked": False,
                    "response_code": None,
                    "error_message": None
                }
                
                try:
                    # Test API endpoint
                    response = await self.api_client.post(
                        "/api/process",
                        json={"input": payload, "mode": "auto"},
                        timeout=5.0
                    )
                    
                    test_result["response_code"] = response.status_code
                    
                    # Check if input was properly sanitized/blocked
                    if response.status_code == 400:  # Bad request due to validation
                        test_result["blocked"] = True
                        passed_tests += 1
                    elif response.status_code == 200:
                        response_data = response.json()
                        # Check if payload was sanitized in response
                        if payload not in str(response_data):
                            test_result["blocked"] = True
                            passed_tests += 1
                        else:
                            results["vulnerabilities"].append({
                                "type": attack_type,
                                "payload": payload,
                                "severity": "HIGH",
                                "description": "Malicious payload not sanitized"
                            })
                    
                except Exception as e:
                    test_result["error_message"] = str(e)
                    # Timeout or error can indicate good security (blocked)
                    if "timeout" in str(e).lower():
                        test_result["blocked"] = True
                        passed_tests += 1
                
                results["tests"].append(test_result)
        
        results["success"] = (passed_tests / total_tests) > 0.9  # 90% should be blocked
        results["total_tests"] = total_tests
        results["passed_tests"] = passed_tests
        results["success_rate"] = (passed_tests / total_tests) * 100
        results["end_time"] = datetime.utcnow().isoformat()
        
        return results
    
    async def test_rate_limiting(self) -> Dict[str, Any]:
        """Test rate limiting and DDoS protection"""
        results = {
            "test_name": "rate_limiting",
            "start_time": datetime.utcnow().isoformat(),
            "tests": [],
            "success": False
        }
        
        # Test rapid requests
        rapid_requests_test = {
            "test_type": "rapid_requests",
            "requests_sent": 0,
            "requests_blocked": 0,
            "success": False
        }
        
        try:
            # Send 100 requests rapidly
            tasks = []
            for i in range(100):
                task = asyncio.create_task(
                    self.api_client.post(
                        "/api/process",
                        json={"input": f"Test request {i}", "mode": "auto"},
                        timeout=2.0
                    )
                )
                tasks.append(task)
            
            responses = await asyncio.gather(*tasks, return_exceptions=True)
            
            for response in responses:
                rapid_requests_test["requests_sent"] += 1
                
                if isinstance(response, Exception):
                    rapid_requests_test["requests_blocked"] += 1
                elif hasattr(response, 'status_code') and response.status_code == 429:
                    rapid_requests_test["requests_blocked"] += 1
            
            # Should block at least 80% of rapid requests
            block_rate = rapid_requests_test["requests_blocked"] / rapid_requests_test["requests_sent"]
            rapid_requests_test["success"] = block_rate > 0.8
            rapid_requests_test["block_rate"] = block_rate * 100
            
        except Exception as e:
            rapid_requests_test["error"] = str(e)
        
        results["tests"].append(rapid_requests_test)
        
        # Test concurrent connections
        concurrent_test = await self._test_concurrent_connections()
        results["tests"].append(concurrent_test)
        
        results["success"] = all(test["success"] for test in results["tests"])
        results["end_time"] = datetime.utcnow().isoformat()
        
        return results
    
    async def _test_concurrent_connections(self) -> Dict[str, Any]:
        """Test concurrent connection limits"""
        test_result = {
            "test_type": "concurrent_connections",
            "max_concurrent": 50,
            "connections_established": 0,
            "connections_rejected": 0,
            "success": False
        }
        
        async def create_connection():
            try:
                async with AsyncClient(app=app, base_url="http://testserver") as client:
                    response = await client.get("/health", timeout=5.0)
                    return response.status_code == 200
            except:
                return False
        
        try:
            # Try to establish many concurrent connections
            tasks = [create_connection() for _ in range(test_result["max_concurrent"])]
            results_list = await asyncio.gather(*tasks)
            
            test_result["connections_established"] = sum(results_list)
            test_result["connections_rejected"] = len(results_list) - sum(results_list)
            
            # Some connections should be rejected under load
            rejection_rate = test_result["connections_rejected"] / test_result["max_concurrent"]
            test_result["success"] = rejection_rate > 0.2  # At least 20% should be rejected
            test_result["rejection_rate"] = rejection_rate * 100
            
        except Exception as e:
            test_result["error"] = str(e)
        
        return test_result
    
    async def test_security_headers(self) -> Dict[str, Any]:
        """Test security headers implementation"""
        results = {
            "test_name": "security_headers",
            "start_time": datetime.utcnow().isoformat(),
            "headers_tested": [],
            "success": False
        }
        
        required_headers = {
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": ["DENY", "SAMEORIGIN"],
            "X-XSS-Protection": "1; mode=block",
            "Strict-Transport-Security": None,  # Should exist
            "Content-Security-Policy": None,    # Should exist
            "Referrer-Policy": None,           # Should exist
            "Permissions-Policy": None         # Should exist
        }
        
        try:
            response = await self.api_client.get("/")
            headers = response.headers
            
            missing_headers = []
            incorrect_headers = []
            
            for header_name, expected_value in required_headers.items():
                header_test = {
                    "header": header_name,
                    "present": header_name in headers,
                    "value": headers.get(header_name),
                    "correct": False
                }
                
                if header_name in headers:
                    if expected_value is None:
                        # Just check presence
                        header_test["correct"] = True
                    elif isinstance(expected_value, list):
                        # Check if value is in allowed list
                        header_test["correct"] = headers[header_name] in expected_value
                    else:
                        # Check exact value
                        header_test["correct"] = headers[header_name] == expected_value
                else:
                    missing_headers.append(header_name)
                
                if not header_test["correct"]:
                    incorrect_headers.append(header_name)
                
                results["headers_tested"].append(header_test)
            
            results["missing_headers"] = missing_headers
            results["incorrect_headers"] = incorrect_headers
            results["success"] = len(missing_headers) == 0 and len(incorrect_headers) == 0
            
        except Exception as e:
            results["error"] = str(e)
        
        results["end_time"] = datetime.utcnow().isoformat()
        return results
    
    async def test_authentication_security(self) -> Dict[str, Any]:
        """Test authentication and session security"""
        results = {
            "test_name": "authentication_security",
            "start_time": datetime.utcnow().isoformat(),
            "tests": [],
            "success": False
        }
        
        # Test password policies (if applicable)
        password_test = await self._test_password_policies()
        results["tests"].append(password_test)
        
        # Test session management
        session_test = await self._test_session_security()
        results["tests"].append(session_test)
        
        # Test authorization bypass attempts
        authz_test = await self._test_authorization_bypass()
        results["tests"].append(authz_test)
        
        results["success"] = all(test.get("success", False) for test in results["tests"])
        results["end_time"] = datetime.utcnow().isoformat()
        
        return results
    
    async def _test_password_policies(self) -> Dict[str, Any]:
        """Test password policy enforcement"""
        return {
            "test_type": "password_policies",
            "success": True,  # Placeholder - implement based on auth system
            "note": "No authentication system implemented yet"
        }
    
    async def _test_session_security(self) -> Dict[str, Any]:
        """Test session security measures"""
        return {
            "test_type": "session_security",
            "success": True,  # Placeholder - implement based on auth system
            "note": "No session management implemented yet"
        }
    
    async def _test_authorization_bypass(self) -> Dict[str, Any]:
        """Test authorization bypass attempts"""
        test_result = {
            "test_type": "authorization_bypass",
            "attempts": [],
            "success": True
        }
        
        # Test direct access to admin endpoints
        admin_endpoints = [
            "/admin",
            "/api/admin/users",
            "/api/admin/system",
            "/../admin",
            "/api/../admin"
        ]
        
        for endpoint in admin_endpoints:
            try:
                response = await self.api_client.get(endpoint, timeout=5.0)
                test_result["attempts"].append({
                    "endpoint": endpoint,
                    "status_code": response.status_code,
                    "blocked": response.status_code in [401, 403, 404]
                })
            except Exception as e:
                test_result["attempts"].append({
                    "endpoint": endpoint,
                    "error": str(e),
                    "blocked": True
                })
        
        # All attempts should be blocked
        test_result["success"] = all(attempt.get("blocked", False) for attempt in test_result["attempts"])
        
        return test_result
    
    async def test_api_security(self) -> Dict[str, Any]:
        """Test API endpoint security"""
        results = {
            "test_name": "api_security",
            "start_time": datetime.utcnow().isoformat(),
            "endpoints_tested": [],
            "success": False
        }
        
        # Test endpoints with malicious payloads
        api_endpoints = [
            ("POST", "/api/process", {"input": "test", "mode": "auto"}),
            ("GET", "/api/tasks", {}),
            ("GET", "/api/calendar/events", {}),
            ("GET", "/health", {})
        ]
        
        security_tests = [
            # Test with oversized payload
            {"input": "A" * 10000, "mode": "auto"},
            # Test with null bytes
            {"input": "test\x00malicious", "mode": "auto"},
            # Test with unicode attacks
            {"input": "test\u202emoc.evil", "mode": "auto"},
            # Test with JSON bombs
            {"input": {"nested": {"very": {"deep": "structure" * 100}}}, "mode": "auto"}
        ]
        
        passed_tests = 0
        total_tests = 0
        
        for method, endpoint, base_payload in api_endpoints:
            if method == "POST":
                for test_payload in security_tests:
                    total_tests += 1
                    test_result = {
                        "endpoint": endpoint,
                        "method": method,
                        "payload_type": str(type(test_payload).__name__),
                        "blocked": False,
                        "status_code": None
                    }
                    
                    try:
                        response = await self.api_client.post(
                            endpoint,
                            json=test_payload,
                            timeout=5.0
                        )
                        test_result["status_code"] = response.status_code
                        
                        # Should be blocked (400, 422, or similar)
                        if response.status_code >= 400:
                            test_result["blocked"] = True
                            passed_tests += 1
                        
                    except Exception as e:
                        # Timeout or error indicates good security
                        test_result["blocked"] = True
                        test_result["error"] = str(e)
                        passed_tests += 1
                    
                    results["endpoints_tested"].append(test_result)
        
        results["total_tests"] = total_tests
        results["passed_tests"] = passed_tests
        results["success_rate"] = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        results["success"] = (passed_tests / total_tests) > 0.8 if total_tests > 0 else True
        results["end_time"] = datetime.utcnow().isoformat()
        
        return results
    
    async def run_security_test_suite(self) -> Dict[str, Any]:
        """Run the complete security test suite"""
        suite_start = time.time()
        
        suite_results = {
            "suite_name": "comprehensive_security_testing",
            "start_time": datetime.utcnow().isoformat(),
            "tests": [],
            "overall_success": False,
            "security_score": 0,
            "vulnerabilities": []
        }
        
        # Run all security tests
        test_methods = [
            self.test_input_validation_security,
            self.test_rate_limiting,
            self.test_security_headers,
            self.test_authentication_security,
            self.test_api_security
        ]
        
        successful_tests = 0
        total_vulnerabilities = []
        
        for test_method in test_methods:
            try:
                logger.info(f"Running security test: {test_method.__name__}...")
                test_result = await test_method()
                suite_results["tests"].append(test_result)
                
                if test_result.get("success", False):
                    successful_tests += 1
                
                # Collect vulnerabilities
                if "vulnerabilities" in test_result:
                    total_vulnerabilities.extend(test_result["vulnerabilities"])
                    
            except Exception as e:
                logger.error(f"Security test {test_method.__name__} failed: {e}")
                suite_results["tests"].append({
                    "test_name": test_method.__name__,
                    "success": False,
                    "error": str(e)
                })
        
        # Calculate security score
        base_score = (successful_tests / len(test_methods)) * 100
        
        # Reduce score for vulnerabilities
        high_vulns = len([v for v in total_vulnerabilities if v.get("severity") == "HIGH"])
        medium_vulns = len([v for v in total_vulnerabilities if v.get("severity") == "MEDIUM"])
        low_vulns = len([v for v in total_vulnerabilities if v.get("severity") == "LOW"])
        
        vulnerability_penalty = (high_vulns * 20) + (medium_vulns * 10) + (low_vulns * 5)
        security_score = max(0, base_score - vulnerability_penalty)
        
        suite_results["security_score"] = security_score
        suite_results["vulnerabilities"] = total_vulnerabilities
        suite_results["vulnerability_summary"] = {
            "high": high_vulns,
            "medium": medium_vulns,
            "low": low_vulns,
            "total": len(total_vulnerabilities)
        }
        
        suite_results["overall_success"] = security_score >= 85  # 85% security score required
        suite_results["total_duration"] = time.time() - suite_start
        suite_results["end_time"] = datetime.utcnow().isoformat()
        
        return suite_results

# Security test runner
async def main():
    """Main security test runner"""
    suite = SecurityTestSuite()
    
    try:
        await suite.setup()
        results = await suite.run_security_test_suite()
        
        # Save results
        timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
        results_file = f"security_test_results_{timestamp}.json"
        
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        logger.info(f"Security test results saved to {results_file}")
        logger.info(f"Security Score: {results['security_score']:.1f}/100")
        logger.info(f"Overall Success: {results['overall_success']}")
        
        if results['vulnerabilities']:
            logger.warning(f"Found {len(results['vulnerabilities'])} vulnerabilities")
            for vuln in results['vulnerabilities']:
                logger.warning(f"- {vuln['severity']}: {vuln['description']}")
        
        return results
        
    finally:
        await suite.teardown()

if __name__ == "__main__":
    asyncio.run(main())
