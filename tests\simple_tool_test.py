"""
Simple test script to verify tool integration is working.

Run this from the project root with:
python tests/simple_tool_test.py
"""

import sys
import os
from pathlib import Path

def main():
    print("🚀 Testing AI-Powered Dashboard Tool Integration...")
    print("=" * 60)
    
    # Add backend to Python path
    project_root = Path(__file__).parent.parent
    backend_path = project_root / "backend"
    sys.path.insert(0, str(backend_path))
    
    print(f"📁 Project Root: {project_root}")
    print(f"📁 Backend Path: {backend_path}")
    
    # Test 1: Environment file exists
    print("\n1️⃣ Checking .env file...")
    env_file = project_root / ".env"
    if env_file.exists():
        print(f"✅ .env file found at: {env_file}")
    else:
        print(f"❌ .env file not found at: {env_file}")
        return False
    
    # Test 2: Load environment variables
    print("\n2️⃣ Loading environment variables...")
    try:
        import dotenv
        dotenv.load_dotenv(env_file)
        
        openrouter_key = os.getenv("OPENROUTER_API_KEY")
        langsearch_key = os.getenv("LANGSEARCH_API_KEY")
        
        if openrouter_key:
            print(f"✅ OPENROUTER_API_KEY loaded: {openrouter_key[:15]}...")
        else:
            print("❌ OPENROUTER_API_KEY not found")
            return False
            
        if langsearch_key:
            print(f"✅ LANGSEARCH_API_KEY loaded: {langsearch_key[:15]}...")
        else:
            print("❌ LANGSEARCH_API_KEY not found")
            return False
            
    except ImportError:
        print("❌ python-dotenv not installed. Install with: pip install python-dotenv")
        return False
    except Exception as e:
        print(f"❌ Error loading environment: {e}")
        return False
    
    # Test 3: Import settings
    print("\n3️⃣ Testing settings import...")
    try:
        from app.config.settings import get_settings
        settings = get_settings()
        print(f"✅ Settings loaded successfully!")
        print(f"   Environment: {settings.environment}")
        print(f"   API Port: {settings.api_port}")
        print(f"   Debug: {settings.debug}")
    except Exception as e:
        print(f"❌ Failed to import settings: {e}")
        return False
    
    # Test 4: Import tool classes
    print("\n4️⃣ Testing tool imports...")
    try:
        from app.tools.task_tool import TaskTool, CreateTaskInput
        from app.tools.calendar_tool import CalendarTool, CreateEventInput
        from app.tools.web_search_tool import WebSearchTool, WebSearchInput
        from app.tools.database_search_tool import HybridDatabaseSearchTool, HybridSearchInput
        
        print("✅ All tool classes imported successfully!")
        print(f"   TaskTool: {TaskTool}")
        print(f"   CalendarTool: {CalendarTool}")
        print(f"   WebSearchTool: {WebSearchTool}")
        print(f"   HybridDatabaseSearchTool: {HybridDatabaseSearchTool}")
    except Exception as e:
        print(f"❌ Failed to import tools: {e}")
        return False
    
    # Test 5: Create tool instances
    print("\n5️⃣ Testing tool instantiation...")
    try:
        task_tool = TaskTool()
        calendar_tool = CalendarTool()
        web_search_tool = WebSearchTool()
        database_search_tool = HybridDatabaseSearchTool()
        
        print("✅ All tools instantiated successfully!")
        print(f"   Task Tool: {type(task_tool).__name__}")
        print(f"   Calendar Tool: {type(calendar_tool).__name__}")
        print(f"   Web Search Tool: {type(web_search_tool).__name__}")
        print(f"   Database Search Tool: {type(database_search_tool).__name__}")
    except Exception as e:
        print(f"❌ Failed to instantiate tools: {e}")
        return False
    
    # Test 6: Create input models
    print("\n6️⃣ Testing input models...")
    try:
        # Test task input
        task_input = CreateTaskInput(
            title="Test Task from Integration Test",
            description="This validates that CreateTaskInput works"
        )
        print(f"✅ CreateTaskInput: {task_input.title}")
        
        # Test event input
        from datetime import datetime, timedelta
        start_time = datetime.now() + timedelta(days=1)  # Tomorrow
        end_time = start_time + timedelta(hours=1)  # 1 hour duration
        
        event_input = CreateEventInput(
            title="Test Event from Integration Test",
            description="This validates that CreateEventInput works",
            start_datetime=start_time,
            end_datetime=end_time
        )
        print(f"✅ CreateEventInput: {event_input.title}")
        
        # Test web search input
        web_input = WebSearchInput(
            query="AI-powered dashboard integration test",
            freshness="oneWeek",
            max_results=5
        )
        print(f"✅ WebSearchInput: {web_input.query}")
        
        # Test database search input
        db_input = HybridSearchInput(
            query="test database integration",
            search_type="all",  # Valid values: 'tasks', 'events', 'inputs', 'all'
            search_mode="hybrid",  # This is where "hybrid" goes
            limit=10
        )
        print(f"✅ HybridSearchInput: {db_input.query}")
        
    except Exception as e:
        print(f"❌ Failed to create input models: {e}")
        return False
    
    # Test 7: Import main module (the real test!)
    print("\n7️⃣ Testing main module import (CRITICAL TEST)...")
    try:
        from app.main import task_tool, calendar_tool, web_search_tool, database_search_tool
        
        print("🎉 MAIN MODULE IMPORTED SUCCESSFULLY!")
        print("✅ All tools from main.py are working:")
        print(f"   task_tool: {type(task_tool).__name__}")
        print(f"   calendar_tool: {type(calendar_tool).__name__}")
        print(f"   web_search_tool: {type(web_search_tool).__name__}")
        print(f"   database_search_tool: {type(database_search_tool).__name__}")
        
    except Exception as e:
        print(f"❌ CRITICAL FAILURE - Cannot import main module: {e}")
        print("\nThis means the backend cannot start properly!")
        print("Check the error details above to fix the issue.")
        return False
    
    # All tests passed!
    print("\n" + "=" * 60)
    print("🎉 ALL INTEGRATION TESTS PASSED!")
    print("✅ Backend tool integration is working correctly!")
    print("✅ Environment configuration is properly loaded!")
    print("✅ All tools are ready for real operations!")
    print("✅ The audit fix for tool integration is COMPLETE!")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n💡 Fix the errors above and run the test again.")
        sys.exit(1)
    else:
        print("\n🚀 Ready to proceed with the next development tasks!")
        sys.exit(0)
