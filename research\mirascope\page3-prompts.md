Title: Prompts | Mirascope

URL Source: https://mirascope.com/docs/mirascope/learn/prompts

Markdown Content:
API Documentation

When working with Large Language Model (LLM) APIs, the "prompt" is generally a list of messages where each message has a particular role. These prompts are the foundation of effectively working with LLMs, so Mirascope provides powerful tools to help you create, manage, and optimize your prompts for various LLM interactions.

Let's look at how we can write prompts using Mirascope in a reusable, modular, and provider-agnostic way.

Calls will come later

For the following explanations we will be talking _only_ about the messages aspect of prompt engineering and will discuss calling the API later in the [Calls](https://mirascope.com/docs/mirascope/learn/calls) documentation.

In that section we will show how to use these provider-agnostic prompts to actually call a provider's API as well as how to engineer and tie a prompt to a specific call.

Prompt Templates (Messages)[](https://mirascope.com/docs/mirascope/learn/prompts#prompt-templates-messages)
-----------------------------------------------------------------------------------------------------------

First, let's look at a basic example:

In this example:

1.   The `recommend_book_prompt` method's signature defines the prompt's template variables.
2.   Calling the method with `genre="fantasy"` returns a list with the corresponding `BaseMessageParam` instance with role `user` and content "Recommend a fantasy book".

The core concept to understand here is `BaseMessageParam`. This class operates as the base class for message parameters that Mirascope can handle and use across all supported providers.

In Mirascope, we use the `@prompt_template` decorator to write prompt templates as reusable methods that return the corresponding list of `BaseMessageParam` instances.

There are two common ways of writing Mirascope prompt functions:

1.   _(Shorthand)_ Returning the `str` or `list` content for a single user message, or returning `Messages.{Role}` (individually or a list) when specific roles are needed.
2.   _(String Template)_ Passing a string template to `@prompt_template` that gets parsed and then formatted like a normal Python formatted string.

Which method you use is mostly up to your preference, so feel free to select which one you prefer in the following sections.

Message Roles[](https://mirascope.com/docs/mirascope/learn/prompts#message-roles)
---------------------------------------------------------------------------------

We can also define additional messages with different roles, such as a system message:

Messages.Type

The return type `Messages.Type` accepts all shorthand methods as well as `BaseMessageParam` types. Since the message methods (e.g. `Messages.User`) return `BaseMessageParam` instances, we generally recommend always typing your prompt templates with the `Messages.Type` return type since it covers all prompt template writing methods.

Supported Roles

Mirascope prompt templates currently support the `system`, `user`, and `assistant` roles. When using string templates, the roles are parsed by their corresponding all caps keyword (e.g. SYSTEM).

For messages with the `tool` role, see how Mirascope automatically generates these messages for you in the [Tools](https://mirascope.com/docs/mirascope/learn/tools) and [Agents](https://mirascope.com/docs/mirascope/learn/agents) sections.

Multi-Line Prompts[](https://mirascope.com/docs/mirascope/learn/prompts#multi-line-prompts)
-------------------------------------------------------------------------------------------

When writing prompts that span multiple lines, it's important to ensure you don't accidentally include additional, unnecessary tokens (namely `\t` tokens):

In this example, we use `inspect.cleandoc` to remove unnecessary tokens while maintaining proper formatting in our codebase.

Multi-Line String Templates

Multi-Modal Inputs[](https://mirascope.com/docs/mirascope/learn/prompts#multi-modal-inputs)
-------------------------------------------------------------------------------------------

Recent advancements in Large Language Model architecture has enabled many model providers to support multi-modal inputs (text, images, audio, etc.) for a single endpoint. Mirascope treats these input types as first-class and supports them natively.

While Mirascope provides a consistent interface, support varies among providers:

| Type | Anthropic | Cohere | Google | Groq | Mistral | OpenAI |
| --- | --- | --- | --- | --- | --- | --- |
| text | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ |
| image | ✓ | — | ✓ | ✓ | ✓ | ✓ |
| audio | — | — | ✓ | — | — | ✓ |
| video | — | — | ✓ | — | — | — |
| document | ✓ | — | ✓ | — | — | — |

_Legend: ✓ (Supported), — (Not Supported)_

### Image Inputs[](https://mirascope.com/docs/mirascope/learn/prompts#image-inputs)

Additional String Template Image Functionality

### Audio Inputs[](https://mirascope.com/docs/mirascope/learn/prompts#audio-inputs)

Additional String Template Audio Functionality

### Document Inputs[](https://mirascope.com/docs/mirascope/learn/prompts#document-inputs)

Supported Document Types

Additional String Template Document Functionality

Chat History[](https://mirascope.com/docs/mirascope/learn/prompts#chat-history)
-------------------------------------------------------------------------------

Often you'll want to inject messages (such as previous chat messages) into the prompt. Generally you can just unroll the messages into the return value of your prompt template. When using string templates, we provide a `MESSAGES` keyword for this injection, which you can add in whatever position and as many times as you'd like:

Object Attribute Access[](https://mirascope.com/docs/mirascope/learn/prompts#object-attribute-access)
-----------------------------------------------------------------------------------------------------

When using template variables that have attributes, you can easily inject these attributes directly even when using string templates:

It's worth noting that this also works with `self` when using prompt templates inside of a class, which is particularly important when building [Agents](https://mirascope.com/docs/mirascope/learn/agents).

Format Specifiers[](https://mirascope.com/docs/mirascope/learn/prompts#format-specifiers)
-----------------------------------------------------------------------------------------

Since Mirascope prompt templates are just formatted strings, standard Python format specifiers work as expected:

When writing string templates, we also offer additional format specifiers for convenience around formatting more dynamic content:

### Lists[](https://mirascope.com/docs/mirascope/learn/prompts#lists)

String templates support the `:list` format specifier for formatting lists:

Computed Fields (Dynamic Configuration)[](https://mirascope.com/docs/mirascope/learn/prompts#computed-fields-dynamic-configuration)
-----------------------------------------------------------------------------------------------------------------------------------

In Mirascope, we write prompt templates as functions, which enables dynamically configuring our prompts at runtime depending on the values of the template variables. We use the term "computed fields" to talk about variables that are computed and formatted at runtime.

There are various other parts of an LLM API call that we may want to configure dynamically as well, such as call parameters, tools, and more. We cover such cases in each of their respective sections.

Next Steps[](https://mirascope.com/docs/mirascope/learn/prompts#next-steps)
---------------------------------------------------------------------------

By mastering prompts in Mirascope, you'll be well-equipped to build robust, flexible, and reusable LLM applications.

Next, we recommend taking a look at the [Calls](https://mirascope.com/docs/mirascope/learn/calls) documentation, which shows you how to use your prompt templates to actually call LLM APIs and generate a response.
