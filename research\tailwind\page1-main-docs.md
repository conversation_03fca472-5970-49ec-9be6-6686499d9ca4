Title: Installing with Vite - Installation

URL Source: https://tailwindcss.com/docs

Markdown Content:
Installing Tailwind CSS with Vite - Tailwind CSS

===============

[](https://tailwindcss.com/)v4.1

⌘K Ctrl K[Docs](https://tailwindcss.com/docs)[Blog](https://tailwindcss.com/blog)[Showcase](https://tailwindcss.com/showcase)[Sponsor](https://tailwindcss.com/sponsor)[Plus](https://tailwindcss.com/plus?ref=top)[](https://github.com/tailwindlabs/tailwindcss)

1.   Getting Started
2.   Using Vite

*   [Documentation](https://tailwindcss.com/docs/installation)
*   [Components](https://tailwindcss.com/plus/ui-blocks?ref=sidebar)
*   [Templates](https://tailwindcss.com/plus/templates?ref=sidebar)
*   [UI Kit](https://tailwindcss.com/plus/ui-kit?ref=sidebar)
*   [Playground](https://play.tailwindcss.com/)
*   [Community New](https://tailwindcss.com/sponsor#insiders)

### Getting started

*   [Installation](https://tailwindcss.com/docs/installation)
*   [Editor setup](https://tailwindcss.com/docs/editor-setup)
*   [Compatibility](https://tailwindcss.com/docs/compatibility)
*   [Upgrade guide](https://tailwindcss.com/docs/upgrade-guide)

### Core concepts

*   [Styling with utility classes](https://tailwindcss.com/docs/styling-with-utility-classes)
*   [Hover, focus, and other states](https://tailwindcss.com/docs/hover-focus-and-other-states)
*   [Responsive design](https://tailwindcss.com/docs/responsive-design)
*   [Dark mode](https://tailwindcss.com/docs/dark-mode)
*   [Theme variables](https://tailwindcss.com/docs/theme)
*   [Colors](https://tailwindcss.com/docs/colors)
*   [Adding custom styles](https://tailwindcss.com/docs/adding-custom-styles)
*   [Detecting classes in source files](https://tailwindcss.com/docs/detecting-classes-in-source-files)
*   [Functions and directives](https://tailwindcss.com/docs/functions-and-directives)

### Base styles

*   [Preflight](https://tailwindcss.com/docs/preflight)

### Layout

*   [aspect-ratio](https://tailwindcss.com/docs/aspect-ratio)
*   [columns](https://tailwindcss.com/docs/columns)
*   [break-after](https://tailwindcss.com/docs/break-after)
*   [break-before](https://tailwindcss.com/docs/break-before)
*   [break-inside](https://tailwindcss.com/docs/break-inside)
*   [box-decoration-break](https://tailwindcss.com/docs/box-decoration-break)
*   [box-sizing](https://tailwindcss.com/docs/box-sizing)
*   [display](https://tailwindcss.com/docs/display)
*   [float](https://tailwindcss.com/docs/float)
*   [clear](https://tailwindcss.com/docs/clear)
*   [isolation](https://tailwindcss.com/docs/isolation)
*   [object-fit](https://tailwindcss.com/docs/object-fit)
*   [object-position](https://tailwindcss.com/docs/object-position)
*   [overflow](https://tailwindcss.com/docs/overflow)
*   [overscroll-behavior](https://tailwindcss.com/docs/overscroll-behavior)
*   [position](https://tailwindcss.com/docs/position)
*   [top / right / bottom / left](https://tailwindcss.com/docs/top-right-bottom-left)
*   [visibility](https://tailwindcss.com/docs/visibility)
*   [z-index](https://tailwindcss.com/docs/z-index)

### Flexbox & Grid

*   [flex-basis](https://tailwindcss.com/docs/flex-basis)
*   [flex-direction](https://tailwindcss.com/docs/flex-direction)
*   [flex-wrap](https://tailwindcss.com/docs/flex-wrap)
*   [flex](https://tailwindcss.com/docs/flex)
*   [flex-grow](https://tailwindcss.com/docs/flex-grow)
*   [flex-shrink](https://tailwindcss.com/docs/flex-shrink)
*   [order](https://tailwindcss.com/docs/order)
*   [grid-template-columns](https://tailwindcss.com/docs/grid-template-columns)
*   [grid-column](https://tailwindcss.com/docs/grid-column)
*   [grid-template-rows](https://tailwindcss.com/docs/grid-template-rows)
*   [grid-row](https://tailwindcss.com/docs/grid-row)
*   [grid-auto-flow](https://tailwindcss.com/docs/grid-auto-flow)
*   [grid-auto-columns](https://tailwindcss.com/docs/grid-auto-columns)
*   [grid-auto-rows](https://tailwindcss.com/docs/grid-auto-rows)
*   [gap](https://tailwindcss.com/docs/gap)
*   [justify-content](https://tailwindcss.com/docs/justify-content)
*   [justify-items](https://tailwindcss.com/docs/justify-items)
*   [justify-self](https://tailwindcss.com/docs/justify-self)
*   [align-content](https://tailwindcss.com/docs/align-content)
*   [align-items](https://tailwindcss.com/docs/align-items)
*   [align-self](https://tailwindcss.com/docs/align-self)
*   [place-content](https://tailwindcss.com/docs/place-content)
*   [place-items](https://tailwindcss.com/docs/place-items)
*   [place-self](https://tailwindcss.com/docs/place-self)

### Spacing

*   [padding](https://tailwindcss.com/docs/padding)
*   [margin](https://tailwindcss.com/docs/margin)

### Sizing

*   [width](https://tailwindcss.com/docs/width)
*   [min-width](https://tailwindcss.com/docs/min-width)
*   [max-width](https://tailwindcss.com/docs/max-width)
*   [height](https://tailwindcss.com/docs/height)
*   [min-height](https://tailwindcss.com/docs/min-height)
*   [max-height](https://tailwindcss.com/docs/max-height)

### Typography

*   [font-family](https://tailwindcss.com/docs/font-family)
*   [font-size](https://tailwindcss.com/docs/font-size)
*   [font-smoothing](https://tailwindcss.com/docs/font-smoothing)
*   [font-style](https://tailwindcss.com/docs/font-style)
*   [font-weight](https://tailwindcss.com/docs/font-weight)
*   [font-stretch](https://tailwindcss.com/docs/font-stretch)
*   [font-variant-numeric](https://tailwindcss.com/docs/font-variant-numeric)
*   [letter-spacing](https://tailwindcss.com/docs/letter-spacing)
*   [line-clamp](https://tailwindcss.com/docs/line-clamp)
*   [line-height](https://tailwindcss.com/docs/line-height)
*   [list-style-image](https://tailwindcss.com/docs/list-style-image)
*   [list-style-position](https://tailwindcss.com/docs/list-style-position)
*   [list-style-type](https://tailwindcss.com/docs/list-style-type)
*   [text-align](https://tailwindcss.com/docs/text-align)
*   [color](https://tailwindcss.com/docs/color)
*   [text-decoration-line](https://tailwindcss.com/docs/text-decoration-line)
*   [text-decoration-color](https://tailwindcss.com/docs/text-decoration-color)
*   [text-decoration-style](https://tailwindcss.com/docs/text-decoration-style)
*   [text-decoration-thickness](https://tailwindcss.com/docs/text-decoration-thickness)
*   [text-underline-offset](https://tailwindcss.com/docs/text-underline-offset)
*   [text-transform](https://tailwindcss.com/docs/text-transform)
*   [text-overflow](https://tailwindcss.com/docs/text-overflow)
*   [text-wrap](https://tailwindcss.com/docs/text-wrap)
*   [text-indent](https://tailwindcss.com/docs/text-indent)
*   [vertical-align](https://tailwindcss.com/docs/vertical-align)
*   [white-space](https://tailwindcss.com/docs/white-space)
*   [word-break](https://tailwindcss.com/docs/word-break)
*   [overflow-wrap](https://tailwindcss.com/docs/overflow-wrap)
*   [hyphens](https://tailwindcss.com/docs/hyphens)
*   [content](https://tailwindcss.com/docs/content)

### Backgrounds

*   [background-attachment](https://tailwindcss.com/docs/background-attachment)
*   [background-clip](https://tailwindcss.com/docs/background-clip)
*   [background-color](https://tailwindcss.com/docs/background-color)
*   [background-image](https://tailwindcss.com/docs/background-image)
*   [background-origin](https://tailwindcss.com/docs/background-origin)
*   [background-position](https://tailwindcss.com/docs/background-position)
*   [background-repeat](https://tailwindcss.com/docs/background-repeat)
*   [background-size](https://tailwindcss.com/docs/background-size)

### Borders

*   [border-radius](https://tailwindcss.com/docs/border-radius)
*   [border-width](https://tailwindcss.com/docs/border-width)
*   [border-color](https://tailwindcss.com/docs/border-color)
*   [border-style](https://tailwindcss.com/docs/border-style)
*   [outline-width](https://tailwindcss.com/docs/outline-width)
*   [outline-color](https://tailwindcss.com/docs/outline-color)
*   [outline-style](https://tailwindcss.com/docs/outline-style)
*   [outline-offset](https://tailwindcss.com/docs/outline-offset)

### Effects

*   [box-shadow](https://tailwindcss.com/docs/box-shadow)
*   [text-shadow](https://tailwindcss.com/docs/text-shadow)
*   [opacity](https://tailwindcss.com/docs/opacity)
*   [mix-blend-mode](https://tailwindcss.com/docs/mix-blend-mode)
*   [background-blend-mode](https://tailwindcss.com/docs/background-blend-mode)
*   [mask-clip](https://tailwindcss.com/docs/mask-clip)
*   [mask-composite](https://tailwindcss.com/docs/mask-composite)
*   [mask-image](https://tailwindcss.com/docs/mask-image)
*   [mask-mode](https://tailwindcss.com/docs/mask-mode)
*   [mask-origin](https://tailwindcss.com/docs/mask-origin)
*   [mask-position](https://tailwindcss.com/docs/mask-position)
*   [mask-repeat](https://tailwindcss.com/docs/mask-repeat)
*   [mask-size](https://tailwindcss.com/docs/mask-size)
*   [mask-type](https://tailwindcss.com/docs/mask-type)

### Filters

*   [filter](https://tailwindcss.com/docs/filter)
    *   [blur](https://tailwindcss.com/docs/filter-blur)
    *   [brightness](https://tailwindcss.com/docs/filter-brightness)
    *   [contrast](https://tailwindcss.com/docs/filter-contrast)
    *   [drop-shadow](https://tailwindcss.com/docs/filter-drop-shadow)
    *   [grayscale](https://tailwindcss.com/docs/filter-grayscale)
    *   [hue-rotate](https://tailwindcss.com/docs/filter-hue-rotate)
    *   [invert](https://tailwindcss.com/docs/filter-invert)
    *   [saturate](https://tailwindcss.com/docs/filter-saturate)
    *   [sepia](https://tailwindcss.com/docs/filter-sepia)

*   [backdrop-filter](https://tailwindcss.com/docs/backdrop-filter)
    *   [blur](https://tailwindcss.com/docs/backdrop-filter-blur)
    *   [brightness](https://tailwindcss.com/docs/backdrop-filter-brightness)
    *   [contrast](https://tailwindcss.com/docs/backdrop-filter-contrast)
    *   [grayscale](https://tailwindcss.com/docs/backdrop-filter-grayscale)
    *   [hue-rotate](https://tailwindcss.com/docs/backdrop-filter-hue-rotate)
    *   [invert](https://tailwindcss.com/docs/backdrop-filter-invert)
    *   [opacity](https://tailwindcss.com/docs/backdrop-filter-opacity)
    *   [saturate](https://tailwindcss.com/docs/backdrop-filter-saturate)
    *   [sepia](https://tailwindcss.com/docs/backdrop-filter-sepia)

### Tables

*   [border-collapse](https://tailwindcss.com/docs/border-collapse)
*   [border-spacing](https://tailwindcss.com/docs/border-spacing)
*   [table-layout](https://tailwindcss.com/docs/table-layout)
*   [caption-side](https://tailwindcss.com/docs/caption-side)

### Transitions & Animation

*   [transition-property](https://tailwindcss.com/docs/transition-property)
*   [transition-behavior](https://tailwindcss.com/docs/transition-behavior)
*   [transition-duration](https://tailwindcss.com/docs/transition-duration)
*   [transition-timing-function](https://tailwindcss.com/docs/transition-timing-function)
*   [transition-delay](https://tailwindcss.com/docs/transition-delay)
*   [animation](https://tailwindcss.com/docs/animation)

### Transforms

*   [backface-visibility](https://tailwindcss.com/docs/backface-visibility)
*   [perspective](https://tailwindcss.com/docs/perspective)
*   [perspective-origin](https://tailwindcss.com/docs/perspective-origin)
*   [rotate](https://tailwindcss.com/docs/rotate)
*   [scale](https://tailwindcss.com/docs/scale)
*   [skew](https://tailwindcss.com/docs/skew)
*   [transform](https://tailwindcss.com/docs/transform)
*   [transform-origin](https://tailwindcss.com/docs/transform-origin)
*   [transform-style](https://tailwindcss.com/docs/transform-style)
*   [translate](https://tailwindcss.com/docs/translate)

### Interactivity

*   [accent-color](https://tailwindcss.com/docs/accent-color)
*   [appearance](https://tailwindcss.com/docs/appearance)
*   [caret-color](https://tailwindcss.com/docs/caret-color)
*   [color-scheme](https://tailwindcss.com/docs/color-scheme)
*   [cursor](https://tailwindcss.com/docs/cursor)
*   [field-sizing](https://tailwindcss.com/docs/field-sizing)
*   [pointer-events](https://tailwindcss.com/docs/pointer-events)
*   [resize](https://tailwindcss.com/docs/resize)
*   [scroll-behavior](https://tailwindcss.com/docs/scroll-behavior)
*   [scroll-margin](https://tailwindcss.com/docs/scroll-margin)
*   [scroll-padding](https://tailwindcss.com/docs/scroll-padding)
*   [scroll-snap-align](https://tailwindcss.com/docs/scroll-snap-align)
*   [scroll-snap-stop](https://tailwindcss.com/docs/scroll-snap-stop)
*   [scroll-snap-type](https://tailwindcss.com/docs/scroll-snap-type)
*   [touch-action](https://tailwindcss.com/docs/touch-action)
*   [user-select](https://tailwindcss.com/docs/user-select)
*   [will-change](https://tailwindcss.com/docs/will-change)

### SVG

*   [fill](https://tailwindcss.com/docs/fill)
*   [stroke](https://tailwindcss.com/docs/stroke)
*   [stroke-width](https://tailwindcss.com/docs/stroke-width)

### Accessibility

*   [forced-color-adjust](https://tailwindcss.com/docs/forced-color-adjust)

Installation

Get started with Tailwind CSS
=============================

Tailwind CSS works by scanning all of your HTML files, JavaScript components, and any other templates for class names, generating the corresponding styles and then writing them to a static CSS file.

It's fast, flexible, and reliable — with zero-runtime.

Installation
------------

*   [Using Vite](https://tailwindcss.com/docs/installation/using-vite)
------------------------------------------------------------------

*   [Using PostCSS](https://tailwindcss.com/docs/installation/using-postcss)
------------------------------------------------------------------------

*   [Tailwind CLI](https://tailwindcss.com/docs/installation/tailwind-cli)
----------------------------------------------------------------------

*   [Framework Guides](https://tailwindcss.com/docs/installation/framework-guides)
------------------------------------------------------------------------------

*   [Play CDN](https://tailwindcss.com/docs/installation/play-cdn)
--------------------------------------------------------------

### Installing Tailwind CSS as a Vite plugin

Installing Tailwind CSS as a Vite plugin is the most seamless way to integrate it with frameworks like Laravel, SvelteKit, React Router, Nuxt, and SolidJS.

01

#### Create your project

Start by creating a new Vite project if you don’t have one set up already. The most common approach is to use[Create Vite](https://vite.dev/guide/#scaffolding-your-first-vite-project).

Terminal

`npm create vite@latest my-projectcd my-project`

02

#### Install Tailwind CSS

Install `tailwindcss` and `@tailwindcss/vite` via npm.

Terminal

`npm install tailwindcss @tailwindcss/vite`

03

#### Configure the Vite plugin

Add the `@tailwindcss/vite` plugin to your Vite configuration.

vite.config.ts

`import { defineConfig } from 'vite'import tailwindcss from '@tailwindcss/vite'export default defineConfig({  plugins: [    tailwindcss(),  ],})`

04

#### Import Tailwind CSS

Add an `@import` to your CSS file that imports Tailwind CSS.

CSS

`@import "tailwindcss";`

05

#### Start your build process

Run your build process with `npm run dev` or whatever command is configured in your`package.json` file.

Terminal

`npm run dev`

06

#### Start using Tailwind in your HTML

Make sure your compiled CSS is included in the `<head>`_(your framework might handle this for you)_, then start using Tailwind’s utility classes to style your content.

HTML

`<!doctype html><html><head>  <meta charset="UTF-8">  <meta name="viewport" content="width=device-width, initial-scale=1.0">  <link href="/src/style.css" rel="stylesheet"></head><body>  <h1 class="text-3xl font-bold underline">    Hello world!  </h1></body></html>`

**Are you stuck?** Setting up Tailwind with Vite can be a bit different across different build tools. Check our framework guides to see if we have more specific instructions for your particular setup.

[Explore our framework guides](https://tailwindcss.com/docs/installation/framework-guides)

### Tailwind CSS

*   [Documentation](https://tailwindcss.com/docs)
*   [Playground](https://play.tailwindcss.com/)
*   [Blog](https://tailwindcss.com/blog)
*   [Showcase](https://tailwindcss.com/showcase)
*   [Sponsor](https://tailwindcss.com/sponsor)

### Resources

*   [Refactoring UI](https://www.refactoringui.com/)
*   [Headless UI](https://headlessui.com/)
*   [Heroicons](https://heroicons.com/)
*   [Hero Patterns](https://heropatterns.com/)

### [Tailwind Plus](https://tailwindcss.com/plus?ref=footer)

*   [UI Blocks](https://tailwindcss.com/plus/ui-blocks?ref=footer)
*   [Templates](https://tailwindcss.com/plus/templates?ref=footer)
*   [UI Kit](https://tailwindcss.com/plus/ui-kit?ref=footer)

### Community

*   [GitHub](https://github.com/tailwindlabs/tailwindcss)
*   [Discord](https://tailwindcss.com/sponsor#insiders)
*   [X](https://x.com/tailwindcss)

### Tailwind CSS

*   [Documentation](https://tailwindcss.com/docs)
*   [Playground](https://play.tailwindcss.com/)
*   [Blog](https://tailwindcss.com/blog)
*   [Showcase](https://tailwindcss.com/showcase)
*   [Sponsor](https://tailwindcss.com/sponsor)

### [Tailwind Plus](https://tailwindcss.com/plus?ref=footer)

*   [UI Blocks](https://tailwindcss.com/plus/ui-blocks?ref=footer)
*   [Templates](https://tailwindcss.com/plus/templates?ref=footer)
*   [UI Kit](https://tailwindcss.com/plus/ui-kit?ref=footer)

### Resources

*   [Refactoring UI](https://www.refactoringui.com/)
*   [Headless UI](https://headlessui.com/)
*   [Heroicons](https://heroicons.com/)
*   [Hero Patterns](https://heropatterns.com/)

### Community

*   [GitHub](https://github.com/tailwindlabs/tailwindcss)
*   [Discord](https://tailwindcss.com/sponsor#insiders)
*   [X](https://x.com/tailwindcss)

Copyright ©2025 Tailwind Labs Inc.·[Trademark Policy](https://tailwindcss.com/brand)
