# Task 31: E2E Testing - Complete Workflow
# Playwright browser automation for full user journey testing

import asyncio
import pytest
from playwright.async_api import async_playwright, <PERSON>, <PERSON><PERSON><PERSON>, BrowserContext
import time
from typing import Generator

class TestWorkflow:
    """Complete user workflow testing with Playwright."""

    @pytest.fixture(scope="session")
    async def browser(self) -> Generator[<PERSON><PERSON><PERSON>, None, None]:
        """Browser fixture for all tests."""
        async with async_playwright() as p:
            browser = await p.chromium.launch(
                headless=False,  # Set to True for CI/CD
                slow_mo=100,     # Slow down for demo purposes
            )
            yield browser
            await browser.close()

    @pytest.fixture
    async def context(self, browser: Browser) -> Generator[BrowserContext, None, None]:
        """Browser context with proper settings."""
        context = await browser.new_context(
            viewport={"width": 1920, "height": 1080},
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            permissions=["notifications"],
            color_scheme="light",
        )
        yield context
        await context.close()

    @pytest.fixture
    async def page(self, context: BrowserContext) -> Generator[Page, None, None]:
        """Page fixture for individual tests."""
        page = await context.new_page()
        # Navigate to the application
        await page.goto("http://localhost:5173")  # Vite dev server
        yield page
        await page.close()

    async def test_complete_ai_query_workflow(self, page: Page):
        """Test complete AI query workflow from start to finish."""
        
        # 1. Wait for application to load
        await page.wait_for_selector("[data-testid='dashboard-container']", timeout=10000)
        
        # 2. Verify initial state
        assert await page.is_visible("[data-testid='search-input']")
        assert await page.is_visible("[data-testid='calendar-container']")
        
        # 3. Enter AI query
        search_input = page.locator("[data-testid='search-input']")
        await search_input.fill("Schedule a team meeting for tomorrow at 2 PM to discuss Q1 planning")
        
        # 4. Submit query with keyboard shortcut
        await search_input.press("Enter")
        
        # 5. Verify loading state appears
        await page.wait_for_selector("[data-testid='ai-loading']", timeout=5000)
        
        # 6. Wait for AI response
        await page.wait_for_selector("[data-testid='ai-response-panel']", timeout=15000)
        
        # 7. Verify response content
        response_panel = page.locator("[data-testid='ai-response-panel']")
        assert await response_panel.is_visible()
        
        # 8. Check for suggested calendar event
        suggested_event = page.locator("[data-testid='suggested-event']")
        if await suggested_event.is_visible():
            # 9. Accept the suggestion
            await page.click("[data-testid='accept-suggestion']")
            
            # 10. Verify event appears in calendar
            await page.wait_for_selector("[data-testid*='calendar-event']", timeout=5000)
            
        # 11. Verify search history is updated
        history_item = page.locator("[data-testid='search-history'] >> text=Schedule a team meeting")
        assert await history_item.is_visible()

    async def test_calendar_interaction_workflow(self, page: Page):
        """Test complete calendar interaction workflow."""
        
        # 1. Wait for calendar to load
        await page.wait_for_selector("[data-testid='calendar-container']")
        
        # 2. Switch to week view
        await page.click("text=Week")
        await page.wait_for_selector("[data-testid='calendar-week-view']")
        
        # 3. Create new event by double-clicking
        calendar_cell = page.locator("[data-testid='calendar-cell-tomorrow-14']").first()
        await calendar_cell.dblclick()
        
        # 4. Fill event creation form
        await page.wait_for_selector("[data-testid='event-creation-modal']")
        
        event_title = page.locator("[data-testid='event-title-input']")
        await event_title.fill("Manual Test Event")
        
        event_description = page.locator("[data-testid='event-description-input']")
        await event_description.fill("This is a test event created manually")
        
        # 5. Save event
        await page.click("[data-testid='save-event-button']")
        
        # 6. Verify event appears in calendar
        await page.wait_for_selector("text=Manual Test Event", timeout=5000)
        
        # 7. Edit the event
        await page.click("text=Manual Test Event")
        await page.wait_for_selector("[data-testid='event-details-modal']")
        
        await page.click("[data-testid='edit-event-button']")
        
        # 8. Update event title
        title_input = page.locator("[data-testid='event-title-input']")
        await title_input.fill("Updated Test Event")
        
        await page.click("[data-testid='save-event-button']")
        
        # 9. Verify update
        await page.wait_for_selector("text=Updated Test Event")

    async def test_settings_configuration_workflow(self, page: Page):
        """Test settings configuration and API setup workflow."""
        
        # 1. Open settings
        await page.click("[data-testid='settings-button']")
        await page.wait_for_selector("[data-testid='settings-modal']")
        
        # 2. Navigate to API settings
        await page.click("text=API Settings")
        await page.wait_for_selector("[data-testid='api-settings-panel']")
        
        # 3. Select AI provider
        provider_select = page.locator("[data-testid='provider-select']")
        await provider_select.select_option("openai")
        
        # 4. Enter API key (using test key)
        api_key_input = page.locator("[data-testid='api-key-input']")
        await api_key_input.fill("sk-test-key-for-demo-purposes")
        
        # 5. Test connection
        await page.click("[data-testid='test-connection-button']")
        
        # 6. Wait for connection result
        await page.wait_for_selector("[data-testid='connection-status']", timeout=10000)
        
        # 7. Save settings
        await page.click("[data-testid='save-settings-button']")
        
        # 8. Verify success message
        await page.wait_for_selector("text=Settings saved successfully")
        
        # 9. Close settings modal
        await page.click("[data-testid='close-settings']")

    async def test_animation_performance_verification(self, page: Page):
        """Test animation smoothness and performance."""
        
        # 1. Enable performance monitoring
        await page.evaluate("() => { window.performance.mark('animation-test-start'); }")
        
        # 2. Trigger calendar view transitions
        await page.click("text=Month")
        await page.wait_for_timeout(300)  # Wait for animation
        
        await page.click("text=Week")
        await page.wait_for_timeout(300)
        
        await page.click("text=Day")
        await page.wait_for_timeout(300)
        
        # 3. Test modal animations
        await page.click("[data-testid='settings-button']")
        await page.wait_for_selector("[data-testid='settings-modal']")
        await page.press("Escape")  # Close with animation
        await page.wait_for_timeout(300)
        
        # 4. Test AI response panel animations
        search_input = page.locator("[data-testid='search-input']")
        await search_input.fill("Test animation performance")
        await search_input.press("Enter")
        
        await page.wait_for_selector("[data-testid='ai-response-panel']", timeout=10000)
        
        # 5. Measure performance
        performance_data = await page.evaluate("""
            () => {
                window.performance.mark('animation-test-end');
                const entries = window.performance.getEntriesByType('measure');
                const paintEntries = window.performance.getEntriesByType('paint');
                
                return {
                    navigationTiming: window.performance.timing,
                    paintTiming: paintEntries,
                    measures: entries
                };
            }
        """)
        
        # 6. Verify performance metrics
        first_paint = next((entry for entry in performance_data['paintTiming'] if entry['name'] == 'first-paint'), None)
        if first_paint:
            assert first_paint['startTime'] < 2000  # First paint under 2 seconds

    async def test_responsive_design_across_viewports(self, context: BrowserContext):
        """Test responsive design across different viewport sizes."""
        
        viewports = [
            {"width": 375, "height": 667, "name": "Mobile"},
            {"width": 768, "height": 1024, "name": "Tablet"},
            {"width": 1920, "height": 1080, "name": "Desktop"},
        ]
        
        for viewport in viewports:
            page = await context.new_page()
            await page.set_viewport_size({"width": viewport["width"], "height": viewport["height"]})
            await page.goto("http://localhost:5173")
            
            # Wait for page load
            await page.wait_for_selector("[data-testid='dashboard-container']")
            
            # Test responsive navigation
            if viewport["width"] < 768:
                # Mobile: hamburger menu should be visible
                assert await page.is_visible("[data-testid='mobile-menu-button']")
                await page.click("[data-testid='mobile-menu-button']")
                await page.wait_for_selector("[data-testid='mobile-navigation']")
            else:
                # Desktop/Tablet: full navigation should be visible
                assert await page.is_visible("[data-testid='desktop-navigation']")
            
            # Test calendar responsiveness
            calendar = page.locator("[data-testid='calendar-container']")
            assert await calendar.is_visible()
            
            # Test search input accessibility
            search_input = page.locator("[data-testid='search-input']")
            await search_input.fill("Responsive test query")
            await search_input.press("Enter")
            
            await page.close()

    async def test_keyboard_navigation_accessibility(self, page: Page):
        """Test complete keyboard navigation and accessibility."""
        
        # 1. Test tab navigation
        await page.keyboard.press("Tab")  # Focus search input
        focused_element = await page.evaluate("document.activeElement.getAttribute('data-testid')")
        assert focused_element == "search-input"
        
        # 2. Test keyboard shortcuts
        await page.keyboard.press("Control+k")  # Global search shortcut
        assert await page.locator("[data-testid='search-input']").is_focused()
        
        # 3. Test escape to close modals
        await page.click("[data-testid='settings-button']")
        await page.wait_for_selector("[data-testid='settings-modal']")
        await page.keyboard.press("Escape")
        await page.wait_for_selector("[data-testid='settings-modal']", state="hidden")
        
        # 4. Test calendar keyboard navigation
        calendar_grid = page.locator("[data-testid='calendar-grid']")
        await calendar_grid.click()  # Focus calendar
        
        await page.keyboard.press("ArrowRight")  # Navigate dates
        await page.keyboard.press("Enter")  # Select date
        
        # 5. Test screen reader compatibility
        aria_labels = await page.evaluate("""
            () => {
                const elements = document.querySelectorAll('[aria-label]');
                return Array.from(elements).map(el => el.getAttribute('aria-label'));
            }
        """)
        
        assert len(aria_labels) > 0  # Should have ARIA labels

    async def test_data_persistence_across_sessions(self, context: BrowserContext):
        """Test data persistence across browser sessions."""
        
        # 1. Create first session
        page1 = await context.new_page()
        await page1.goto("http://localhost:5173")
        await page1.wait_for_selector("[data-testid='dashboard-container']")
        
        # 2. Create some data
        search_input = page1.locator("[data-testid='search-input']")
        await search_input.fill("Persistence test query")
        await search_input.press("Enter")
        
        await page1.wait_for_selector("[data-testid='ai-response-panel']", timeout=10000)
        
        # 3. Configure settings
        await page1.click("[data-testid='settings-button']")
        await page1.wait_for_selector("[data-testid='settings-modal']")
        
        theme_select = page1.locator("[data-testid='theme-select']")
        await theme_select.select_option("dark")
        
        await page1.click("[data-testid='save-settings-button']")
        await page1.click("[data-testid='close-settings']")
        
        await page1.close()
        
        # 4. Create new session (simulate browser restart)
        page2 = await context.new_page()
        await page2.goto("http://localhost:5173")
        await page2.wait_for_selector("[data-testid='dashboard-container']")
        
        # 5. Verify persistence
        # Check if search history is maintained
        history_items = page2.locator("[data-testid='search-history'] >> text=Persistence test query")
        assert await history_items.is_visible()
        
        # Check if theme setting is maintained
        body_class = await page2.evaluate("document.body.className")
        assert "dark-theme" in body_class
        
        await page2.close()

    async def test_error_handling_and_recovery(self, page: Page):
        """Test error handling and recovery mechanisms."""
        
        # 1. Test network error handling
        await page.route("**/api/**", lambda route: route.abort())
        
        search_input = page.locator("[data-testid='search-input']")
        await search_input.fill("Network error test")
        await search_input.press("Enter")
        
        # 2. Verify error message appears
        await page.wait_for_selector("[data-testid='error-message']", timeout=5000)
        error_text = await page.locator("[data-testid='error-message']").text_content()
        assert "network" in error_text.lower() or "error" in error_text.lower()
        
        # 3. Test retry mechanism
        await page.unroute("**/api/**")  # Remove network block
        await page.click("[data-testid='retry-button']")
        
        # 4. Verify recovery
        await page.wait_for_selector("[data-testid='ai-response-panel']", timeout=10000)
        
        # 5. Test invalid input handling
        await search_input.fill("")  # Empty query
        await search_input.press("Enter")
        
        # Should show validation message
        validation_message = page.locator("[data-testid='validation-message']")
        assert await validation_message.is_visible()

    async def test_cross_browser_compatibility(self):
        """Test compatibility across different browsers."""
        
        browsers_to_test = ["chromium", "firefox", "webkit"]
        
        for browser_name in browsers_to_test:
            async with async_playwright() as p:
                browser = getattr(p, browser_name)
                browser_instance = await browser.launch()
                context = await browser_instance.new_context()
                page = await context.new_page()
                
                try:
                    await page.goto("http://localhost:5173", timeout=30000)
                    await page.wait_for_selector("[data-testid='dashboard-container']", timeout=10000)
                    
                    # Test basic functionality
                    search_input = page.locator("[data-testid='search-input']")
                    await search_input.fill(f"Cross-browser test in {browser_name}")
                    await search_input.press("Enter")
                    
                    # Verify response (with longer timeout for different browsers)
                    await page.wait_for_selector("[data-testid='ai-response-panel']", timeout=20000)
                    
                    # Test calendar interaction
                    await page.click("text=Week")
                    await page.wait_for_selector("[data-testid='calendar-week-view']")
                    
                except Exception as e:
                    pytest.fail(f"Browser {browser_name} failed: {str(e)}")
                
                finally:
                    await context.close()
                    await browser_instance.close()

    async def test_performance_under_load(self, page: Page):
        """Test application performance under simulated load."""
        
        # 1. Measure initial load time
        start_time = time.time()
        await page.goto("http://localhost:5173")
        await page.wait_for_selector("[data-testid='dashboard-container']")
        load_time = time.time() - start_time
        
        assert load_time < 5.0  # Should load within 5 seconds
        
        # 2. Test rapid interactions
        search_input = page.locator("[data-testid='search-input']")
        
        for i in range(10):
            await search_input.fill(f"Performance test query {i}")
            await search_input.press("Enter")
            await page.wait_for_selector("[data-testid='ai-loading']", timeout=2000)
            
            # Don't wait for full response, test rapid firing
            if i < 9:  # Last one we'll wait for
                await page.wait_for_timeout(100)
        
        # 3. Wait for final response
        await page.wait_for_selector("[data-testid='ai-response-panel']", timeout=15000)
        
        # 4. Test calendar performance with many events
        # Simulate loading many events
        await page.evaluate("""
            () => {
                const events = [];
                for (let i = 0; i < 100; i++) {
                    events.push({
                        id: i,
                        title: `Performance Test Event ${i}`,
                        start: new Date(2024, 0, 1 + (i % 30), 10 + (i % 8)),
                        end: new Date(2024, 0, 1 + (i % 30), 11 + (i % 8))
                    });
                }
                window.testEvents = events;
            }
        """)
        
        # Calendar should still be responsive
        await page.click("text=Month")
        await page.wait_for_timeout(1000)  # Allow for rendering
        
        # Should still be able to interact
        calendar_cell = page.locator("[data-testid^='calendar-cell']").first()
        await calendar_cell.click()

# Configuration for different environments
@pytest.mark.parametrize("environment", ["development", "staging", "production"])
async def test_environment_specific_behavior(environment: str):
    """Test behavior in different deployment environments."""
    
    base_urls = {
        "development": "http://localhost:5173",
        "staging": "https://staging.onesearch.app",
        "production": "https://onesearch.app"
    }
    
    if environment == "development":
        # Only run dev tests locally
        async with async_playwright() as p:
            browser = await p.chromium.launch()
            page = await browser.new_page()
            
            await page.goto(base_urls[environment])
            await page.wait_for_selector("[data-testid='dashboard-container']")
            
            # Development should have debug tools
            debug_panel = page.locator("[data-testid='debug-panel']")
            assert await debug_panel.is_visible()
            
            await browser.close()
    
    # Add staging and production tests as needed
