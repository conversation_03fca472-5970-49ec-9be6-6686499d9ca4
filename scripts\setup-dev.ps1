# AI-Powered Dashboard Development Setup Script (PowerShell)
# Run this script to set up the complete development environment on Windows

param(
    [switch]$SkipChecks,
    [switch]$Quiet
)

# Color functions
function Write-Success {
    param([string]$Message)
    Write-Host "✅ $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "⚠️  $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "❌ $Message" -ForegroundColor Red
}

function Write-Info {
    param([string]$Message)
    Write-Host "ℹ️  $Message" -ForegroundColor Blue
}

# Check prerequisites
function Test-Prerequisites {
    Write-Info "Checking prerequisites..."
    
    # Check Docker
    if (Get-Command docker -ErrorAction SilentlyContinue) {
        Write-Success "Docker is installed"
    }
    else {
        Write-Error "Docker is not installed. Please install Docker Desktop first."
        Write-Info "Download from: https://www.docker.com/products/docker-desktop"
        exit 1
    }
    
    # Check Docker Compose
    if (Get-Command docker-compose -ErrorAction SilentlyContinue) {
        Write-Success "Docker Compose is available"
    }
    else {
        Write-Error "Docker Compose is not available. Please ensure Docker Desktop is properly installed."
        exit 1
    }
    
    # Check Node.js
    if (Get-Command node -ErrorAction SilentlyContinue) {
        $nodeVersion = node --version
        Write-Success "Node.js is installed: $nodeVersion"
    }
    else {
        Write-Warning "Node.js is not installed. Using Docker containers only."
    }
    
    # Check Python
    if (Get-Command python -ErrorAction SilentlyContinue) {
        $pythonVersion = python --version
        Write-Success "Python is installed: $pythonVersion"
    }
    elseif (Get-Command python3 -ErrorAction SilentlyContinue) {
        $pythonVersion = python3 --version
        Write-Success "Python is installed: $pythonVersion"
    }
    else {
        Write-Warning "Python is not installed. Using Docker containers only."
    }
}

# Setup environment file
function Set-Environment {
    Write-Info "Setting up environment configuration..."
    
    if (-not (Test-Path ".env")) {
        if (Test-Path ".env.example") {
            Copy-Item ".env.example" ".env"
            Write-Success "Created .env from .env.example"
            Write-Warning "Please edit .env file with your API keys before continuing"
            
            # Check if API keys are set
            $envContent = Get-Content ".env" -Raw
            if ($envContent -match "your-openrouter-key-here") {
                Write-Error "Please set your OPENROUTER_API_KEY in .env file"
                Write-Info "Get your free API key from: https://openrouter.ai/"
                exit 1
            }
            
            if ($envContent -match "your-langsearch-key-here") {
                Write-Error "Please set your LANGSEARCH_API_KEY in .env file"
                Write-Info "Get your free API key from: https://langsearch.ai/"
                exit 1
            }
        }
        else {
            Write-Error ".env.example file not found"
            exit 1
        }
    }
    else {
        Write-Success ".env file already exists"
    }
}

# Create data directory
function New-DataDirectory {
    Write-Info "Setting up data directory..."
    
    if (-not (Test-Path "data")) {
        New-Item -ItemType Directory -Path "data" | Out-Null
        Write-Success "Created data directory"
    }
    else {
        Write-Success "Data directory already exists"
    }
}

# Install dependencies
function Install-Dependencies {
    Write-Info "Installing dependencies..."
    
    # Frontend dependencies
    if ((Test-Path "frontend") -and (Get-Command npm -ErrorAction SilentlyContinue)) {
        Write-Info "Installing frontend dependencies..."
        Set-Location "frontend"
        npm install
        Set-Location ".."
        Write-Success "Frontend dependencies installed"
    }
    
    # Backend dependencies
    if ((Test-Path "backend") -and ((Get-Command python -ErrorAction SilentlyContinue) -or (Get-Command python3 -ErrorAction SilentlyContinue))) {
        Write-Info "Installing backend dependencies..."
        Set-Location "backend"
        
        # Create virtual environment
        if (Get-Command python -ErrorAction SilentlyContinue) {
            python -m venv venv
        }
        else {
            python3 -m venv venv
        }
        
        # Activate virtual environment and install dependencies
        .\venv\Scripts\Activate.ps1
        pip install -r requirements.txt
        deactivate
        
        Set-Location ".."
        Write-Success "Backend dependencies installed"
    }
}

# Setup Ollama
function Set-Ollama {
    Write-Info "Setting up Ollama for embeddings..."
    
    # Check if Ollama is running
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:11434/api/tags" -TimeoutSec 5 -ErrorAction Stop
        Write-Success "Ollama is already running"
    }
    catch {
        Write-Info "Starting Ollama with Docker..."
        docker run -d --name ollama -p 11434:11434 -v ollama_data:/root/.ollama ollama/ollama:latest
        
        # Wait for Ollama to start
        Write-Info "Waiting for Ollama to start..."
        $attempts = 30
        $ollamaReady = $false
        
        for ($i = 1; $i -le $attempts; $i++) {
            try {
                Invoke-WebRequest -Uri "http://localhost:11434/api/tags" -TimeoutSec 2 -ErrorAction Stop | Out-Null
                $ollamaReady = $true
                break
            }
            catch {
                Start-Sleep -Seconds 1
            }
        }
        
        if ($ollamaReady) {
            Write-Success "Ollama is running"
        }
        else {
            Write-Error "Timeout waiting for Ollama to start"
            exit 1
        }
    }
    
    # Pull required models
    Write-Info "Pulling required embedding model..."
    docker exec ollama ollama pull nomic-embed-text
    Write-Success "Embedding model ready"
}

# Build Docker containers
function Build-Containers {
    Write-Info "Building Docker containers..."
    
    docker-compose build
    Write-Success "Docker containers built successfully"
}

# Run health checks
function Test-Health {
    Write-Info "Running health checks..."
    
    # Start services
    docker-compose up -d
    
    # Wait for services to be ready
    Write-Info "Waiting for services to start..."
    Start-Sleep -Seconds 10
    
    # Check backend health
    try {
        Invoke-WebRequest -Uri "http://localhost:8000/health" -TimeoutSec 10 -ErrorAction Stop | Out-Null
        Write-Success "Backend is healthy"
    }
    catch {
        Write-Error "Backend health check failed"
        docker-compose logs backend
        exit 1
    }
    
    # Check frontend
    try {
        Invoke-WebRequest -Uri "http://localhost:3000" -TimeoutSec 10 -ErrorAction Stop | Out-Null
        Write-Success "Frontend is accessible"
    }
    catch {
        Write-Error "Frontend is not accessible"
        docker-compose logs frontend
        exit 1
    }
    
    # Check Ollama
    try {
        Invoke-WebRequest -Uri "http://localhost:11434/api/tags" -TimeoutSec 5 -ErrorAction Stop | Out-Null
        Write-Success "Ollama is healthy"
    }
    catch {
        Write-Error "Ollama health check failed"
        exit 1
    }
}

# Create development scripts
function New-DevScripts {
    Write-Info "Creating development scripts..."
    
    # Create scripts directory
    if (-not (Test-Path "scripts")) {
        New-Item -ItemType Directory -Path "scripts" | Out-Null
    }
    
    # Backend dev script (PowerShell)
    @'
# Backend Development Script
Set-Location backend
.\venv\Scripts\Activate.ps1
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
'@ | Out-File -FilePath "scripts\dev-backend.ps1" -Encoding UTF8
    
    # Frontend dev script (PowerShell)
    @'
# Frontend Development Script
Set-Location frontend
npm run dev
'@ | Out-File -FilePath "scripts\dev-frontend.ps1" -Encoding UTF8
    
    # Full dev environment script (PowerShell)
    @'
# Full Development Environment Script
Write-Host "🚀 Starting full development environment..." -ForegroundColor Blue
docker-compose up -d
Write-Host "✅ Development environment is running!" -ForegroundColor Green
Write-Host "📱 Frontend: http://localhost:3000" -ForegroundColor Cyan
Write-Host "🔧 Backend: http://localhost:8000" -ForegroundColor Cyan
Write-Host "🔍 API Docs: http://localhost:8000/docs" -ForegroundColor Cyan
Write-Host "🧠 Ollama: http://localhost:11434" -ForegroundColor Cyan
'@ | Out-File -FilePath "scripts\dev-full.ps1" -Encoding UTF8
    
    Write-Success "Development scripts created in scripts\ directory"
}

# Main setup function
function Main {
    Write-Host "=======================================" -ForegroundColor Magenta
    Write-Host "🚀 AI-Powered Dashboard Dev Setup" -ForegroundColor Magenta
    Write-Host "=======================================" -ForegroundColor Magenta
    
    if (-not $SkipChecks) {
        Test-Prerequisites
    }
    
    Set-Environment
    New-DataDirectory
    Install-Dependencies
    Set-Ollama
    Build-Containers
    New-DevScripts
    Test-Health
    
    Write-Host ""
    Write-Host "=======================================" -ForegroundColor Green
    Write-Host "✅ Development Environment Setup Complete!" -ForegroundColor Green
    Write-Host "=======================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "🎯 Quick Start Commands:" -ForegroundColor Yellow
    Write-Host "  Full Environment:  .\scripts\dev-full.ps1" -ForegroundColor Cyan
    Write-Host "  Backend Only:      .\scripts\dev-backend.ps1" -ForegroundColor Cyan
    Write-Host "  Frontend Only:     .\scripts\dev-frontend.ps1" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "🌐 Access URLs:" -ForegroundColor Yellow
    Write-Host "  Dashboard:         http://localhost:3000" -ForegroundColor Cyan
    Write-Host "  Backend API:       http://localhost:8000" -ForegroundColor Cyan
    Write-Host "  API Documentation: http://localhost:8000/docs" -ForegroundColor Cyan
    Write-Host "  Ollama API:        http://localhost:11434" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "📚 Next Steps:" -ForegroundColor Yellow
    Write-Host "  1. Open http://localhost:3000 in your browser" -ForegroundColor White
    Write-Host "  2. Type anything in the input bar to test AI categorization" -ForegroundColor White
    Write-Host "  3. Check logs with: docker-compose logs -f" -ForegroundColor White
    Write-Host "  4. Run tests with: npm test (frontend) or pytest (backend)" -ForegroundColor White
    Write-Host ""
    Write-Success "Happy developing! 🎉"
}

# Run main function
Main
