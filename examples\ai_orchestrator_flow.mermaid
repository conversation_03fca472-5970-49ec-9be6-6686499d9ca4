graph TD
    A[User Input in Hero Bar] --> B[🎭 'Analyzing input...'<br/>⚡ TYPING INDICATOR]
    B --> C[🤖 'Categorizing...'<br/>⚡ BRAIN ANIMATION]
    
    C --> D{🎯 Category Decision<br/>⚡ SELECTION HIGHLIGHT}
    
    %% Task Flow with Visual Transparency
    D -->|TASK| E[📝 'Task identified!'<br/>⚡ GREEN PULSE]
    E --> F[⚙️ 'Extracting details...'<br/>⚡ SPINNER ANIMATION]
    F --> G[🏷️ 'Auto-categorizing...'<br/>⚡ TAG ANIMATIONS]
    G --> H[📅 'Checking for dates...'<br/>⚡ CALENDAR SCAN]
    H --> I{⏰ Has Date?<br/>⚡ DATE HIGHLIGHT}
    I -->|No| J[📌 'Adding to today...'<br/>⚡ DROP ANIMATION]
    I -->|Yes| K[🗓️ 'Scheduling...'<br/>⚡ SLIDE TO DATE]
    J --> L[📋 'Updating task list...'<br/>⚡ LIST GROW ANIMATION]
    K --> L
    L --> M[📅 'Syncing calendar...'<br/>⚡ SYNC SPINNER]
    M --> N[✅ 'Task added successfully!'<br/>⚡ SUCCESS CONFETTI]
    
    %% Event/Reminder Flow with Visual Transparency
    D -->|EVENT/REMINDER| O[📋 'Event identified!'<br/>⚡ BLUE PULSE]
    O --> P[🕐 'Extracting date/time...'<br/>⚡ CLOCK ANIMATION]
    P --> Q{📆 Complete DateTime?<br/>⚡ INFO CHECK}
    Q -->|No| R[❓ 'Need more info...'<br/>⚡ INPUT PROMPT SLIDE]
    Q -->|Yes| S[📅 'Adding to calendar...'<br/>⚡ CALENDAR DROP]
    R --> T[⏳ 'Waiting for response...'<br/>⚡ BREATHING ANIMATION]
    T --> S
    S --> U[🔄 'Syncing calendar...'<br/>⚡ SYNC ANIMATION]
    U --> V[✅ 'Event scheduled!'<br/>⚡ SUCCESS SPARKLE]
    
    %% AI Question Flow with Visual Transparency
    D -->|AI QUESTION| W[❓ 'Question identified!'<br/>⚡ PURPLE PULSE]
    W --> X[🧠 'Analyzing query type...'<br/>⚡ THINKING DOTS]
    X --> Y{🎯 Query Type Decision<br/>⚡ TOOL HIGHLIGHT}
    
    %% Simple Knowledge Query
    Y -->|Simple Knowledge| Z[🤖 'Accessing knowledge...'<br/>⚡ BRAIN GLOW]
    Z --> AA[📝 'Formatting answer...'<br/>⚡ TEXT TYPEWRITER]
    AA --> BB[✅ 'Answer ready!'<br/>⚡ REVEAL ANIMATION]
    
    %% Database Search
    Y -->|Database Search| CC[🔍 'Searching database...'<br/>⚡ RADAR SCAN]
    CC --> DD[🧮 'Generating embeddings...'<br/>⚡ MATRIX ANIMATION]
    DD --> EE[📊 'Vector searching...'<br/>⚡ PROGRESS DOTS]
    EE --> FF[📄 'Found relevant docs...'<br/>⚡ DOCUMENT FLY-IN]
    FF --> GG[🤖 'Processing context...'<br/>⚡ LOADING SPINNER]
    GG --> HH[📝 'Generating answer...'<br/>⚡ TYPEWRITER EFFECT]
    HH --> II[✅ 'Database search complete!'<br/>⚡ SUCCESS FADE-IN]
    
    %% Web Search
    Y -->|Web Search| JJ[🌐 'Starting web search...'<br/>⚡ GLOBE SPIN]
    JJ --> KK[🔗 'Calling LangSearch API...'<br/>⚡ API PULSE]
    KK --> LL[⚙️ 'Processing results...'<br/>⚡ GEAR ANIMATION]
    LL --> MM[🤖 'Summarizing findings...'<br/>⚡ TEXT PROCESSING]
    MM --> NN[🔗 'Adding sources...'<br/>⚡ LINK ANIMATIONS]
    NN --> OO[✅ 'Web search complete!'<br/>⚡ SUCCESS BURST]
    
    %% Final Display with Smooth Transitions
    N --> PP[🎭 Dashboard Update<br/>⚡ SMOOTH TRANSITIONS]
    V --> PP
    BB --> PP
    II --> PP
    OO --> PP
    PP --> QQ[🎬 Real-time UI Animation<br/>⚡ FRAMER MOTION]
    QQ --> RR[👀 User Sees Result<br/>⚡ FINAL REVEAL]
    
    %% Tool Availability with Visual States
    subgraph Tools[🛠️ AVAILABLE LLM TOOLS + VISUAL STATES]
        TT["📅 Calendar Tool<br/>⚡ CALENDAR ANIMATIONS<br/>- Read events → 📖 Scanning...<br/>- Add events → ➕ Dropping in...<br/>- Check availability → 🔍 Checking..."]
        UU["📋 Tasks Tool<br/>⚡ LIST ANIMATIONS<br/>- Read task list → 📄 Loading...<br/>- Add tasks → ✨ Adding...<br/>- Update categories → 🏷️ Categorizing..."]
        VV["🔍 Database Search Tool<br/>⚡ SEARCH ANIMATIONS<br/>- Semantic search → 🎯 Searching...<br/>- Vector similarity → 🧮 Calculating...<br/>- Context retrieval → 📄 Retrieving..."]
        WW["🌐 Web Search Tool<br/>⚡ WEB ANIMATIONS<br/>- LangSearch API → 🌍 Searching web...<br/>- Multi-site search → 🔗 Crawling sites...<br/>- Source attribution → 📝 Citing sources..."]
    end
    
    %% Visual Feedback States with Transparency
    subgraph Feedback[🎬 VISUAL TRANSPARENCY SYSTEM]
        XX["🔄 Processing Animation<br/>⚡ SMOOTH FADE-INS"]
        YY["🤖 'Categorizing...'<br/>⚡ TYPING ANIMATION"]
        ZZ["🧠 'Task identified...'<br/>⚡ CATEGORY HIGHLIGHT"]
        AAA["⚙️ 'Extracting details...'<br/>⚡ PULSE ANIMATION"]
        BBB["✅ 'Task list updated!'<br/>⚡ SUCCESS CONFETTI"]
        CCC["❌ Error with Retry<br/>⚡ SHAKE ANIMATION"]
        
        DDD["🔍 'Searching database...'<br/>⚡ LOADING DOTS"]
        EEE["🌐 'Searching web...'<br/>⚡ PROGRESS BAR"]
        FFF["📅 'Adding to calendar...'<br/>⚡ SLIDE ANIMATION"]
        GGG["📝 'Processing reminder...'<br/>⚡ GLOW EFFECT"]
    end
    
    %% Database Components
    subgraph Database[Local Data Layer]
        DDD[(SQLite<br/>Tasks & Events)]
        EEE[(Vector DB<br/>Embeddings)]
        FFF[Ollama<br/>Local Embeddings]
    end
    
    %% Real-time Updates with Animation Details
    subgraph Realtime[⚡ REAL-TIME ANIMATION SYSTEM]
        GGG["🔌 WebSocket Connection<br/>⚡ PULSE INDICATOR"]
        HHH["⚛️ React State Updates<br/>⚡ STATE TRANSITIONS"]
        III["🎬 Framer Motion Animations<br/>⚡ SMOOTH 60FPS ANIMATIONS<br/>- Spring physics<br/>- Stagger effects<br/>- Page transitions<br/>- Micro-interactions"]
        JJJ["🎭 Animation Orchestration<br/>⚡ SEQUENCE TIMING<br/>- Enter animations<br/>- Exit animations<br/>- Loading states<br/>- Success celebrations"]
    end
    
    %% Styling
    classDef task fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#e2e8f0
    classDef event fill:#2b341f,stroke:#38a169,stroke-width:2px,color:#e2e8f0
    classDef question fill:#1a202c,stroke:#63b3ed,stroke-width:2px,color:#e2e8f0
    classDef tool fill:#2d2d2d,stroke:#a0aec0,stroke-width:2px,color:#f7fafc
    classDef feedback fill:#1a1a1a,stroke:#f6ad55,stroke-width:2px,color:#fffaf0
    classDef data fill:#2c1810,stroke:#d69e2e,stroke-width:2px,color:#faf089
    
    class E,F,G,H,I,J,K,L,M,N task
    class O,P,Q,R,S,T,U,V event
    class W,X,Y,Z,AA,BB,CC,DD,EE,FF,GG,HH,II,JJ,KK,LL,MM,NN,OO question
    class TT,UU,VV,WW tool
    class XX,YY,ZZ,AAA,BBB,CCC feedback
    class DDD,EEE,FFF data