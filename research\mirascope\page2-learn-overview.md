Title: Learn Mirascope | Mirascope

URL Source: https://mirascope.com/docs/mirascope/learn

Markdown Content:
This section is designed to help you master Mirascope, a toolkit for building AI-powered applications with Large Language Models (LLMs).

Our documentation is tailored for developers who have at least some experience with Python and LLMs. Whether you're coming from other development tool libraries or have worked directly with provider SDKs and APIs, Mirascope offers a familiar but enhanced experience.

If you haven't already, we recommend checking out [Getting Started](https://mirascope.com/docs/mirascope/guides/getting-started/quickstart) and [Why Use Mirascope](https://mirascope.com/docs/mirascope/getting-started/why).

Key Features and Benefits[](https://mirascope.com/docs/mirascope/learn#key-features-and-benefits)
-------------------------------------------------------------------------------------------------

### Pythonic By Design

Our design approach is to remain Pythonic so you can build your way

### Editor Support & Type Hints

Rich autocomplete, inline documentation, and type hints to catch errors before runtime

### Provider-Agnostic & Provider-Specific

Seamlessly engineer prompts agnostic or specific to various LLM providers

### Comprehensive Tooling

Complete suite of tools for every aspect of working with LLM provider APIs

Core Components[](https://mirascope.com/docs/mirascope/learn#core-components)
-----------------------------------------------------------------------------

Mirascope is built around these core components, each designed to handle specific aspects of working with LLM provider APIs.

We encourage you to dive into each component's documentation to gain a deeper understanding of Mirascope's capabilities. Start with the topics that align most closely with your immediate needs, but don't hesitate to explore all areas – you might discover new ways to enhance your LLM applications!

![Image 1: Learn Flow Chart](https://mirascope.com/assets/learn_flow.svg)

### Prompts

Learn how to create and manage prompts effectively

[Read more →](https://mirascope.com/docs/mirascope/learn/prompts)

### Calls

Understand how to make calls to LLMs using Mirascope

[Read more →](https://mirascope.com/docs/mirascope/learn/calls)

### Streams

Explore streaming responses for real-time applications

[Read more →](https://mirascope.com/docs/mirascope/learn/streams)

### Chaining

Understand the art of chaining multiple LLM calls for complex tasks

[Read more →](https://mirascope.com/docs/mirascope/learn/chaining)

### Response Models

Define and use structured output models with automatic validation

[Read more →](https://mirascope.com/docs/mirascope/learn/response_models)

### JSON Mode

Work with structured JSON data responses from LLMs

[Read more →](https://mirascope.com/docs/mirascope/learn/json_mode)

### Output Parsers

Process and transform custom LLM output structures effectively

[Read more →](https://mirascope.com/docs/mirascope/learn/output_parsers)

### Tools

Discover how to extend LLM capabilities with custom tools

[Read more →](https://mirascope.com/docs/mirascope/learn/tools)

### Agents

Put everything together to build advanced AI agents using Mirascope

[Read more →](https://mirascope.com/docs/mirascope/learn/agents)

### Evals

Apply core components to build evaluation strategies for your LLM applications

[Read more →](https://mirascope.com/docs/mirascope/learn/evals)

### Async

Maximize efficiency with asynchronous programming

[Read more →](https://mirascope.com/docs/mirascope/learn/async)

### Retries

Understand how to automatically retry failed API calls

[Read more →](https://mirascope.com/docs/mirascope/learn/retries)

### Local Models

Learn how to use Mirascope with locally deployed LLMs

[Read more →](https://mirascope.com/docs/mirascope/learn/local_models)

As you progress, you'll find advanced topics and best practices throughout the documentation. These will help you optimize your use of Mirascope and build increasingly sophisticated AI-powered applications.

Happy learning, and welcome to the world of development with Mirascope!
